#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学校社团管理系统Flask应用初始化
支持三类用户：管理员、会长、会员
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_wtf.csrf import CSRFProtect, generate_csrf
import pytz

# 初始化扩展
db = SQLAlchemy()
migrate = Migrate()
csrf = CSRFProtect()

def create_app():
    """
    Flask应用工厂函数
    创建并配置Flask应用实例
    """
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object('app.config.Config')
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    csrf.init_app(app)

    # 注册CSRF令牌到模板上下文
    @app.context_processor
    def inject_csrf_token():
        return dict(csrf_token=generate_csrf)
    
    # 设置中国标准时间为默认时区
    app.config['TIMEZONE'] = pytz.timezone('Asia/Shanghai')
    
    # 注册蓝图
    from app.views.auth import auth_bp
    from app.views.member import member_bp
    from app.views.president import president_bp
    from app.views.admin import admin_bp
    from app.views.common import common_bp
    
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(member_bp, url_prefix='/member')
    app.register_blueprint(president_bp, url_prefix='/president')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(common_bp, url_prefix='/')
    
    # 导入数据模型（确保数据库表能被正确创建）
    from app.models import member, club, venue, activity, member_club, approval
    
    return app
