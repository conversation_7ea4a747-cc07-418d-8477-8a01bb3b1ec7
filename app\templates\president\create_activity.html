{% extends "admin_base.html" %}

{% block title %}创建活动 - 会长后台{% endblock %}

{% block breadcrumb_title %}创建活动{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">创建活动</h2>
            <p class="text-muted mb-0">为 <span class="badge bg-primary">{{ club.ClubName }}</span> 创建新活动</p>
        </div>
        <div>
            <a href="{{ url_for('president.activities') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回活动管理
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2 text-success"></i>活动信息
                    </h5>
                </div>
                <div class="card-body">
                    <form id="createActivityForm" method="POST" action="{{ url_for('president.create_activity', club_id=club.ClubID) }}">
                        {{ csrf_token() }}
                        
                        <!-- 基本信息 -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="activityName" class="form-label">活动名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="activityName" name="activity_name" 
                                       placeholder="请输入活动名称" required>
                            </div>
                            <div class="col-md-4">
                                <label for="activityType" class="form-label">活动类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="activityType" name="activity_type" required>
                                    <option value="">请选择类型</option>
                                    <option value="学术讲座">学术讲座</option>
                                    <option value="文艺演出">文艺演出</option>
                                    <option value="体育竞赛">体育竞赛</option>
                                    <option value="社会实践">社会实践</option>
                                    <option value="志愿服务">志愿服务</option>
                                    <option value="技能培训">技能培训</option>
                                    <option value="交流座谈">交流座谈</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>

                        <!-- 时间安排 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="startTime" class="form-label">开始时间 <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="startTime" name="start_time" required>
                            </div>
                            <div class="col-md-6">
                                <label for="endTime" class="form-label">结束时间 <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="endTime" name="end_time" required>
                            </div>
                        </div>

                        <!-- 地点和人数 -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="venue" class="form-label">活动地点 <span class="text-danger">*</span></label>
                                <select class="form-select" id="venue" name="venue_id" required>
                                    <option value="">请选择场馆</option>
                                    {% for venue in venues %}
                                    <option value="{{ venue.VenueID }}" data-capacity="{{ venue.Capacity }}">
                                        {{ venue.VenueName }} (容量: {{ venue.Capacity }}人)
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="participantLimit" class="form-label">人数限制</label>
                                <input type="number" class="form-control" id="participantLimit" name="participant_limit" 
                                       placeholder="不限制请留空" min="1">
                                <small class="text-muted">留空表示不限制人数</small>
                            </div>
                        </div>

                        <!-- 活动描述 -->
                        <div class="mb-3">
                            <label for="description" class="form-label">活动描述</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="请详细描述活动内容、目的、流程等..."></textarea>
                        </div>

                        <!-- 参与要求 -->
                        <div class="mb-3">
                            <label for="requirements" class="form-label">参与要求</label>
                            <textarea class="form-control" id="requirements" name="requirements" rows="3" 
                                      placeholder="请说明参与活动的要求、条件、注意事项等..."></textarea>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                                <i class="fas fa-times me-1"></i>取消
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-1"></i>创建活动
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 帮助信息 -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2 text-warning"></i>创建提示
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-1"></i>填写建议</h6>
                        <ul class="mb-0 small">
                            <li>活动名称要简洁明了，突出主题</li>
                            <li>选择合适的活动类型便于分类管理</li>
                            <li>确保时间安排合理，避免冲突</li>
                            <li>人数限制要考虑场馆容量</li>
                            <li>详细的描述有助于吸引参与者</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-1"></i>注意事项</h6>
                        <ul class="mb-0 small">
                            <li>创建后可以编辑，但开始后不可修改</li>
                            <li>场馆预约需要提前确认可用性</li>
                            <li>活动时间不能与其他活动冲突</li>
                            <li>人数限制一旦设定建议不要随意更改</li>
                        </ul>
                    </div>

                    <div class="mt-3">
                        <h6>社团信息</h6>
                        <div class="border rounded p-2 bg-light">
                            <div><strong>{{ club.ClubName }}</strong></div>
                            <small class="text-muted">{{ club.Description or '暂无描述' }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-control:focus,
.form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.alert {
    border: none;
    border-radius: 8px;
}

.card {
    border-radius: 12px;
}

.btn {
    border-radius: 6px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 设置最小时间为当前时间
    const now = new Date();
    const minDateTime = now.toISOString().slice(0, 16);
    $('#startTime, #endTime').attr('min', minDateTime);
    
    // 开始时间变化时，更新结束时间的最小值
    $('#startTime').change(function() {
        const startTime = $(this).val();
        if (startTime) {
            $('#endTime').attr('min', startTime);
            
            // 如果结束时间早于开始时间，清空结束时间
            const endTime = $('#endTime').val();
            if (endTime && endTime <= startTime) {
                $('#endTime').val('');
            }
        }
    });
    
    // 场馆选择变化时，更新人数限制提示
    $('#venue').change(function() {
        const selectedOption = $(this).find('option:selected');
        const capacity = selectedOption.data('capacity');
        
        if (capacity) {
            $('#participantLimit').attr('max', capacity);
            $('#participantLimit').attr('placeholder', `建议不超过${capacity}人`);
        } else {
            $('#participantLimit').removeAttr('max');
            $('#participantLimit').attr('placeholder', '不限制请留空');
        }
    });
    
    // 表单验证
    $('#createActivityForm').submit(function(e) {
        const startTime = new Date($('#startTime').val());
        const endTime = new Date($('#endTime').val());
        
        if (endTime <= startTime) {
            e.preventDefault();
            alert('结束时间必须晚于开始时间');
            return false;
        }
        
        const participantLimit = parseInt($('#participantLimit').val());
        const venueCapacity = parseInt($('#venue').find('option:selected').data('capacity'));
        
        if (participantLimit && venueCapacity && participantLimit > venueCapacity) {
            e.preventDefault();
            alert(`人数限制不能超过场馆容量(${venueCapacity}人)`);
            return false;
        }
        
        return true;
    });
});
</script>
{% endblock %}
