{% extends "admin_base.html" %}

{% block title %}创建活动 - 会长后台{% endblock %}

{% block breadcrumb_title %}创建活动{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">创建活动</h2>
            <p class="text-muted mb-0">为 <span class="badge bg-primary">{{ club.ClubName }}</span> 创建新活动</p>
        </div>
        <div>
            <a href="{{ url_for('president.activities') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回活动管理
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2 text-success"></i>活动信息
                    </h5>
                </div>
                <div class="card-body">
                    <form id="createActivityForm" method="POST" action="{{ url_for('president.create_activity', club_id=club.ClubID) }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <!-- 基本信息 -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="activityName" class="form-label">活动名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="activityName" name="activity_name" 
                                       placeholder="请输入活动名称" required>
                            </div>
                            <div class="col-md-4">
                                <label for="activityType" class="form-label">活动类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="activityType" name="activity_type" required>
                                    <option value="">请选择类型</option>
                                    <option value="讲座">讲座</option>
                                    <option value="比赛">比赛</option>
                                    <option value="培训">培训</option>
                                    <option value="展览">展览</option>
                                    <option value="演出">演出</option>
                                    <option value="会议">会议</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>

                        <!-- 时间安排 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="startTime" class="form-label">开始时间 <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="startTime" name="start_time" required>
                            </div>
                            <div class="col-md-6">
                                <label for="endTime" class="form-label">结束时间 <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="endTime" name="end_time" required>
                            </div>
                        </div>

                        <!-- 地点和人数 -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="venue" class="form-label">活动地点 <span class="text-danger">*</span></label>
                                <select class="form-select" id="venue" name="venue_id" required>
                                    <option value="">请选择场馆</option>
                                    {% for venue in venues %}
                                    <option value="{{ venue.VenueID }}" data-capacity="{{ venue.Capacity }}">
                                        {{ venue.VenueName }} (容量: {{ venue.Capacity }}人)
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="participantLimit" class="form-label">人数限制</label>
                                <input type="number" class="form-control" id="participantLimit" name="participant_limit" 
                                       placeholder="不限制请留空" min="1">
                                <small class="text-muted">留空表示不限制人数</small>
                            </div>
                        </div>

                        <!-- 活动描述 -->
                        <div class="mb-3">
                            <label for="description" class="form-label">活动描述</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="请详细描述活动内容、目的、流程等..."></textarea>
                        </div>

                        <!-- 注意：数据库中Activities表没有Requirements字段，移除参与要求输入框 -->

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                                <i class="fas fa-times me-1"></i>取消
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-1"></i>创建活动
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 帮助信息 -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2 text-warning"></i>创建提示
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-1"></i>填写建议</h6>
                        <ul class="mb-0 small">
                            <li>活动名称要简洁明了，突出主题</li>
                            <li>选择合适的活动类型便于分类管理</li>
                            <li>系统已自动设置当前时间为开始时间</li>
                            <li>结束时间默认为开始时间+3小时，可自由调整</li>
                            <li>人数限制要考虑场馆容量</li>
                            <li>详细的描述有助于吸引参与者</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-1"></i>注意事项</h6>
                        <ul class="mb-0 small">
                            <li>活动申请需要管理员审批后才能正式开始</li>
                            <li>修改开始时间时，结束时间会自动调整</li>
                            <li>活动时长建议至少30分钟</li>
                            <li>场馆预约需要提前确认可用性</li>
                            <li>人数限制一旦设定建议不要随意更改</li>
                        </ul>
                    </div>

                    <div class="mt-3">
                        <h6>社团信息</h6>
                        <div class="border rounded p-2 bg-light">
                            <div><strong>{{ club.ClubName }}</strong></div>
                            <small class="text-muted">{{ club.Description or '暂无描述' }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-control:focus,
.form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.alert {
    border: none;
    border-radius: 8px;
}

.card {
    border-radius: 12px;
}

.btn {
    border-radius: 6px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 获取当前中国标准时间 (UTC+8)
    function getCurrentChinaTime() {
        const now = new Date();
        // 获取UTC时间并加8小时转换为中国时间
        const chinaTime = new Date(now.getTime() + (8 * 60 * 60 * 1000) - (now.getTimezoneOffset() * 60 * 1000));
        return chinaTime;
    }

    // 格式化时间为datetime-local格式 (YYYY-MM-DDTHH:MM)
    function formatDateTimeLocal(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // 添加小时到指定时间
    function addHours(date, hours) {
        const newDate = new Date(date);
        newDate.setHours(newDate.getHours() + hours);
        return newDate;
    }

    // 初始化默认时间
    const currentTime = getCurrentChinaTime();
    const defaultStartTime = formatDateTimeLocal(currentTime);
    const defaultEndTime = formatDateTimeLocal(addHours(currentTime, 3));

    // 设置默认值
    $('#startTime').val(defaultStartTime);
    $('#endTime').val(defaultEndTime);

    // 设置最小时间为当前时间
    $('#startTime, #endTime').attr('min', defaultStartTime);

    // 标记用户是否手动修改过结束时间
    let endTimeManuallyChanged = false;

    // 监听结束时间的手动修改
    $('#endTime').on('input change', function() {
        endTimeManuallyChanged = true;
    });

    // 开始时间变化时的智能联动
    $('#startTime').change(function() {
        const startTime = $(this).val();
        if (startTime) {
            // 更新结束时间的最小值
            $('#endTime').attr('min', startTime);

            // 如果用户没有手动修改过结束时间，自动调整结束时间
            if (!endTimeManuallyChanged) {
                const startDate = new Date(startTime);
                const newEndTime = formatDateTimeLocal(addHours(startDate, 3));
                $('#endTime').val(newEndTime);
            } else {
                // 如果结束时间早于开始时间，提示用户
                const endTime = $('#endTime').val();
                if (endTime && endTime <= startTime) {
                    // 显示警告但不自动清空，让用户决定
                    $('#endTime').addClass('is-invalid');
                    if (!$('#endTime').next('.invalid-feedback').length) {
                        $('#endTime').after('<div class="invalid-feedback">结束时间不能早于或等于开始时间</div>');
                    }
                } else {
                    $('#endTime').removeClass('is-invalid');
                    $('#endTime').next('.invalid-feedback').remove();
                }
            }
        }
    });
    
    // 场馆选择变化时，更新人数限制提示
    $('#venue').change(function() {
        const selectedOption = $(this).find('option:selected');
        const capacity = selectedOption.data('capacity');
        
        if (capacity) {
            $('#participantLimit').attr('max', capacity);
            $('#participantLimit').attr('placeholder', `建议不超过${capacity}人`);
        } else {
            $('#participantLimit').removeAttr('max');
            $('#participantLimit').attr('placeholder', '不限制请留空');
        }
    });
    
    // 表单验证
    $('#createActivityForm').submit(function(e) {
        // 清除之前的错误状态
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        let hasError = false;

        // 验证时间
        const startTimeVal = $('#startTime').val();
        const endTimeVal = $('#endTime').val();

        if (!startTimeVal) {
            $('#startTime').addClass('is-invalid');
            $('#startTime').after('<div class="invalid-feedback">请选择开始时间</div>');
            hasError = true;
        }

        if (!endTimeVal) {
            $('#endTime').addClass('is-invalid');
            $('#endTime').after('<div class="invalid-feedback">请选择结束时间</div>');
            hasError = true;
        }

        if (startTimeVal && endTimeVal) {
            const startTime = new Date(startTimeVal);
            const endTime = new Date(endTimeVal);

            if (endTime <= startTime) {
                $('#endTime').addClass('is-invalid');
                $('#endTime').after('<div class="invalid-feedback">结束时间必须晚于开始时间</div>');
                hasError = true;
            }

            // 检查时间是否过于接近（至少30分钟）
            const timeDiff = (endTime - startTime) / (1000 * 60); // 分钟
            if (timeDiff < 30) {
                $('#endTime').addClass('is-invalid');
                $('#endTime').after('<div class="invalid-feedback">活动时长至少需要30分钟</div>');
                hasError = true;
            }
        }

        // 验证人数限制
        const participantLimit = parseInt($('#participantLimit').val());
        const venueCapacity = parseInt($('#venue').find('option:selected').data('capacity'));

        if (participantLimit && venueCapacity && participantLimit > venueCapacity) {
            $('#participantLimit').addClass('is-invalid');
            $('#participantLimit').after(`<div class="invalid-feedback">人数限制不能超过场馆容量(${venueCapacity}人)</div>`);
            hasError = true;
        }

        if (hasError) {
            e.preventDefault();
            // 滚动到第一个错误字段
            const firstError = $('.is-invalid').first();
            if (firstError.length) {
                $('html, body').animate({
                    scrollTop: firstError.offset().top - 100
                }, 300);
            }
            return false;
        }

        return true;
    });

    // 实时清除验证错误
    $('input, select, textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
        $(this).next('.invalid-feedback').remove();
    });
});
</script>
{% endblock %}
