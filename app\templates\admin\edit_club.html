{% extends "admin_base.html" %}

{% block title %}编辑社团 - 管理后台{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧：社团信息编辑 -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-layer-group me-2 text-primary"></i>编辑社团信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.edit_club', club_id=club.ClubID) }}" id="editClubForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <!-- 基本信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-info-circle me-1"></i>基本信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="club_name" class="form-label">社团名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="club_name" name="club_name" 
                                   value="{{ club.ClubName }}" required maxlength="100">
                        </div>
                        <div class="col-md-6">
                            <label for="description" class="form-label">社团类别 <span class="text-danger">*</span></label>
                            <select class="form-select" id="description" name="description" required>
                                <option value="学术" {% if club.Description == '学术' %}selected{% endif %}>学术</option>
                                <option value="体育" {% if club.Description == '体育' %}selected{% endif %}>体育</option>
                                <option value="艺术" {% if club.Description == '艺术' %}selected{% endif %}>艺术</option>
                                <option value="公益" {% if club.Description == '公益' %}selected{% endif %}>公益</option>
                                <option value="娱乐" {% if club.Description == '娱乐' %}selected{% endif %}>娱乐</option>
                                <option value="其他" {% if club.Description == '其他' %}selected{% endif %}>其他</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="foundation_date" class="form-label">成立时间</label>
                            <input type="date" class="form-control" id="foundation_date" name="foundation_date" 
                                   value="{{ club.FoundationDate.strftime('%Y-%m-%d') if club.FoundationDate else '' }}">
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">社团状态 <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="活跃" {% if club.Status == '活跃' %}selected{% endif %}>活跃</option>
                                <option value="休眠" {% if club.Status == '休眠' %}selected{% endif %}>休眠</option>
                                <option value="解散" {% if club.Status == '解散' %}selected{% endif %}>解散</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="website" class="form-label">官方网站</label>
                            <input type="url" class="form-control" id="website" name="website"
                                   value="{{ club.Website or '' }}" placeholder="https://" maxlength="200">
                            <div class="form-text">可选，社团官方网站地址</div>
                        </div>
                    </div>

                    <!-- 会长信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-crown me-1"></i>会长信息
                            </h6>
                        </div>
                        <div class="col-md-12">
                            <label for="president_id" class="form-label">指定会长 <span class="text-danger">*</span></label>
                            <select class="form-select" id="president_id" name="president_id" required>
                                <option value="">请选择会长</option>
                                {% for member in available_presidents %}
                                <option value="{{ member.MemberID }}" {% if club.PresidentID == member.MemberID %}selected{% endif %}>
                                    {{ member.Name or member.Username }} (@{{ member.Username }})
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                {% if available_presidents %}
                                    找到 {{ available_presidents|length }} 个可选会长
                                {% else %}
                                    未找到可选会长，请先创建角色为"会长"的用户
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- 成员设置 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-users me-1"></i>成员设置
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="max_members" class="form-label">最大成员数</label>
                            <input type="number" class="form-control" id="max_members" name="max_members" 
                                   value="{{ club.MaxMembers or '' }}" min="1" max="1000">
                            <div class="form-text">留空表示不限制成员数量</div>
                        </div>
                        <div class="col-md-6">
                            <label for="current_members" class="form-label">当前成员数</label>
                            <input type="number" class="form-control" id="current_members" name="current_members" 
                                   value="{{ club.CurrentMembers or 0 }}" min="0" readonly>
                            <div class="form-text">系统自动统计，不可手动修改</div>
                        </div>
                    </div>



                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('admin.clubs') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="reset" class="btn btn-outline-warning">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 右侧：社团统计和操作 -->
    <div class="col-lg-4">
        <!-- 社团头像和基本信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body text-center">
                <div class="club-avatar-xl mb-3">
                    {{ club.ClubName[0] }}
                </div>
                <h5 class="mb-1">{{ club.ClubName }}</h5>
                <p class="text-muted mb-2">{{ club.Description }}</p>
                <span class="badge bg-{{ 'success' if club.Status == '活跃' else 'warning' if club.Status == '暂停' else 'danger' }} mb-3">
                    {{ club.Status }}
                </span>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="fw-bold">{{ club.CurrentMembers or 0 }}</div>
                        <small class="text-muted">当前成员</small>
                    </div>
                    <div class="col-6">
                        <div class="fw-bold">{{ club.MaxMembers or 0 }}</div>
                        <small class="text-muted">最大成员</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 社团信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2 text-info"></i>社团信息
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">社团ID</small>
                    <div class="fw-bold">{{ club.ClubID }}</div>
                </div>
                <div class="mb-3">
                    <small class="text-muted">成立时间</small>
                    <div class="fw-bold">{{ club.FoundationDate.strftime('%Y-%m-%d') if club.FoundationDate else '未设置' }}</div>
                </div>
                <div class="mb-3">
                    <small class="text-muted">会长</small>
                    <div class="fw-bold">
                        {% if club.president %}
                        {{ club.president.Name or club.president.Username }}
                        {% else %}
                        <span class="text-muted">暂无会长</span>
                        {% endif %}
                    </div>
                </div>
                <div class="mb-0">
                    <small class="text-muted">成员限制</small>
                    <div class="fw-bold">
                        {% if club.MaxMembers %}
                        最多 {{ club.MaxMembers }} 人
                        {% else %}
                        无限制
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2 text-success"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin.club_members', club_id=club.ClubID) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users me-1"></i>成员管理
                    </a>
                    <a href="{{ url_for('admin.club_activities', club_id=club.ClubID) }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-calendar-alt me-1"></i>活动管理
                    </a>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteClub()">
                        <i class="fas fa-trash me-1"></i>删除社团
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.club-avatar-xl {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 2rem;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 最大成员数验证
    $('#max_members').on('input', function() {
        const maxMembers = parseInt($(this).val());
        const currentMembers = parseInt('{{ club.CurrentMembers or 0 }}');

        if (maxMembers && maxMembers < currentMembers) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">最大成员数不能小于当前成员数</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
});

// 删除社团
function deleteClub() {
    if (confirm('确定要删除这个社团吗？此操作不可恢复，将同时删除社团的所有相关数据。')) {
        fetch(`/admin/clubs/{{ club.ClubID }}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('社团删除成功');
                window.location.href = '{{ url_for("admin.clubs") }}';
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败：' + error.message);
        });
    }
}
</script>
{% endblock %}
