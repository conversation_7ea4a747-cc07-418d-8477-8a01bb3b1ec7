{% extends "admin_base.html" %}

{% block title %}报名审批 - 会长后台{% endblock %}

{% block breadcrumb_title %}报名审批{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">活动报名审批</h2>
            <p class="text-muted mb-0">管理活动报名申请和参与者审批</p>
        </div>
        <div>
            <a href="{{ url_for('president.activities') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>返回活动管理
            </a>
        </div>
    </div>

    {% if upcoming_activities %}
    <!-- 即将举行的活动 -->
    <div class="row">
        {% for activity in upcoming_activities %}
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{{ activity.ActivityName }}</h5>
                        <span class="badge bg-warning">{{ activity.Status }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 活动基本信息 -->
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">社团</small>
                            <div>{{ activity.club.ClubName if activity.club else '未知社团' }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">活动类型</small>
                            <div>{{ activity.ActivityType }}</div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">开始时间</small>
                            <div>{{ activity.StartTime.strftime('%Y-%m-%d %H:%M') if activity.StartTime else '时间待定' }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">地点</small>
                            <div>{{ activity.venue.VenueName if activity.venue else '地点待定' }}</div>
                        </div>
                    </div>

                    <!-- 报名统计 -->
                    <div class="row mb-3">
                        <div class="col-4 text-center">
                            <div class="fw-bold text-primary">{{ activity.ActualParticipant }}</div>
                            <small class="text-muted">已报名</small>
                        </div>
                        <div class="col-4 text-center">
                            <div class="fw-bold text-warning">0</div>
                            <small class="text-muted">待审批</small>
                        </div>
                        <div class="col-4 text-center">
                            <div class="fw-bold text-info">{{ activity.ParticipantLimit or '∞' }}</div>
                            <small class="text-muted">限制人数</small>
                        </div>
                    </div>

                    <!-- 进度条 -->
                    {% if activity.ParticipantLimit %}
                    <div class="mb-3">
                        {% set progress = (activity.ActualParticipant / activity.ParticipantLimit * 100) if activity.ParticipantLimit > 0 else 0 %}
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar {% if progress >= 90 %}bg-danger{% elif progress >= 70 %}bg-warning{% else %}bg-success{% endif %}" 
                                 style="width: {{ progress }}%"></div>
                        </div>
                        <small class="text-muted">报名进度：{{ "%.1f"|format(progress) }}%</small>
                    </div>
                    {% endif %}

                    <!-- 操作按钮 -->
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm flex-fill"
                                onclick="viewRegistrations('{{ activity.ActivityID }}')">
                            <i class="fas fa-list me-1"></i>查看报名
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm"
                                onclick="exportRegistrations('{{ activity.ActivityID }}')"
                                title="导出报名名单">
                            <i class="fas fa-download"></i>
                        </button>
                        <a href="{{ url_for('president.activity_detail', activity_id=activity.ActivityID) }}" 
                           class="btn btn-outline-info btn-sm" title="活动详情">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 功能说明 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-info"></i>功能说明
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-clipboard-check me-2 text-primary"></i>报名管理</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>查看活动报名情况</li>
                                <li><i class="fas fa-check text-success me-2"></i>审批报名申请</li>
                                <li><i class="fas fa-check text-success me-2"></i>管理参与者名单</li>
                                <li><i class="fas fa-check text-success me-2"></i>导出报名数据</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-users me-2 text-warning"></i>参与者管理</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-clock text-warning me-2"></i>实时报名统计</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>人数限制控制</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>签到管理（开发中）</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>反馈收集（开发中）</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- 空状态 -->
    <div class="text-center py-5">
        <i class="fas fa-clipboard-check text-muted" style="font-size: 4rem;"></i>
        <h4 class="mt-3 text-muted">暂无即将举行的活动</h4>
        <p class="text-muted">当前没有需要管理报名的活动。</p>
        <div class="d-flex justify-content-center gap-2">
            <a href="{{ url_for('president.activities') }}" class="btn btn-primary">
                <i class="fas fa-calendar-alt me-1"></i>查看所有活动
            </a>
            {% if clubs %}
            <div class="dropdown">
                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-plus me-1"></i>创建活动
                </button>
                <ul class="dropdown-menu">
                    {% for club in clubs %}
                    <li>
                        <a class="dropdown-item" href="{{ url_for('president.create_activity', club_id=club.ClubID) }}">
                            <i class="fas fa-layer-group me-2"></i>{{ club.ClubName }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<!-- 报名详情模态框 -->
<div class="modal fade" id="registrationsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">活动报名详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="registrationsContent">
                    <!-- 动态加载内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="exportBtn">
                    <i class="fas fa-download me-1"></i>导出名单
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 查看报名情况
function viewRegistrations(activityId) {
    // 模拟报名数据
    const mockData = `
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            报名管理功能正在开发中，将包含以下功能：
        </div>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>报名者</th>
                        <th>报名时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="4" class="text-center text-muted py-4">
                            <i class="fas fa-users" style="font-size: 2rem;"></i>
                            <div class="mt-2">暂无报名数据</div>
                            <small>报名功能将在后续版本中实现</small>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
    
    $('#registrationsContent').html(mockData);
    $('#registrationsModal').modal('show');
}

// 导出报名名单
function exportRegistrations(activityId) {
    alert('导出功能开发中，将支持Excel和PDF格式导出。');
}

$(document).ready(function() {
    // 工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // 卡片悬停效果
    $('.card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
    
    // 导出按钮点击事件
    $('#exportBtn').click(function() {
        alert('导出功能开发中');
    });
});
</script>
{% endblock %}
