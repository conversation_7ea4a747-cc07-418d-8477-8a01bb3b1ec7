{% extends "admin_base.html" %}

{% block title %}报名审批 - 会长后台{% endblock %}

{% block breadcrumb_title %}报名审批{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">活动报名审批</h2>
            <p class="text-muted mb-0">管理活动报名申请和参与者审批</p>
        </div>
        <div>
            <a href="{{ url_for('president.activities') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>返回活动管理
            </a>
        </div>
    </div>

    {% if activity_registrations %}
    <!-- 活动报名申请列表 -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white">
            <h5 class="mb-0">
                <i class="fas fa-clipboard-list me-2 text-primary"></i>待审批的活动报名申请
                <span class="badge bg-warning ms-2">{{ activity_registrations|length }}</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>申请人</th>
                            <th>活动信息</th>
                            <th>申请时间</th>
                            <th>申请说明</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for registration in activity_registrations %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-3">
                                        {{ registration.applicant.Name[0] if registration.applicant.Name else registration.applicant.Username[0] }}
                                    </div>
                                    <div>
                                        <div class="fw-semibold">{{ registration.applicant.Name or registration.applicant.Username }}</div>
                                        <small class="text-muted">{{ registration.applicant.Username }}</small>
                                        {% if registration.applicant.College %}
                                        <br><small class="text-muted">{{ registration.applicant.College }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% set activity = activities[registration.RelatedID] %}
                                {% if activity %}
                                <div>
                                    <div class="fw-semibold">{{ activity.ActivityName }}</div>
                                    <small class="text-muted">{{ activity.ActivityType }}</small>
                                    <br><small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ activity.StartTime.strftime('%m-%d %H:%M') if activity.StartTime else '时间待定' }}
                                    </small>
                                    <br><small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ activity.venue.VenueName if activity.venue else '地点待定' }}
                                    </small>
                                </div>
                                {% else %}
                                <span class="text-muted">活动信息不可用</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>{{ registration.RequestTime.strftime('%Y-%m-%d') }}</div>
                                <small class="text-muted">{{ registration.RequestTime.strftime('%H:%M') }}</small>
                                <br>
                                {% set days = (moment() - registration.RequestTime).days %}
                                <span class="badge {% if days > 7 %}bg-danger{% elif days > 3 %}bg-warning{% else %}bg-info{% endif %}">
                                    {{ days }}天前
                                </span>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 200px;" title="{{ registration.Comments }}">
                                    {{ registration.Comments or '无特殊说明' }}
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-success"
                                            onclick="approveRegistration('{{ registration.RequestID }}')"
                                            title="批准申请">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger"
                                            onclick="rejectRegistration('{{ registration.RequestID }}')"
                                            title="拒绝申请">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-info"
                                            onclick="viewRegistrationDetail('{{ registration.RequestID }}')"
                                            title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 功能说明 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-info"></i>功能说明
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-clipboard-check me-2 text-primary"></i>报名管理</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>查看活动报名情况</li>
                                <li><i class="fas fa-check text-success me-2"></i>审批报名申请</li>
                                <li><i class="fas fa-check text-success me-2"></i>管理参与者名单</li>
                                <li><i class="fas fa-check text-success me-2"></i>导出报名数据</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-users me-2 text-warning"></i>参与者管理</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-clock text-warning me-2"></i>实时报名统计</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>人数限制控制</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>签到管理（开发中）</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>反馈收集（开发中）</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- 空状态 -->
    <div class="text-center py-5">
        <i class="fas fa-clipboard-check text-muted" style="font-size: 4rem;"></i>
        <h4 class="mt-3 text-muted">暂无待审批的活动报名申请</h4>
        <p class="text-muted">当前没有需要处理的活动报名申请。</p>
        <div class="d-flex justify-content-center gap-2">
            <a href="{{ url_for('president.activities') }}" class="btn btn-primary">
                <i class="fas fa-calendar-alt me-1"></i>查看所有活动
            </a>
            <a href="{{ url_for('president.dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-tachometer-alt me-1"></i>返回仪表板
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- 报名详情模态框 -->
<div class="modal fade" id="registrationsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">活动报名详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="registrationsContent">
                    <!-- 动态加载内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="exportBtn">
                    <i class="fas fa-download me-1"></i>导出名单
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 批准活动报名申请
function approveRegistration(requestId) {
    if (confirm('确定要批准这个活动报名申请吗？')) {
        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ url_for('president.approve_activity_registration', request_id='') }}${requestId}`;

        // 添加CSRF令牌
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}

// 拒绝活动报名申请
function rejectRegistration(requestId) {
    const reason = prompt('请输入拒绝理由（可选）：');
    if (reason !== null) { // 用户没有取消
        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ url_for('president.reject_activity_registration', request_id='') }}${requestId}`;

        // 添加CSRF令牌
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // 添加拒绝理由
        if (reason.trim()) {
            const commentsInput = document.createElement('input');
            commentsInput.type = 'hidden';
            commentsInput.name = 'comments';
            commentsInput.value = reason.trim();
            form.appendChild(commentsInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// 查看申请详情
function viewRegistrationDetail(requestId) {
    alert('查看详情功能开发中');
}

$(document).ready(function() {
    // 工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();

    // 表格行悬停效果
    $('tbody tr').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
});
</script>
{% endblock %}
