{% extends "base.html" %}

{% block title %}{{ club.ClubName }} - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 返回按钮 -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ url_for('common.clubs') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回社团列表
            </a>
        </div>
    </div>

    <!-- 社团信息 -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>{{ club.ClubName }}
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>社团类别：</strong>{{ club.Description }}</p>
                            <p><strong>成立时间：</strong>{{ club.FoundationDate.strftime('%Y年%m月%d日') if club.FoundationDate else '未知' }}</p>
                            <p><strong>社团状态：</strong>
                                <span class="badge bg-{{ 'success' if club.Status == '活跃' else 'secondary' }}">
                                    {{ club.Status }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>会长：</strong>{{ club.president.Name if club.president else '未知' }}</p>
                            <p><strong>成员数量：</strong>{{ member_count }}/{{ club.MaxMembers or '无限制' }}</p>
                            {% if club.Website %}
                            <p><strong>官方网站：</strong>
                                <a href="{{ club.Website }}" target="_blank" class="text-decoration-none">
                                    <i class="fas fa-external-link-alt me-1"></i>访问网站
                                </a>
                            </p>
                            {% endif %}
                        </div>
                    </div>

                    {% if session.user_id and session.role == '会员' %}
                    <div class="text-center">
                        {% if user_club_status == '已批准' %}
                            <div class="alert alert-success mb-3">
                                <i class="fas fa-check-circle me-2"></i>您已经是该社团的成员
                            </div>
                            <button type="button" class="btn btn-outline-danger" onclick="showWithdrawModal()">
                                <i class="fas fa-sign-out-alt me-2"></i>申请退出社团
                            </button>
                        {% elif user_club_status == '待审批' %}
                            <div class="alert alert-warning mb-3">
                                <i class="fas fa-clock me-2"></i>您的申请正在审核中，请耐心等待
                            </div>
                            <button type="button" class="btn btn-outline-secondary" onclick="showCancelModal()">
                                <i class="fas fa-times me-2"></i>取消申请
                            </button>
                        {% elif user_club_status == '已拒绝' %}
                            <div class="alert alert-danger mb-3">
                                <i class="fas fa-times-circle me-2"></i>您的申请已被拒绝
                            </div>
                            <button type="button" class="btn btn-primary" onclick="showApplyModal()">
                                <i class="fas fa-redo me-2"></i>重新申请
                            </button>
                        {% elif user_club_status == '已退出' %}
                            <div class="alert alert-secondary mb-3">
                                <i class="fas fa-sign-out-alt me-2"></i>您已退出该社团
                            </div>
                            <button type="button" class="btn btn-primary" onclick="showApplyModal()">
                                <i class="fas fa-plus-circle me-2"></i>重新申请加入
                            </button>
                        {% else %}
                            <button type="button" class="btn btn-primary btn-lg" onclick="showApplyModal()">
                                <i class="fas fa-plus-circle me-2"></i>申请加入社团
                            </button>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 最近活动 -->
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>最近活动
                    </h5>
                </div>
                <div class="card-body">
                    {% for activity in recent_activities %}
                    <div class="mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                        <h6 class="mb-1">{{ activity.ActivityName }}</h6>
                        <p class="small text-muted mb-1">
                            <i class="fas fa-calendar me-1"></i>
                            {{ activity.StartTime.strftime('%Y-%m-%d %H:%M') if activity.StartTime else '时间待定' }}
                        </p>
                        <p class="small text-muted mb-1">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ activity.venue.VenueName if activity.venue else '地点待定' }}
                        </p>
                        <span class="badge bg-{{ 'primary' if activity.Status == '计划中' else 'success' if activity.Status == '进行中' else 'secondary' }}">
                            {{ activity.Status }}
                        </span>
                    </div>
                    {% else %}
                    <div class="text-center py-3 text-muted">
                        <i class="fas fa-calendar-alt" style="font-size: 2rem;"></i>
                        <div class="mt-2">暂无活动</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- 社团成员列表 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>社团成员
                        <span class="badge bg-primary ms-2">{{ member_count }}人</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if club_members %}
                        <div class="row">
                            {% for member_club in club_members %}
                            <div class="col-lg-6 col-xl-4 mb-3">
                                <div class="d-flex align-items-center p-3 border rounded">
                                    <div class="flex-shrink-0">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 48px; height: 48px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">{{ member_club.member.Name or member_club.member.Username }}</h6>
                                        <p class="text-muted mb-1 small">
                                            {% if member_club.member.College %}
                                            <i class="fas fa-graduation-cap me-1"></i>{{ member_club.member.College }}
                                            {% endif %}
                                        </p>
                                        {% if member_club.member.Specialty %}
                                        <p class="text-muted mb-1 small">
                                            <i class="fas fa-book me-1"></i>{{ member_club.member.Specialty }}
                                        </p>
                                        {% endif %}
                                        <small class="text-success">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ member_club.ApprovalTime.strftime('%Y年%m月') if member_club.ApprovalTime else '未知' }} 加入
                                        </small>
                                        {% if member_club.member.MemberID == club.PresidentID %}
                                        <div class="mt-1">
                                            <span class="badge bg-warning">会长</span>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        {% if member_count > club_members|length %}
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                还有 {{ member_count - club_members|length }} 位成员...
                            </small>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5 text-muted">
                            <i class="fas fa-users" style="font-size: 3rem;"></i>
                            <div class="mt-3">暂无成员</div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 申请加入社团模态框 -->
{% if session.user_id and session.role == '会员' and user_club_status in [None, '已拒绝', '已退出'] %}
<div class="modal fade" id="applyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">申请加入社团</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('member.apply_club', club_id=club.ClubID) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <p>您正在申请加入 "<strong>{{ club.ClubName }}</strong>"</p>
                    <div class="mb-3">
                        <label for="application_reason" class="form-label">申请理由</label>
                        <textarea class="form-control" id="application_reason" name="application_reason"
                                  rows="4" placeholder="请简要说明您申请加入该社团的理由..." required></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        申请提交后，需要等待社团会长审批。请耐心等待审批结果。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">提交申请</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<!-- 申请退出社团模态框 -->
{% if session.user_id and session.role == '会员' and user_club_status == '已批准' %}
<div class="modal fade" id="withdrawModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">申请退出社团</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('member.withdraw_club', club_id=club.ClubID) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <p>您正在申请退出 "<strong>{{ club.ClubName }}</strong>"</p>
                    <div class="mb-3">
                        <label for="withdraw_reason" class="form-label">退出理由</label>
                        <textarea class="form-control" id="withdraw_reason" name="withdraw_reason"
                                  rows="4" placeholder="请简要说明您退出该社团的理由..." required></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        退出申请提交后，需要等待社团会长审批。审批通过后您将失去社团成员身份。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">提交退出申请</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<!-- 取消申请模态框 -->
{% if session.user_id and session.role == '会员' and user_club_status == '待审批' %}
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">取消申请</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您确定要取消加入 "<strong>{{ club.ClubName }}</strong>" 的申请吗？</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    取消后，您可以重新申请加入该社团。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">不取消</button>
                <form method="POST" action="{{ url_for('member.cancel_application', club_id=club.ClubID) }}" class="d-inline">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-outline-danger">确认取消</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function showApplyModal() {
    const modalElement = document.getElementById('applyModal');
    if (modalElement) {
        new bootstrap.Modal(modalElement).show();
    } else {
        alert('申请功能暂时不可用，请刷新页面重试');
    }
}

function showWithdrawModal() {
    const modalElement = document.getElementById('withdrawModal');
    if (modalElement) {
        new bootstrap.Modal(modalElement).show();
    }
}

function showCancelModal() {
    const modalElement = document.getElementById('cancelModal');
    if (modalElement) {
        new bootstrap.Modal(modalElement).show();
    }
}
</script>
{% endblock %}
