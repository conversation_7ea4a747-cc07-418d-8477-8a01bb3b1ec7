{% extends "admin_base.html" %}

{% block title %}编辑活动 - 管理后台{% endblock %}
{% block page_title %}编辑活动{% endblock %}
{% block title_icon %}<i class="fas fa-edit"></i>{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-nav">
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.dashboard') }}">
                <i class="fas fa-home"></i>
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.activities') }}">活动管理</a>
        </li>
        <li class="breadcrumb-item active">编辑活动</li>
    </ol>
</nav>
{% endblock %}

{% block page_actions %}
<div class="d-flex gap-2">
    <a href="{{ url_for('admin.activities') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回列表
    </a>
    <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}" class="btn btn-outline-info" target="_blank">
        <i class="fas fa-eye me-1"></i>查看详情
    </a>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧：活动信息编辑 -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>编辑活动信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.edit_activity', activity_id=activity.ActivityID) }}" id="editActivityForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <!-- 基本信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-info-circle me-1"></i>基本信息
                            </h6>
                        </div>
                        <div class="col-md-8">
                            <label for="activity_name" class="form-label">活动名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="activity_name" name="activity_name" 
                                   value="{{ activity.ActivityName }}" required maxlength="100">
                        </div>
                        <div class="col-md-4">
                            <label for="activity_type" class="form-label">活动类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="activity_type" name="activity_type" required>
                                <option value="讲座" {% if activity.ActivityType == '讲座' %}selected{% endif %}>讲座</option>
                                <option value="比赛" {% if activity.ActivityType == '比赛' %}selected{% endif %}>比赛</option>
                                <option value="培训" {% if activity.ActivityType == '培训' %}selected{% endif %}>培训</option>
                                <option value="展览" {% if activity.ActivityType == '展览' %}selected{% endif %}>展览</option>
                                <option value="演出" {% if activity.ActivityType == '演出' %}selected{% endif %}>演出</option>
                                <option value="会议" {% if activity.ActivityType == '会议' %}selected{% endif %}>会议</option>
                                <option value="其他" {% if activity.ActivityType == '其他' %}selected{% endif %}>其他</option>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label for="description" class="form-label">活动描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      maxlength="500">{{ activity.Description or '' }}</textarea>
                            <div class="form-text">
                                <span id="desc-count">0</span>/500 字符
                            </div>
                        </div>
                    </div>

                    <!-- 主办方信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-users me-1"></i>主办方信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="club_id" class="form-label">主办社团 <span class="text-danger">*</span></label>
                            <select class="form-select" id="club_id" name="club_id" required>
                                {% for club in clubs %}
                                <option value="{{ club.ClubID }}" {% if activity.ClubID == club.ClubID %}selected{% endif %}>
                                    {{ club.ClubName }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="organizer_id" class="form-label">活动组织者 <span class="text-danger">*</span></label>
                            <select class="form-select" id="organizer_id" name="organizer_id" required>
                                {% for organizer in organizers %}
                                <option value="{{ organizer.MemberID }}" {% if activity.OrganizerID == organizer.MemberID %}selected{% endif %}>
                                    {{ organizer.Name }} ({{ organizer.Role }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- 时间地点 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>时间地点
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="start_time" class="form-label">开始时间 <span class="text-danger">*</span></label>
                            <input type="datetime-local" class="form-control" id="start_time" name="start_time" 
                                   value="{{ activity.StartTime.strftime('%Y-%m-%dT%H:%M') if activity.StartTime else '' }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="end_time" class="form-label">结束时间 <span class="text-danger">*</span></label>
                            <input type="datetime-local" class="form-control" id="end_time" name="end_time" 
                                   value="{{ activity.EndTime.strftime('%Y-%m-%dT%H:%M') if activity.EndTime else '' }}" required>
                        </div>
                        <div class="col-md-12">
                            <label for="venue_id" class="form-label">活动场馆 <span class="text-danger">*</span></label>
                            <select class="form-select" id="venue_id" name="venue_id" required>
                                <option value="">请选择场馆</option>
                                {% for venue in venues %}
                                <option value="{{ venue.VenueID }}" {% if activity.VenueID == venue.VenueID %}selected{% endif %}>
                                    {{ venue.VenueName }} (容量: {{ venue.Capacity or '未知' }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- 参与设置 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-cog me-1"></i>参与设置
                            </h6>
                        </div>
                        <div class="col-md-4">
                            <label for="participant_limit" class="form-label">参与人数限制</label>
                            <input type="number" class="form-control" id="participant_limit" name="participant_limit"
                                   value="{{ activity.ParticipantLimit or '' }}" min="0" max="10000">
                            <div class="form-text">留空或0表示不限制人数</div>
                        </div>
                        <div class="col-md-4">
                            <label for="actual_participant" class="form-label">实际参与人数</label>
                            <input type="number" class="form-control" id="actual_participant" name="actual_participant"
                                   value="{{ activity.ActualParticipant or 0 }}" min="0">
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">活动状态 <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="计划中" {% if activity.Status == '计划中' %}selected{% endif %}>计划中</option>
                                <option value="进行中" {% if activity.Status == '进行中' %}selected{% endif %}>进行中</option>
                                <option value="已完成" {% if activity.Status == '已完成' %}selected{% endif %}>已完成</option>
                                <option value="已取消" {% if activity.Status == '已取消' %}selected{% endif %}>已取消</option>
                            </select>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('admin.activities') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="reset" class="btn btn-outline-warning">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 右侧：活动统计和操作 -->
    <div class="col-lg-4">
        <!-- 活动头像和基本信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body text-center">
                <div class="activity-avatar-xl mb-3">
                    {{ activity.ActivityName[0] }}
                </div>
                <h5 class="mb-1">{{ activity.ActivityName }}</h5>
                <p class="text-muted mb-2">{{ activity.ActivityType }}</p>
                <span class="badge bg-{{ 'primary' if activity.Status == '计划中' else 'success' if activity.Status == '报名中' else 'warning' if activity.Status == '进行中' else 'secondary' if activity.Status == '已结束' else 'danger' }} mb-3">
                    {{ activity.Status }}
                </span>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="fw-bold">{{ activity.ActualParticipant or 0 }}</div>
                        <small class="text-muted">实际参与</small>
                    </div>
                    <div class="col-6">
                        <div class="fw-bold">{{ activity.ParticipantLimit or '∞' }}</div>
                        <small class="text-muted">人数限制</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 活动信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2 text-info"></i>活动信息
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">活动ID</small>
                    <div class="fw-bold">{{ activity.ActivityID }}</div>
                </div>
                <div class="mb-3">
                    <small class="text-muted">主办社团</small>
                    <div class="fw-bold">
                        {% if activity.club %}
                        {{ activity.club.ClubName }}
                        {% else %}
                        <span class="text-muted">未知</span>
                        {% endif %}
                    </div>
                </div>
                <div class="mb-3">
                    <small class="text-muted">开始时间</small>
                    <div class="fw-bold">{{ activity.StartTime.strftime('%Y-%m-%d %H:%M') if activity.StartTime else '未设置' }}</div>
                </div>
                <div class="mb-3">
                    <small class="text-muted">结束时间</small>
                    <div class="fw-bold">{{ activity.EndTime.strftime('%Y-%m-%d %H:%M') if activity.EndTime else '未设置' }}</div>
                </div>
                <div class="mb-0">
                    <small class="text-muted">活动地点</small>
                    <div class="fw-bold">
                        {% if activity.venue %}
                        {{ activity.venue.VenueName }}
                        {% else %}
                        <span class="text-muted">待定</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2 text-success"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin.activities') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list me-1"></i>返回列表
                    </a>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteActivity()">
                        <i class="fas fa-trash me-1"></i>删除活动
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.activity-avatar-xl {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 2rem;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 字符计数
    $('#description').on('input', function() {
        const count = $(this).val().length;
        $('#desc-count').text(count);
        
        if (count > 500) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // 初始化字符计数
    $('#description').trigger('input');
    
    // 时间验证
    $('#end_time').on('change', function() {
        const startTime = new Date($('#start_time').val());
        const endTime = new Date($(this).val());
        
        if (startTime && endTime && endTime <= startTime) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">结束时间必须晚于开始时间</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
    
    // 参与人数限制验证
    $('#participant_limit').on('input', function() {
        const participantLimit = parseInt($(this).val());
        const actualParticipant = parseInt($('#actual_participant').val()) || 0;

        if (participantLimit && participantLimit > 0 && participantLimit < actualParticipant) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">参与人数限制不能小于实际参与人数</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
});

// 删除活动
function deleteActivity() {
    if (confirm('确定要删除这个活动吗？此操作不可恢复，将同时删除活动的所有相关数据。')) {
        fetch(`/admin/activities/{{ activity.ActivityID }}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('活动删除成功');
                window.location.href = '{{ url_for("admin.activities") }}';
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败：' + error.message);
        });
    }
}
</script>
{% endblock %}
