{% extends "admin_base.html" %}

{% block title %}活动管理 - 管理后台{% endblock %}
{% block page_title %}活动管理{% endblock %}
{% block title_icon %}<i class="fas fa-calendar-alt"></i>{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-nav">
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.dashboard') }}">
                <i class="fas fa-home"></i>
            </a>
        </li>
        <li class="breadcrumb-item active">活动管理</li>
    </ol>
</nav>
{% endblock %}



{% block content %}
<!-- 搜索和筛选 -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">搜索活动</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="输入活动名称" value="{{ request.args.get('search', '') }}">
                </div>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">状态筛选</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" {% if current_status == 'all' %}selected{% endif %}>全部状态</option>
                    <option value="计划中" {% if current_status == '计划中' %}selected{% endif %}>计划中</option>
                    <option value="进行中" {% if current_status == '进行中' %}selected{% endif %}>进行中</option>
                    <option value="已完成" {% if current_status == '已完成' %}selected{% endif %}>已完成</option>
                    <option value="已取消" {% if current_status == '已取消' %}selected{% endif %}>已取消</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="activity_type" class="form-label">类型筛选</label>
                <select class="form-select" id="activity_type" name="activity_type">
                    <option value="all" {% if current_type == 'all' %}selected{% endif %}>全部类型</option>
                    <option value="讲座" {% if current_type == '讲座' %}selected{% endif %}>讲座</option>
                    <option value="比赛" {% if current_type == '比赛' %}selected{% endif %}>比赛</option>
                    <option value="培训" {% if current_type == '培训' %}selected{% endif %}>培训</option>
                    <option value="展览" {% if current_type == '展览' %}selected{% endif %}>展览</option>
                    <option value="演出" {% if current_type == '演出' %}selected{% endif %}>演出</option>
                    <option value="会议" {% if current_type == '会议' %}selected{% endif %}>会议</option>
                    <option value="其他" {% if current_type == '其他' %}selected{% endif %}>其他</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="club_id" class="form-label">社团筛选</label>
                <select class="form-select" id="club_id" name="club_id">
                    <option value="all" {% if current_club == 'all' %}selected{% endif %}>全部社团</option>
                    {% for club in clubs %}
                    <option value="{{ club.ClubID }}" {% if current_club == club.ClubID %}selected{% endif %}>
                        {{ club.ClubName }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_range" class="form-label">时间范围</label>
                <select class="form-select" id="date_range" name="date_range">
                    <option value="all" {% if current_date_range == 'all' %}selected{% endif %}>全部时间</option>
                    <option value="today" {% if current_date_range == 'today' %}selected{% endif %}>今天</option>
                    <option value="week" {% if current_date_range == 'week' %}selected{% endif %}>本周</option>
                    <option value="month" {% if current_date_range == 'month' %}selected{% endif %}>本月</option>
                    <option value="upcoming" {% if current_date_range == 'upcoming' %}selected{% endif %}>即将举行</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="{{ url_for('admin.create_activity') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>创建活动
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 活动列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="fas fa-calendar-alt me-2"></i>活动列表
            <span class="badge bg-primary ms-2">{{ pagination.total }}个</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if activities %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="5%">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th width="25%">活动信息</th>
                        <th width="15%">主办社团</th>
                        <th width="10%">类型</th>
                        <th width="15%">时间地点</th>
                        <th width="10%">参与人数</th>
                        <th width="10%">状态</th>
                        <th width="10%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for activity in activities %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input activity-checkbox" 
                                   value="{{ activity.ActivityID }}">
                        </td>
                        <td>
                            <div>
                                <div class="fw-bold">{{ activity.ActivityName }}</div>
                                {% if activity.Description %}
                                <div class="text-muted small">
                                    {{ activity.Description[:50] }}{% if activity.Description|length > 50 %}...{% endif %}
                                </div>
                                {% endif %}
                                <div class="text-muted small">
                                    <i class="fas fa-user me-1"></i>{{ activity.organizer.Name if activity.organizer else '未知' }}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if activity.club %}
                            <div class="d-flex align-items-center">
                                <div class="club-avatar-sm me-2">
                                    {{ activity.club.ClubName[0] }}
                                </div>
                                <div>
                                    <div class="small fw-bold">{{ activity.club.ClubName }}</div>
                                    <div class="text-muted" style="font-size: 0.7rem;">
                                        {{ activity.club.Description }}
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <span class="text-muted">未知社团</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'primary' if activity.ActivityType == '讲座' else 'success' if activity.ActivityType == '比赛' else 'warning' if activity.ActivityType == '志愿活动' else 'info' }}">
                                {{ activity.ActivityType }}
                            </span>
                        </td>
                        <td>
                            <div class="small">
                                {% if activity.StartTime %}
                                <div><i class="fas fa-calendar me-1"></i>{{ activity.StartTime.strftime('%m-%d %H:%M') }}</div>
                                {% endif %}
                                {% if activity.venue %}
                                <div><i class="fas fa-map-marker-alt me-1"></i>{{ activity.venue.VenueName }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="text-center">
                                {% if activity.ActualParticipant %}
                                <div class="fw-bold">{{ activity.ActualParticipant }}</div>
                                <div class="text-muted small">实际</div>
                                {% elif activity.ParticipantLimit %}
                                <div class="fw-bold">{{ activity.ParticipantLimit }}</div>
                                <div class="text-muted small">限制</div>
                                {% else %}
                                <span class="text-muted">无限制</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'warning' if activity.Status == '计划中' else 'primary' if activity.Status == '进行中' else 'success' if activity.Status == '已完成' else 'danger' }}">
                                {{ activity.Status }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('admin.edit_activity', activity_id=activity.ActivityID) }}"
                                   class="btn btn-outline-primary"
                                   data-bs-toggle="tooltip" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="deleteActivity('{{ activity.ActivityID }}', '{{ activity.ActivityName }}')"
                                        data-bs-toggle="tooltip" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-alt text-muted" style="font-size: 4rem;"></i>
            <h5 class="mt-3 text-muted">暂无活动数据</h5>
            <p class="text-muted">点击上方"创建活动"按钮创建第一个活动</p>
            <a href="{{ url_for('admin.create_activity') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>创建活动
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<nav aria-label="活动列表分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.activities', page=pagination.prev_num, search=request.args.get('search', ''), status=request.args.get('status', 'all'), activity_type=request.args.get('activity_type', 'all'), club_id=request.args.get('club_id', 'all'), date_range=request.args.get('date_range', 'all')) }}">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        {% endif %}

        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.activities', page=page_num, search=request.args.get('search', ''), status=request.args.get('status', 'all'), activity_type=request.args.get('activity_type', 'all'), club_id=request.args.get('club_id', 'all'), date_range=request.args.get('date_range', 'all')) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}

        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.activities', page=pagination.next_num, search=request.args.get('search', ''), status=request.args.get('status', 'all'), activity_type=request.args.get('activity_type', 'all'), club_id=request.args.get('club_id', 'all'), date_range=request.args.get('date_range', 'all')) }}">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- 批量操作 -->
<div class="card border-0 shadow-sm mt-4" id="batchActions" style="display: none;">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <span class="text-muted">已选择 <span id="selectedCount">0</span> 个活动</span>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-warning" onclick="batchChangeStatus()">
                    <i class="fas fa-toggle-on me-1"></i>批量修改状态
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="batchDelete()">
                    <i class="fas fa-trash me-1"></i>批量删除
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="exportSelected()">
                    <i class="fas fa-download me-1"></i>导出选中
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.club-avatar-sm {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.8rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 全选/取消全选
    $('#selectAll').change(function() {
        $('.activity-checkbox').prop('checked', this.checked);
        updateBatchActions();
    });
    
    // 单个选择
    $('.activity-checkbox').change(function() {
        updateBatchActions();
        
        // 更新全选状态
        var total = $('.activity-checkbox').length;
        var checked = $('.activity-checkbox:checked').length;
        $('#selectAll').prop('indeterminate', checked > 0 && checked < total);
        $('#selectAll').prop('checked', checked === total);
    });
    
    // 更新批量操作显示
    function updateBatchActions() {
        var selectedCount = $('.activity-checkbox:checked').length;
        $('#selectedCount').text(selectedCount);
        
        if (selectedCount > 0) {
            $('#batchActions').show();
        } else {
            $('#batchActions').hide();
        }
    }
});

// 删除活动
function deleteActivity(activityId, activityName) {
    if (confirm(`确定要删除活动"${activityName}"吗？此操作不可恢复。`)) {
        // 创建表单数据
        var formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');

        // 发送删除请求
        fetch(`/admin/activities/${activityId}/delete`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('删除失败');
            }
        })
        .catch(error => {
            alert('删除失败：' + error.message);
        });
    }
}

// 批量删除
function batchDelete() {
    var selectedIds = $('.activity-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要删除的活动');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${selectedIds.length} 个活动吗？此操作不可恢复。`)) {
        // 发送批量删除请求
        fetch('/admin/activities/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({activity_ids: selectedIds})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('批量删除失败：' + data.message);
            }
        })
        .catch(error => {
            alert('批量删除失败：' + error.message);
        });
    }
}

// 批量修改状态
function batchChangeStatus() {
    var selectedIds = $('.activity-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要修改的活动');
        return;
    }
    
    var newStatus = prompt('请输入新状态（计划中/进行中/已完成/已取消）：');
    if (newStatus && ['计划中', '进行中', '已完成', '已取消'].includes(newStatus)) {
        // 发送批量修改请求
        fetch('/admin/activities/batch-change-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                activity_ids: selectedIds,
                new_status: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('批量修改失败：' + data.message);
            }
        })
        .catch(error => {
            alert('批量修改失败：' + error.message);
        });
    }
}

// 导出数据
function exportActivities() {
    window.open('/admin/activities/export', '_blank');
}

// 导出选中
function exportSelected() {
    var selectedIds = $('.activity-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要导出的活动');
        return;
    }
    
    var form = document.createElement('form');
    form.method = 'POST';
    form.action = '/admin/activities/export-selected';
    
    var csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '{{ csrf_token() }}';
    form.appendChild(csrfInput);
    
    var idsInput = document.createElement('input');
    idsInput.type = 'hidden';
    idsInput.name = 'activity_ids';
    idsInput.value = JSON.stringify(selectedIds);
    form.appendChild(idsInput);
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
</script>
{% endblock %}
