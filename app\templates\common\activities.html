{% extends "base.html" %}

{% block title %}活动列表 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="fas fa-calendar-alt me-2 text-primary"></i>活动列表
            </h2>
            <p class="text-muted">参与丰富多彩的校园活动</p>
        </div>
    </div>

    <!-- 即将举行的活动 -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="mb-4">
                <i class="fas fa-clock me-2 text-warning"></i>即将举行
            </h3>
            <div class="row">
                {% for activity in upcoming_activities %}
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">{{ activity.ActivityName }}</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text text-muted">
                                <i class="fas fa-layer-group me-2"></i>{{ activity.club.ClubName if activity.club else '未知社团' }}
                            </p>
                            <p class="card-text">
                                <i class="fas fa-calendar me-2"></i>{{ activity.StartTime.strftime('%Y年%m月%d日 %H:%M') if activity.StartTime else '时间待定' }}
                            </p>
                            <p class="card-text">
                                <i class="fas fa-map-marker-alt me-2"></i>{{ activity.venue.VenueName if activity.venue else '地点待定' }}
                            </p>
                            {% if activity.ParticipantLimit %}
                            <p class="card-text">
                                <i class="fas fa-users me-2"></i>限{{ activity.ParticipantLimit }}人
                            </p>
                            {% endif %}
                            {% if activity.Description %}
                            <p class="card-text">{{ activity.Description[:100] }}{% if activity.Description|length > 100 %}...{% endif %}</p>
                            {% endif %}
                        </div>
                        <div class="card-footer bg-transparent">
                            <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>查看详情
                            </a>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="col-12">
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-alt text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">暂无即将举行的活动</h5>
                        <p class="text-muted">请关注社团动态</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 最近完成的活动 -->
    <div class="row">
        <div class="col-12">
            <h3 class="mb-4">
                <i class="fas fa-check-circle me-2 text-success"></i>最近完成
            </h3>
            <div class="row">
                {% for activity in recent_activities %}
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">{{ activity.ActivityName }}</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text text-muted">
                                <i class="fas fa-layer-group me-2"></i>{{ activity.club.ClubName if activity.club else '未知社团' }}
                            </p>
                            <p class="card-text">
                                <i class="fas fa-calendar me-2"></i>
                                {{ activity.StartTime.strftime('%Y年%m月%d日') if activity.StartTime else '时间未知' }} - 
                                {{ activity.EndTime.strftime('%Y年%m月%d日') if activity.EndTime else '时间未知' }}
                            </p>
                            <p class="card-text">
                                <i class="fas fa-map-marker-alt me-2"></i>{{ activity.venue.VenueName if activity.venue else '地点未知' }}
                            </p>
                            {% if activity.ActualParticipant %}
                            <p class="card-text">
                                <i class="fas fa-users me-2"></i>实际参与{{ activity.ActualParticipant }}人
                            </p>
                            {% endif %}
                            {% if activity.Description %}
                            <p class="card-text">{{ activity.Description[:100] }}{% if activity.Description|length > 100 %}...{% endif %}</p>
                            {% endif %}
                        </div>
                        <div class="card-footer bg-transparent">
                            <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>查看详情
                            </a>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="col-12">
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-check text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">暂无最近完成的活动</h5>
                        <p class="text-muted">活动记录将在这里显示</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
