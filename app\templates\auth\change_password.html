{% extends "base.html" %}

{% block title %}修改密码 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="bi bi-key me-2 text-primary"></i>修改密码
            </h2>
            <p class="text-muted">为了账户安全，请定期更换密码</p>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="bi bi-shield-lock me-2"></i>密码修改
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('auth.change_password') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <!-- 当前密码 -->
                        <div class="mb-3">
                            <label for="current_password" class="form-label">
                                <i class="bi bi-lock me-1"></i>当前密码
                            </label>
                            <input type="password" class="form-control" id="current_password" 
                                   name="current_password" required
                                   placeholder="请输入当前密码">
                        </div>

                        <!-- 新密码 -->
                        <div class="mb-3">
                            <label for="new_password" class="form-label">
                                <i class="bi bi-key me-1"></i>新密码
                            </label>
                            <input type="password" class="form-control" id="new_password" 
                                   name="new_password" required minlength="6"
                                   placeholder="请输入新密码（至少6位）">
                            <div class="form-text">密码长度至少6位，建议包含字母和数字</div>
                        </div>

                        <!-- 确认新密码 -->
                        <div class="mb-4">
                            <label for="confirm_password" class="form-label">
                                <i class="bi bi-check-circle me-1"></i>确认新密码
                            </label>
                            <input type="password" class="form-control" id="confirm_password" 
                                   name="confirm_password" required minlength="6"
                                   placeholder="请再次输入新密码">
                        </div>

                        <!-- 安全提示 -->
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>安全提示：</strong>
                            <ul class="mb-0 mt-2">
                                <li>密码长度至少6位</li>
                                <li>建议使用字母、数字和特殊字符的组合</li>
                                <li>不要使用过于简单的密码</li>
                                <li>定期更换密码以保证账户安全</li>
                            </ul>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-1"></i>修改密码
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 密码强度指示器 -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="bi bi-shield-check me-2"></i>密码强度
                    </h6>
                    <div class="progress mb-2" style="height: 8px;">
                        <div id="password-strength" class="progress-bar" role="progressbar" 
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <small id="password-strength-text" class="text-muted">请输入密码</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 密码强度检测
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = '';
    
    if (password.length >= 6) strength += 20;
    if (password.length >= 8) strength += 10;
    if (/[a-z]/.test(password)) strength += 20;
    if (/[A-Z]/.test(password)) strength += 20;
    if (/[0-9]/.test(password)) strength += 20;
    if (/[^A-Za-z0-9]/.test(password)) strength += 10;
    
    const progressBar = document.getElementById('password-strength');
    const strengthText = document.getElementById('password-strength-text');
    
    progressBar.style.width = strength + '%';
    progressBar.setAttribute('aria-valuenow', strength);
    
    if (strength < 40) {
        progressBar.className = 'progress-bar bg-danger';
        feedback = '密码强度：弱';
    } else if (strength < 70) {
        progressBar.className = 'progress-bar bg-warning';
        feedback = '密码强度：中等';
    } else {
        progressBar.className = 'progress-bar bg-success';
        feedback = '密码强度：强';
    }
    
    strengthText.textContent = feedback;
}

// 密码确认检测
function checkPasswordMatch() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const confirmInput = document.getElementById('confirm_password');
    
    if (confirmPassword && newPassword !== confirmPassword) {
        confirmInput.setCustomValidity('两次输入的密码不一致');
        confirmInput.classList.add('is-invalid');
    } else {
        confirmInput.setCustomValidity('');
        confirmInput.classList.remove('is-invalid');
    }
}

// 页面加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    
    newPasswordInput.addEventListener('input', function() {
        checkPasswordStrength(this.value);
        checkPasswordMatch();
    });
    
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);
    
    // 表单提交验证
    document.querySelector('form').addEventListener('submit', function(e) {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        
        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('两次输入的密码不一致，请重新输入');
            return false;
        }
        
        if (newPassword.length < 6) {
            e.preventDefault();
            alert('密码长度至少6位');
            return false;
        }
    });
});
</script>
{% endblock %}
