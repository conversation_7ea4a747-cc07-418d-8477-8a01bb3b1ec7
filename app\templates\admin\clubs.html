{% extends "admin_base.html" %}

{% block title %}社团管理 - 管理后台{% endblock %}

{% block content %}
<!-- 搜索功能 -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">搜索社团</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search" name="search"
                           placeholder="输入社团名称" value="{{ search_query or '' }}">
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{{ url_for('admin.clubs') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>重置
                    </a>
                </div>
            </div>
            <div class="col-md-5">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('admin.create_club') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>创建社团
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 社团列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="fas fa-layer-group me-2"></i>社团列表
            <span class="badge bg-primary ms-2">{{ pagination.total }}个</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if clubs %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="5%">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th width="25%">社团信息</th>
                        <th width="10%">类别</th>
                        <th width="15%">会长</th>
                        <th width="10%">成员数</th>
                        <th width="15%">成立时间</th>
                        <th width="10%">状态</th>
                        <th width="15%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for club in clubs %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input club-checkbox" 
                                   value="{{ club.ClubID }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="club-avatar me-3">
                                    {{ club.ClubName[0] }}
                                </div>
                                <div>
                                    <div class="fw-bold">{{ club.ClubName }}</div>
                                    {% if club.Description %}
                                    <div class="text-muted small">{{ club.Description }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-primary">
                                社团
                            </span>
                        </td>
                        <td>
                            {% if club.president %}
                            <div class="d-flex align-items-center">
                                <div class="user-avatar-sm me-2">
                                    {{ club.president.Name[0] if club.president.Name else club.president.Username[0] }}
                                </div>
                                <div>
                                    <div class="small fw-bold">{{ club.president.Name or club.president.Username }}</div>
                                    <div class="text-muted" style="font-size: 0.75rem;">@{{ club.president.Username }}</div>
                                </div>
                            </div>
                            {% else %}
                            <span class="text-muted">未设置</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="fw-bold">{{ club.CurrentMembers or 0 }}</span>
                            <span class="text-muted">
                                {% if club.MaxMembers %}
                                / {{ club.MaxMembers }}
                                {% else %}
                                / ∞
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            <span class="text-muted small">
                                {{ club.FoundationDate.strftime('%Y-%m-%d') if club.FoundationDate else '未知' }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if club.Status == '活跃' else 'warning' if club.Status == '暂停' else 'danger' }}">
                                {{ club.Status }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('admin.edit_club', club_id=club.ClubID) }}"
                                   class="btn btn-outline-primary"
                                   data-bs-toggle="tooltip" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('admin.club_members', club_id=club.ClubID) }}"
                                   class="btn btn-outline-secondary"
                                   data-bs-toggle="tooltip" title="成员管理">
                                    <i class="fas fa-users"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="deleteClub('{{ club.ClubID }}', '{{ club.ClubName }}')"
                                        data-bs-toggle="tooltip" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-layer-group text-muted" style="font-size: 4rem;"></i>
            <h5 class="mt-3 text-muted">暂无社团数据</h5>
            <p class="text-muted">点击上方"创建社团"按钮创建第一个社团</p>
            <a href="{{ url_for('admin.create_club') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>创建社团
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<nav aria-label="社团列表分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.clubs', page=pagination.prev_num, search=search_query) }}">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        {% endif %}

        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.clubs', page=page_num, search=search_query) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}

        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.clubs', page=pagination.next_num, search=search_query) }}">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- 批量操作 -->
<div class="card border-0 shadow-sm mt-4" id="batchActions" style="display: none;">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <span class="text-muted">已选择 <span id="selectedCount">0</span> 个社团</span>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-warning" onclick="batchChangeStatus()">
                    <i class="fas fa-toggle-on me-1"></i>批量修改状态
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="batchDelete()">
                    <i class="fas fa-trash me-1"></i>批量删除
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="exportSelected()">
                    <i class="fas fa-download me-1"></i>导出选中
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.club-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.user-avatar-sm {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.7rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 全选/取消全选
    $('#selectAll').change(function() {
        $('.club-checkbox').prop('checked', this.checked);
        updateBatchActions();
    });
    
    // 单个选择
    $('.club-checkbox').change(function() {
        updateBatchActions();
        
        // 更新全选状态
        var total = $('.club-checkbox').length;
        var checked = $('.club-checkbox:checked').length;
        $('#selectAll').prop('indeterminate', checked > 0 && checked < total);
        $('#selectAll').prop('checked', checked === total);
    });
    
    // 更新批量操作显示
    function updateBatchActions() {
        var selectedCount = $('.club-checkbox:checked').length;
        $('#selectedCount').text(selectedCount);
        
        if (selectedCount > 0) {
            $('#batchActions').show();
        } else {
            $('#batchActions').hide();
        }
    }
});

// 删除社团
function deleteClub(clubId, clubName) {
    if (confirm(`确定要删除社团"${clubName}"吗？此操作不可恢复。`)) {
        // 发送删除请求
        fetch(`/admin/clubs/${clubId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败：' + error.message);
        });
    }
}

// 批量删除
function batchDelete() {
    var selectedIds = $('.club-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要删除的社团');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${selectedIds.length} 个社团吗？此操作不可恢复。`)) {
        // 发送批量删除请求
        fetch('/admin/clubs/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({club_ids: selectedIds})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('批量删除失败：' + data.message);
            }
        })
        .catch(error => {
            alert('批量删除失败：' + error.message);
        });
    }
}

// 批量修改状态
function batchChangeStatus() {
    var selectedIds = $('.club-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要修改的社团');
        return;
    }
    
    var newStatus = prompt('请输入新状态（活跃/暂停/解散）：');
    if (newStatus && ['活跃', '暂停', '解散'].includes(newStatus)) {
        // 发送批量修改请求
        fetch('/admin/clubs/batch-change-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                club_ids: selectedIds,
                new_status: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('批量修改失败：' + data.message);
            }
        })
        .catch(error => {
            alert('批量修改失败：' + error.message);
        });
    }
}

// 导出数据
function exportClubs() {
    window.open('/admin/clubs/export', '_blank');
}

// 导出选中
function exportSelected() {
    var selectedIds = $('.club-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要导出的社团');
        return;
    }
    
    var form = document.createElement('form');
    form.method = 'POST';
    form.action = '/admin/clubs/export-selected';
    
    var csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '{{ csrf_token() }}';
    form.appendChild(csrfInput);
    
    var idsInput = document.createElement('input');
    idsInput.type = 'hidden';
    idsInput.name = 'club_ids';
    idsInput.value = JSON.stringify(selectedIds);
    form.appendChild(idsInput);
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
</script>
{% endblock %}
