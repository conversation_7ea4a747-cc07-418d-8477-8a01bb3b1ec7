{% extends "admin_base.html" %}

{% block title %}添加场馆{% endblock %}

{% block page_title %}添加场馆{% endblock %}

{% block page_actions %}
<a href="{{ url_for('admin.venues') }}" class="btn btn-secondary">
    <i class="fas fa-arrow-left me-1"></i>返回列表
</a>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="form-container">
            <form method="POST" id="venueForm" data-validate="true">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="row">
                    <!-- 基本信息 -->
                    <div class="col-12">
                        <h5 class="text-primary mb-3 border-bottom pb-2">
                            <i class="fas fa-info-circle me-2"></i>基本信息
                        </h5>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="venue_name" class="form-label">
                            <i class="fas fa-building me-1"></i>场馆名称 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="venue_name" name="venue_name" 
                               required maxlength="100" placeholder="请输入场馆名称"
                               data-rules='{"required": true, "maxLength": 100}'>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="venue_type" class="form-label">
                            <i class="fas fa-tag me-1"></i>场馆类型 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="venue_type" name="venue_type" required
                                data-rules='{"required": true}'>
                            <option value="">请选择场馆类型</option>
                            <option value="室内">室内</option>
                            <option value="室外">室外</option>
                            <option value="多功能厅">多功能厅</option>
                            <option value="体育馆">体育馆</option>
                            <option value="其他">其他</option>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="location" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>场馆位置 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="location" name="location" 
                               required maxlength="100" placeholder="如：教学楼A区"
                               data-rules='{"required": true, "maxLength": 100}'>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="capacity" class="form-label">
                            <i class="fas fa-users me-1"></i>场馆容量
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="capacity" name="capacity" 
                                   min="1" max="10000" placeholder="请输入容量"
                                   data-rules='{"min": 1, "max": 10000}'>
                            <span class="input-group-text">人</span>
                        </div>
                        <div class="form-text">场馆最大容纳人数</div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="col-12 mb-3">
                        <label for="address" class="form-label">
                            <i class="fas fa-location-arrow me-1"></i>详细地址 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="address" name="address" 
                               required maxlength="200" placeholder="请输入详细地址"
                               data-rules='{"required": true, "maxLength": 200}'>
                        <div class="form-text">具体的场馆地址，便于用户查找</div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <!-- 联系信息 -->
                    <div class="col-12 mt-4">
                        <h5 class="text-primary mb-3 border-bottom pb-2">
                            <i class="fas fa-phone me-2"></i>联系信息
                        </h5>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="contact_phone" class="form-label">
                            <i class="fas fa-phone me-1"></i>联系电话
                        </label>
                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                               pattern="[0-9]{7,15}" placeholder="请输入联系电话"
                               data-rules='{"pattern": "^[0-9]{7,15}$", "message": "请输入7-15位数字"}'>
                        <div class="form-text">7-15位数字，用于场馆预订咨询</div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="availab_time" class="form-label">
                            <i class="fas fa-clock me-1"></i>可用时间 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="availab_time" name="availab_time" 
                               required maxlength="20" placeholder="如：周一至周日 8:00-22:00"
                               data-rules='{"required": true, "maxLength": 20}'>
                        <div class="form-text">场馆的开放时间</div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <!-- 预览区域 -->
                    <div class="col-12 mt-4">
                        <h5 class="text-primary mb-3 border-bottom pb-2">
                            <i class="fas fa-eye me-2"></i>信息预览
                        </h5>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 id="preview-name" class="text-primary">场馆名称</h6>
                                        <p class="mb-1">
                                            <i class="fas fa-tag me-1"></i>
                                            <span id="preview-type">场馆类型</span>
                                        </p>
                                        <p class="mb-1">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            <span id="preview-location">场馆位置</span>
                                        </p>
                                        <p class="mb-0">
                                            <i class="fas fa-location-arrow me-1"></i>
                                            <span id="preview-address">详细地址</span>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1">
                                            <i class="fas fa-users me-1"></i>
                                            容量：<span id="preview-capacity">未设置</span>
                                        </p>
                                        <p class="mb-1">
                                            <i class="fas fa-phone me-1"></i>
                                            电话：<span id="preview-phone">未设置</span>
                                        </p>
                                        <p class="mb-0">
                                            <i class="fas fa-clock me-1"></i>
                                            时间：<span id="preview-time">可用时间</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="col-12 mt-4">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin.venues') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <div>
                                <button type="reset" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-undo me-1"></i>重置
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>保存场馆
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 实时预览功能
    function updatePreview() {
        $('#preview-name').text($('#venue_name').val() || '场馆名称');
        $('#preview-type').text($('#venue_type').val() || '场馆类型');
        $('#preview-location').text($('#location').val() || '场馆位置');
        $('#preview-address').text($('#address').val() || '详细地址');
        $('#preview-capacity').text($('#capacity').val() ? $('#capacity').val() + '人' : '未设置');
        $('#preview-phone').text($('#contact_phone').val() || '未设置');
        $('#preview-time').text($('#availab_time').val() || '可用时间');
    }
    
    // 绑定输入事件
    $('#venue_name, #venue_type, #location, #address, #capacity, #contact_phone, #availab_time').on('input change', updatePreview);
    
    // 表单验证
    $('#venueForm').on('submit', function(e) {
        let isValid = true;
        
        // 验证必填字段
        const requiredFields = ['venue_name', 'venue_type', 'location', 'address', 'availab_time'];
        requiredFields.forEach(function(fieldId) {
            const $field = $('#' + fieldId);
            if (!$field.val().trim()) {
                $field.addClass('is-invalid');
                isValid = false;
            } else {
                $field.removeClass('is-invalid').addClass('is-valid');
            }
        });
        
        // 验证电话号码
        const phone = $('#contact_phone').val();
        if (phone && !/^[0-9]{7,15}$/.test(phone)) {
            $('#contact_phone').addClass('is-invalid');
            isValid = false;
        } else if (phone) {
            $('#contact_phone').removeClass('is-invalid').addClass('is-valid');
        }
        
        // 验证容量
        const capacity = $('#capacity').val();
        if (capacity && (capacity < 1 || capacity > 10000)) {
            $('#capacity').addClass('is-invalid');
            isValid = false;
        } else if (capacity) {
            $('#capacity').removeClass('is-invalid').addClass('is-valid');
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('请检查表单中的错误');
        }
    });
    
    // 重置表单时清除预览
    $('button[type="reset"]').on('click', function() {
        setTimeout(updatePreview, 100);
        $('.form-control, .form-select').removeClass('is-valid is-invalid');
    });
    
    // 初始化预览
    updatePreview();
});
</script>
{% endblock %}
