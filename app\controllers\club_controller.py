#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
社团控制器
处理社团管理相关的业务逻辑，主要服务于会长功能
严格基于Clubs、MemberClub、Activities、ApprovalRequests表的数据结构
"""

from app import db
from app.models.club import Club
from app.models.member import Member
from app.models.member_club import MemberClub
from app.models.activity import Activity
from app.models.approval import ApprovalRequest
from app.models.venue import Venue
from app.utils.helpers import generate_uuid, validate_website
from datetime import datetime

class ClubController:
    """社团控制器类"""
    
    @staticmethod
    def get_president_dashboard_data(president_id):
        """
        获取会长仪表板数据

        Args:
            president_id: 会长ID

        Returns:
            dict: 仪表板数据
        """
        from app.models.member import Member
        from app.models.venue import Venue

        # 获取会长管理的社团
        clubs = Club.query.filter_by(PresidentID=president_id).all()

        if not clubs:
            return {
                'clubs': [],
                'pending_applications': [],
                'recent_activities': [],
                'upcoming_activities': [],
                'recent_members': [],
                'stats': {
                    'total_clubs': 0,
                    'total_members': 0,
                    'pending_requests': 0,
                    'total_activities': 0
                }
            }

        club_ids = [club.ClubID for club in clubs]

        # 获取待审批的入会申请（包含关联的成员信息）
        pending_applications = MemberClub.query.join(Member).filter(
            MemberClub.ClubID.in_(club_ids),
            MemberClub.Status == '待审批'
        ).order_by(MemberClub.ApplyTime.asc()).limit(5).all()

        # 获取最近的活动（包含关联的场馆信息）
        recent_activities = Activity.query.join(Venue).filter(
            Activity.ClubID.in_(club_ids)
        ).order_by(Activity.StartTime.desc()).limit(5).all()

        # 获取即将举行的活动（包含关联的场馆信息）
        upcoming_activities = Activity.query.join(Venue).filter(
            Activity.ClubID.in_(club_ids),
            Activity.Status == '计划中',
            Activity.StartTime > datetime.now()
        ).order_by(Activity.StartTime.asc()).limit(5).all()

        # 获取最近加入的成员（已批准），包含完整的成员信息
        recent_members = MemberClub.query.join(Member).filter(
            MemberClub.ClubID.in_(club_ids),
            MemberClub.Status == '已批准'
        ).order_by(MemberClub.ApprovalTime.desc()).limit(8).all()

        # 获取待审批的成员申请
        pending_members = MemberClub.query.join(Member).filter(
            MemberClub.ClubID.in_(club_ids),
            MemberClub.Status == '待审批'
        ).order_by(MemberClub.ApplyTime.asc()).limit(5).all()

        # 统计信息
        total_members = MemberClub.query.filter(
            MemberClub.ClubID.in_(club_ids),
            MemberClub.Status == '已批准'
        ).count()

        total_activities = Activity.query.filter(
            Activity.ClubID.in_(club_ids)
        ).count()

        # 获取第一个社团作为主要社团信息（如果会长管理多个社团）
        my_club = clubs[0] if clubs else None

        return {
            'clubs': clubs,
            'pending_applications': pending_applications,
            'recent_activities': recent_activities,
            'upcoming_activities': upcoming_activities,
            'recent_members': recent_members,
            'pending_members': pending_members,
            'my_club': my_club,
            'stats': {
                'total_clubs': len(clubs),
                'total_members': total_members,
                'pending_requests': len(pending_applications),
                'total_activities': total_activities
            }
        }
    
    @staticmethod
    def get_club_members(club_id, status=None):
        """
        获取社团成员列表

        Args:
            club_id: 社团ID
            status: 状态过滤

        Returns:
            list: 成员关系列表
        """
        # 使用join确保关联的成员信息能被正确加载
        query = MemberClub.query.join(Member).filter(MemberClub.ClubID == club_id)

        if status:
            query = query.filter(MemberClub.Status == status)

        return query.order_by(MemberClub.ApplyTime.desc()).all()
    
    @staticmethod
    def approve_member_application(president_id, record_id, comments=None):
        """
        批准会员申请
        
        Args:
            president_id: 会长ID
            record_id: 申请记录ID
            comments: 审批意见
        
        Returns:
            tuple: (success: bool, message: str)
        """
        # 查找申请记录
        member_club = MemberClub.query.get(record_id)
        if not member_club:
            return False, '申请记录不存在'
        
        # 验证权限（必须是该社团的会长）
        club = Club.query.get(member_club.ClubID)
        if not club or club.PresidentID != president_id:
            return False, '您没有权限审批该申请'
        
        # 检查申请状态
        if member_club.Status != '待审批':
            return False, '该申请已被处理'
        
        # 检查社团是否还能接受新成员
        if not club.can_accept_new_member():
            return False, '社团已满员，无法批准新成员'
        
        try:
            # 批准申请
            member_club.approve(president_id, comments)
            db.session.commit()
            
            member_name = member_club.member.Name if member_club.member else '会员'
            return True, f'已批准"{member_name}"的入会申请'
            
        except Exception as e:
            db.session.rollback()
            return False, f'批准申请失败：{str(e)}'
    
    @staticmethod
    def reject_member_application(president_id, record_id, comments=None):
        """
        拒绝会员申请
        
        Args:
            president_id: 会长ID
            record_id: 申请记录ID
            comments: 拒绝理由
        
        Returns:
            tuple: (success: bool, message: str)
        """
        # 查找申请记录
        member_club = MemberClub.query.get(record_id)
        if not member_club:
            return False, '申请记录不存在'
        
        # 验证权限
        club = Club.query.get(member_club.ClubID)
        if not club or club.PresidentID != president_id:
            return False, '您没有权限审批该申请'
        
        # 检查申请状态
        if member_club.Status != '待审批':
            return False, '该申请已被处理'
        
        try:
            # 拒绝申请
            member_club.reject(president_id, comments)
            db.session.commit()
            
            member_name = member_club.member.Name if member_club.member else '会员'
            return True, f'已拒绝"{member_name}"的入会申请'
            
        except Exception as e:
            db.session.rollback()
            return False, f'拒绝申请失败：{str(e)}'
    
    @staticmethod
    def create_activity(president_id, club_id, activity_data):
        """
        创建活动
        
        Args:
            president_id: 会长ID
            club_id: 社团ID
            activity_data: 活动数据
        
        Returns:
            tuple: (success: bool, message: str, activity_id: str)
        """
        # 验证权限
        club = Club.query.get(club_id)
        if not club or club.PresidentID != president_id:
            return False, '您没有权限为该社团创建活动', None
        
        # 验证必填字段
        required_fields = ['activity_name', 'activity_type', 'start_time', 'end_time', 'venue_id']
        for field in required_fields:
            if not activity_data.get(field):
                return False, f'{field}不能为空', None
        
        # 验证活动名称长度
        if len(activity_data['activity_name']) > 100:
            return False, '活动名称长度不能超过100个字符', None
        
        # 验证活动类型
        valid_types = ['讲座', '比赛', '演出', '展览', '会议', '其他']
        if activity_data['activity_type'] not in valid_types:
            return False, '活动类型无效', None
        
        # 验证描述长度
        description = activity_data.get('description', '')
        if description and len(description) > 500:
            return False, '活动描述长度不能超过500个字符', None
        
        # 验证参与人数限制
        participant_limit = activity_data.get('participant_limit')
        if participant_limit is not None:
            try:
                participant_limit = int(participant_limit)
                if participant_limit < 0:
                    return False, '参与人数限制不能为负数', None
            except ValueError:
                return False, '参与人数限制必须为数字', None
        
        # 验证时间
        try:
            start_time = datetime.fromisoformat(activity_data['start_time'])
            end_time = datetime.fromisoformat(activity_data['end_time'])
        except ValueError:
            return False, '时间格式错误', None
        
        if end_time <= start_time:
            return False, '结束时间必须晚于开始时间', None
        
        # 验证场馆存在
        venue = Venue.query.get(activity_data['venue_id'])
        if not venue:
            return False, '场馆不存在', None
        
        # 检查场馆可用性
        if not venue.check_availability(start_time, end_time):
            return False, '该时间段场馆已被占用', None
        
        try:
            # 创建活动
            activity = Activity(
                ActivityID=generate_uuid(),
                ClubID=club_id,
                ActivityName=activity_data['activity_name'],
                ActivityType=activity_data['activity_type'],
                StartTime=start_time,
                EndTime=end_time,
                Description=description or None,
                OrganizerID=president_id,
                VenueID=activity_data['venue_id'],
                ParticipantLimit=participant_limit,
                Status='计划中',
                ActualParticipant=0
            )
            
            db.session.add(activity)
            
            # 创建活动审批申请
            approval_request = ApprovalRequest.create_activity_request(
                president_id,
                activity.ActivityID,
                activity_data.get('comments')
            )
            
            db.session.add(approval_request)
            db.session.commit()
            
            return True, f'活动"{activity.ActivityName}"创建成功，已提交审批', activity.ActivityID
            
        except Exception as e:
            db.session.rollback()
            return False, f'创建活动失败：{str(e)}', None
    
    @staticmethod
    def update_club_info(president_id, club_id, club_data):
        """
        更新社团信息
        
        Args:
            president_id: 会长ID
            club_id: 社团ID
            club_data: 社团数据
        
        Returns:
            tuple: (success: bool, message: str)
        """
        # 验证权限
        club = Club.query.get(club_id)
        if not club or club.PresidentID != president_id:
            return False, '您没有权限修改该社团信息'
        
        try:
            # 更新允许修改的字段
            if 'website' in club_data:
                website = club_data['website'].strip() or None
                if website and not validate_website(website):
                    return False, '网站URL格式不正确（必须以http开头）'
                club.Website = website
            
            if 'max_members' in club_data and club_data['max_members']:
                try:
                    max_members = int(club_data['max_members'])
                    if max_members <= 0:
                        return False, '最大成员数必须大于0'
                    if max_members < club.CurrentMembers:
                        return False, f'最大成员数不能小于当前成员数({club.CurrentMembers})'
                    club.MaxMembers = max_members
                except ValueError:
                    return False, '最大成员数必须为数字'
            
            if 'category' in club_data:
                category = club_data['category'].strip() or None
                if category:
                    try:
                        if int(category) < 0:
                            return False, '分类编号不能为负数'
                    except ValueError:
                        return False, '分类编号必须为数字'
                club.Category = category
            
            db.session.commit()
            return True, '社团信息更新成功'
            
        except Exception as e:
            db.session.rollback()
            return False, f'更新失败：{str(e)}'
    
    @staticmethod
    def get_club_activities(club_id, status=None):
        """
        获取社团活动列表

        Args:
            club_id: 社团ID
            status: 状态过滤

        Returns:
            list: 活动列表
        """
        # 使用join确保关联的场馆信息能被正确加载
        query = Activity.query.join(Venue).filter(Activity.ClubID == club_id)

        if status:
            query = query.filter(Activity.Status == status)

        return query.order_by(Activity.StartTime.desc()).all()
    
    @staticmethod
    def get_available_venues():
        """
        获取可用场馆列表

        Returns:
            list: 场馆列表
        """
        return Venue.query.order_by(Venue.VenueName.asc()).all()

    @staticmethod
    def get_club_members_with_pagination(club_id, page=1, per_page=10, status=None, search=None):
        """
        分页获取社团成员列表

        Args:
            club_id: 社团ID
            page: 页码
            per_page: 每页数量
            status: 状态筛选
            search: 搜索关键词

        Returns:
            dict: 包含成员列表和分页信息
        """
        # 构建查询
        query = MemberClub.query.filter_by(ClubID=club_id)

        # 状态筛选
        if status and status != 'all':
            query = query.filter_by(Status=status)

        # 搜索功能
        if search:
            from app.models.member import Member
            query = query.join(Member).filter(
                db.or_(
                    Member.Name.like(f'%{search}%'),
                    Member.Username.like(f'%{search}%'),
                    Member.Phone.like(f'%{search}%'),
                    Member.College.like(f'%{search}%'),
                    Member.Specialty.like(f'%{search}%')
                )
            )

        # 分页查询
        pagination = query.order_by(MemberClub.ApplyTime.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return {
            'members': pagination.items,
            'pagination': pagination
        }

    @staticmethod
    def get_member_application_details(record_id):
        """
        获取成员申请详情

        Args:
            record_id: 申请记录ID

        Returns:
            MemberClub: 申请记录对象
        """
        return MemberClub.query.get(record_id)

    @staticmethod
    def update_member_info(president_id, record_id, member_data):
        """
        更新成员信息（仅限已批准的成员）

        Args:
            president_id: 会长ID
            record_id: 成员关系记录ID
            member_data: 成员数据

        Returns:
            tuple: (success: bool, message: str)
        """
        # 查找成员关系记录
        member_club = MemberClub.query.get(record_id)
        if not member_club:
            return False, '成员记录不存在'

        # 验证权限
        club = Club.query.get(member_club.ClubID)
        if not club or club.PresidentID != president_id:
            return False, '您没有权限修改该成员信息'

        # 只能修改已批准的成员
        if member_club.Status != '已批准':
            return False, '只能修改已批准的成员信息'

        try:
            # 更新成员基本信息
            member = member_club.member
            if not member:
                return False, '成员信息不存在'

            # 更新允许修改的字段
            if 'specialty' in member_data and member_data['specialty'].strip():
                member.Specialty = member_data['specialty'].strip()

            # 更新成员关系备注信息
            if 'application_reason' in member_data:
                member_club.ApplicationReason = member_data['application_reason'].strip()

            db.session.commit()
            return True, '成员信息更新成功'

        except Exception as e:
            db.session.rollback()
            return False, f'更新失败：{str(e)}'

    @staticmethod
    def remove_member_from_club(president_id, record_id, reason=None):
        """
        将成员从社团中移除（会长操作）

        Args:
            president_id: 会长ID
            record_id: 成员关系记录ID
            reason: 移除理由

        Returns:
            tuple: (success: bool, message: str)
        """
        # 查找成员关系记录
        member_club = MemberClub.query.get(record_id)
        if not member_club:
            return False, '成员记录不存在'

        # 验证权限
        club = Club.query.get(member_club.ClubID)
        if not club or club.PresidentID != president_id:
            return False, '您没有权限移除该成员'

        # 只能移除已批准的成员
        if member_club.Status != '已批准':
            return False, '只能移除已批准的成员'

        # 不能移除自己
        if member_club.MemberID == president_id:
            return False, '不能移除自己'

        try:
            # 执行移除操作
            member_club.withdraw(reason)

            member_name = member_club.member.Name if member_club.member else '会员'
            return True, f'已将"{member_name}"从社团中移除'

        except Exception as e:
            db.session.rollback()
            return False, f'移除失败：{str(e)}'
