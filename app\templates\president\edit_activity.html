{% extends "admin_base.html" %}

{% block title %}编辑活动 - 会长后台{% endblock %}

{% block breadcrumb_title %}编辑活动{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">编辑活动</h2>
            <p class="text-muted mb-0">修改 <span class="badge bg-primary">{{ activity.ActivityName }}</span> 的信息</p>
        </div>
        <div>
            <a href="{{ url_for('president.activity_detail', activity_id=activity.ActivityID) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回活动详情
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2 text-primary"></i>活动信息
                    </h5>
                </div>
                <div class="card-body">
                    <form id="editActivityForm" method="POST" action="{{ url_for('president.edit_activity', activity_id=activity.ActivityID) }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <!-- 基本信息 -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="activityName" class="form-label">活动名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="activityName" name="activity_name" 
                                       value="{{ activity.ActivityName }}" placeholder="请输入活动名称" required>
                            </div>
                            <div class="col-md-4">
                                <label for="activityType" class="form-label">活动类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="activityType" name="activity_type" required>
                                    <option value="">请选择类型</option>
                                    <option value="讲座" {% if activity.ActivityType == '讲座' %}selected{% endif %}>讲座</option>
                                    <option value="比赛" {% if activity.ActivityType == '比赛' %}selected{% endif %}>比赛</option>
                                    <option value="培训" {% if activity.ActivityType == '培训' %}selected{% endif %}>培训</option>
                                    <option value="展览" {% if activity.ActivityType == '展览' %}selected{% endif %}>展览</option>
                                    <option value="演出" {% if activity.ActivityType == '演出' %}selected{% endif %}>演出</option>
                                    <option value="会议" {% if activity.ActivityType == '会议' %}selected{% endif %}>会议</option>
                                    <option value="其他" {% if activity.ActivityType == '其他' %}selected{% endif %}>其他</option>
                                </select>
                            </div>
                        </div>

                        <!-- 时间安排 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="startTime" class="form-label">开始时间 <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="startTime" name="start_time" 
                                       value="{{ activity.StartTime.strftime('%Y-%m-%dT%H:%M') if activity.StartTime else '' }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="endTime" class="form-label">结束时间 <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="endTime" name="end_time" 
                                       value="{{ activity.EndTime.strftime('%Y-%m-%dT%H:%M') if activity.EndTime else '' }}" required>
                            </div>
                        </div>

                        <!-- 地点和人数 -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="venue" class="form-label">活动地点 <span class="text-danger">*</span></label>
                                <select class="form-select" id="venue" name="venue_id" required>
                                    <option value="">请选择场馆</option>
                                    {% for venue in venues %}
                                    <option value="{{ venue.VenueID }}" data-capacity="{{ venue.Capacity }}"
                                            {% if activity.VenueID == venue.VenueID %}selected{% endif %}>
                                        {{ venue.VenueName }} (容量: {{ venue.Capacity }}人)
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="participantLimit" class="form-label">人数限制</label>
                                <input type="number" class="form-control" id="participantLimit" name="participant_limit" 
                                       value="{{ activity.ParticipantLimit or '' }}" placeholder="不限制请留空" min="1">
                                <small class="text-muted">留空表示不限制人数</small>
                            </div>
                        </div>

                        <!-- 活动描述 -->
                        <div class="mb-3">
                            <label for="description" class="form-label">活动描述</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="请详细描述活动内容、目的、流程等...">{{ activity.Description or '' }}</textarea>
                        </div>

                        <!-- 注意：数据库中Activities表没有Requirements字段，移除参与要求输入框 -->

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                                <i class="fas fa-times me-1"></i>取消
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 活动信息 -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-info"></i>活动状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-1"></i>编辑说明</h6>
                        <ul class="mb-0 small">
                            <li>只能编辑状态为"计划中"的活动</li>
                            <li>修改时间可能影响已报名的参与者</li>
                            <li>修改地点需要重新确认场馆可用性</li>
                            <li>减少人数限制可能影响已报名用户</li>
                        </ul>
                    </div>

                    <div class="mt-3">
                        <h6>当前状态</h6>
                        <div class="border rounded p-2 bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>活动状态</span>
                                <span class="badge bg-warning">{{ activity.Status }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <span>参与人数</span>
                                <span class="badge bg-info">{{ activity.ActualParticipant }}人</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <span>创建时间</span>
                                <small class="text-muted">{{ activity.CreateTime.strftime('%Y-%m-%d') if activity.CreateTime else '未知' }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <h6>所属社团</h6>
                        <div class="border rounded p-2 bg-light">
                            <div><strong>{{ activity.club.ClubName if activity.club else '未知社团' }}</strong></div>
                            <small class="text-muted">{{ activity.club.Description if activity.club else '暂无描述' }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert {
    border: none;
    border-radius: 8px;
}

.card {
    border-radius: 12px;
}

.btn {
    border-radius: 6px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 设置最小时间为当前时间
    const now = new Date();
    const minDateTime = now.toISOString().slice(0, 16);
    $('#startTime, #endTime').attr('min', minDateTime);
    
    // 开始时间变化时，更新结束时间的最小值
    $('#startTime').change(function() {
        const startTime = $(this).val();
        if (startTime) {
            $('#endTime').attr('min', startTime);
            
            // 如果结束时间早于开始时间，清空结束时间
            const endTime = $('#endTime').val();
            if (endTime && endTime <= startTime) {
                $('#endTime').val('');
            }
        }
    });
    
    // 场馆选择变化时，更新人数限制提示
    $('#venue').change(function() {
        const selectedOption = $(this).find('option:selected');
        const capacity = selectedOption.data('capacity');
        
        if (capacity) {
            $('#participantLimit').attr('max', capacity);
            $('#participantLimit').attr('placeholder', `建议不超过${capacity}人`);
        } else {
            $('#participantLimit').removeAttr('max');
            $('#participantLimit').attr('placeholder', '不限制请留空');
        }
    });
    
    // 初始化场馆容量提示
    $('#venue').trigger('change');
    
    // 表单验证
    $('#editActivityForm').submit(function(e) {
        const startTime = new Date($('#startTime').val());
        const endTime = new Date($('#endTime').val());
        
        if (endTime <= startTime) {
            e.preventDefault();
            alert('结束时间必须晚于开始时间');
            return false;
        }
        
        const participantLimit = parseInt($('#participantLimit').val());
        const venueCapacity = parseInt($('#venue').find('option:selected').data('capacity'));
        const currentParticipants = {{ activity.ActualParticipant }};
        
        if (participantLimit && venueCapacity && participantLimit > venueCapacity) {
            e.preventDefault();
            alert(`人数限制不能超过场馆容量(${venueCapacity}人)`);
            return false;
        }
        
        if (participantLimit && currentParticipants > participantLimit) {
            e.preventDefault();
            alert(`人数限制不能少于当前参与人数(${currentParticipants}人)`);
            return false;
        }
        
        return true;
    });
});
</script>
{% endblock %}
