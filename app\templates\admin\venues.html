{% extends "admin_base.html" %}

{% block title %}场馆管理{% endblock %}

{% block page_title %}场馆管理{% endblock %}



{% block content %}
<!-- 搜索和过滤器 -->
<div class="search-filter-section">
    <form method="GET" class="row g-3">
        <div class="col-md-4">
            <label for="search" class="form-label">搜索场馆</label>
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="输入场馆名称或位置" value="{{ request.args.get('search', '') }}">
            </div>
        </div>
        <div class="col-md-3">
            <label for="venue_type" class="form-label">场馆类型</label>
            <select class="form-select" id="venue_type" name="venue_type">
                <option value="">全部类型</option>
                <option value="室内" {% if request.args.get('venue_type') == '室内' %}selected{% endif %}>室内</option>
                <option value="室外" {% if request.args.get('venue_type') == '室外' %}selected{% endif %}>室外</option>
                <option value="多功能厅" {% if request.args.get('venue_type') == '多功能厅' %}selected{% endif %}>多功能厅</option>
                <option value="体育馆" {% if request.args.get('venue_type') == '体育馆' %}selected{% endif %}>体育馆</option>
                <option value="其他" {% if request.args.get('venue_type') == '其他' %}selected{% endif %}>其他</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="capacity_range" class="form-label">容量范围</label>
            <select class="form-select" id="capacity_range" name="capacity_range">
                <option value="">全部容量</option>
                <option value="0-50" {% if request.args.get('capacity_range') == '0-50' %}selected{% endif %}>50人以下</option>
                <option value="50-100" {% if request.args.get('capacity_range') == '50-100' %}selected{% endif %}>50-100人</option>
                <option value="100-200" {% if request.args.get('capacity_range') == '100-200' %}selected{% endif %}>100-200人</option>
                <option value="200+" {% if request.args.get('capacity_range') == '200+' %}selected{% endif %}>200人以上</option>
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search me-1"></i>搜索
                </button>
                <a href="{{ url_for('admin.create_venue') }}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>添加场馆
                </a>
            </div>
        </div>
    </form>
</div>

<!-- 场馆列表 -->
<div class="data-table">
    <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
        <h5 class="mb-0">
            <i class="bi bi-building me-2"></i>场馆列表
            <span class="badge bg-secondary ms-2">{{ pagination.total }}</span>
        </h5>

    </div>
    
    <div class="table-responsive">
        <table class="table table-hover sortable-table mb-0">
            <thead>
                <tr>
                    <th data-sort="0">
                        <input type="checkbox" class="form-check-input select-all">
                    </th>
                    <th data-sort="1">场馆名称</th>
                    <th data-sort="2">类型</th>
                    <th data-sort="3">位置</th>
                    <th data-sort="4">容量</th>
                    <th data-sort="5">可用时间</th>
                    <th data-sort="6">联系电话</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for venue in venues %}
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input select-item" 
                               value="{{ venue.VenueID }}">
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center me-2" 
                                 style="width: 32px; height: 32px;">
                                <i class="bi bi-building"></i>
                            </div>
                            <div>
                                <strong>{{ venue.VenueName }}</strong>
                                {% if venue.Address %}
                                <br><small class="text-muted">{{ venue.Address }}</small>
                                {% endif %}
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-{{ loop.cycle('primary', 'success', 'warning', 'info', 'secondary') }}">
                            {{ venue.VenueType }}
                        </span>
                    </td>
                    <td>{{ venue.Location }}</td>
                    <td>
                        {% if venue.Capacity %}
                            <span class="badge bg-light text-dark">{{ venue.Capacity }}人</span>
                        {% else %}
                            <span class="text-muted">未设置</span>
                        {% endif %}
                    </td>
                    <td>
                        <small class="text-muted">{{ venue.AvailabTime }}</small>
                    </td>
                    <td>
                        {% if venue.ContactPhone %}
                            <a href="tel:{{ venue.ContactPhone }}" class="text-decoration-none">
                                {{ venue.ContactPhone }}
                            </a>
                        {% else %}
                            <span class="text-muted">未设置</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <a href="{{ url_for('admin.edit_venue', venue_id=venue.VenueID) }}"
                               class="btn btn-outline-primary"
                               data-bs-toggle="tooltip" title="编辑">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form method="POST" action="{{ url_for('admin.delete_venue', venue_id=venue.VenueID) }}"
                                  class="d-inline">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <button type="submit" class="btn btn-outline-danger"
                                        onclick="return confirm('确定要删除场馆{{ venue.VenueName }}吗？删除后无法恢复！')"
                                        data-bs-toggle="tooltip" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="8" class="text-center py-5">
                        <div class="empty-state">
                            <i class="fas fa-building"></i>
                            <h5>暂无场馆数据</h5>
                            <p>点击上方"添加场馆"按钮创建第一个场馆</p>
                            <a href="{{ url_for('admin.create_venue') }}" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-1"></i>添加场馆
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- 批量操作 -->
<div class="batch-actions" style="display: none;">
    <div class="d-flex justify-content-between align-items-center">
        <span>已选择 <strong class="selected-count">0</strong> 个场馆</span>
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-danger" onclick="batchDelete()">
                <i class="fas fa-trash me-1"></i>批量删除
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="exportSelected()">
                <i class="fas fa-download me-1"></i>导出选中
            </button>
        </div>
    </div>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<div class="pagination-wrapper">
    <nav aria-label="场馆列表分页">
        <ul class="pagination justify-content-center mb-0">
            {% if pagination.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.venues', page=pagination.prev_num, search=request.args.get('search', ''), venue_type=request.args.get('venue_type', ''), capacity_range=request.args.get('capacity_range', '')) }}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            {% endif %}

            {% for page_num in pagination.iter_pages() %}
                {% if page_num %}
                    {% if page_num != pagination.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.venues', page=page_num, search=request.args.get('search', ''), venue_type=request.args.get('venue_type', ''), capacity_range=request.args.get('capacity_range', '')) }}">
                                {{ page_num }}
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                    {% endif %}
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                {% endif %}
            {% endfor %}

            {% if pagination.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.venues', page=pagination.next_num, search=request.args.get('search', ''), venue_type=request.args.get('venue_type', ''), capacity_range=request.args.get('capacity_range', '')) }}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    
    <div class="text-center mt-2">
        <small class="text-muted">
            显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - 
            {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page < pagination.total else pagination.total }} 
            条，共 {{ pagination.total }} 条记录
        </small>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function batchDelete() {
    const selected = $('.select-item:checked');
    if (selected.length === 0) {
        alert('请先选择要删除的场馆');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${selected.length} 个场馆吗？删除后无法恢复！`)) {
        const venueIds = [];
        selected.each(function() {
            venueIds.push($(this).val());
        });
        
        // 这里可以实现批量删除的AJAX请求
        console.log('批量删除场馆:', venueIds);
        alert('批量删除功能开发中...');
    }
}

function exportToExcel() {
    // 导出Excel功能
    window.open('{{ url_for("admin.venues") }}?export=excel&' + new URLSearchParams({{ request.args.to_dict()|tojson }}));
}

function exportSelected() {
    const selected = $('.select-item:checked');
    if (selected.length === 0) {
        alert('请先选择要导出的场馆');
        return;
    }
    
    const venueIds = [];
    selected.each(function() {
        venueIds.push($(this).val());
    });
    
    // 导出选中场馆
    console.log('导出选中场馆:', venueIds);
    alert('导出功能开发中...');
}

$(document).ready(function() {
    // 初始化工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // 搜索框自动提交
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            $('#search').closest('form').submit();
        }, 1000);
    });
});
</script>
{% endblock %}
