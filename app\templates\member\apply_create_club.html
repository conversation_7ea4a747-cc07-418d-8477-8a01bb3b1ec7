{% extends "base.html" %}

{% block title %}申请创建社团{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 页面标题 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-plus-circle text-primary me-2"></i>申请创建社团
                        </h4>
                        <a href="{{ url_for('member.profile') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回个人中心
                        </a>
                    </div>
                </div>
            </div>

            <!-- 申请表单 -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-layer-group me-2 text-primary"></i>社团基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('member.apply_create_club') }}" id="applyClubForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <!-- 基本信息 -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label for="club_name" class="form-label">社团名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="club_name" name="club_name" 
                                       value="{{ request.form.get('club_name', '') }}" required maxlength="100">
                            </div>
                            <div class="col-md-6">
                                <label for="description" class="form-label">社团类别 <span class="text-danger">*</span></label>
                                <select class="form-select" id="description" name="description" required>
                                    <option value="">请选择类别</option>
                                    <option value="学术" {% if request.form.get('description') == '学术' %}selected{% endif %}>学术</option>
                                    <option value="体育" {% if request.form.get('description') == '体育' %}selected{% endif %}>体育</option>
                                    <option value="艺术" {% if request.form.get('description') == '艺术' %}selected{% endif %}>艺术</option>
                                    <option value="公益" {% if request.form.get('description') == '公益' %}selected{% endif %}>公益</option>
                                    <option value="娱乐" {% if request.form.get('description') == '娱乐' %}selected{% endif %}>娱乐</option>
                                    <option value="其他" {% if request.form.get('description') == '其他' %}selected{% endif %}>其他</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="max_members" class="form-label">预计最大成员数</label>
                                <input type="number" class="form-control" id="max_members" name="max_members" 
                                       value="{{ request.form.get('max_members', '') }}" min="10" max="1000">
                                <div class="form-text">建议设置为10-1000人</div>
                            </div>
                            <div class="col-md-6">
                                <label for="website" class="form-label">官方网站</label>
                                <input type="url" class="form-control" id="website" name="website" 
                                       value="{{ request.form.get('website', '') }}" placeholder="https://" maxlength="200">
                                <div class="form-text">可选，社团官方网站地址</div>
                            </div>
                        </div>

                        <!-- 申请理由 -->
                        <div class="row g-3 mb-4">
                            <div class="col-12">
                                <label for="application_reason" class="form-label">申请理由 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="application_reason" name="application_reason" 
                                          rows="4" required maxlength="500" 
                                          placeholder="请详细说明创建该社团的理由、目标和计划...">{{ request.form.get('application_reason', '') }}</textarea>
                                <div class="form-text">
                                    <span id="reason-count">0</span>/500 字符
                                </div>
                            </div>
                        </div>

                        <!-- 申请人信息 -->
                        <div class="row g-3 mb-4">
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">
                                    <i class="fas fa-user me-1"></i>申请人信息
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">申请人姓名</label>
                                <input type="text" class="form-control" value="{{ current_user.Name or current_user.Username }}" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">联系电话</label>
                                <input type="text" class="form-control" value="{{ current_user.Phone or '未填写' }}" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">所在学院</label>
                                <input type="text" class="form-control" value="{{ current_user.College or '未填写' }}" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">专业特长</label>
                                <input type="text" class="form-control" value="{{ current_user.Specialty or '未填写' }}" readonly>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('member.profile') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>提交申请
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 字符计数
    $('#application_reason').on('input', function() {
        const count = $(this).val().length;
        $('#reason-count').text(count);
        
        if (count > 500) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // 初始化字符计数
    $('#application_reason').trigger('input');
    
    // 网站URL验证
    $('#website').on('input', function() {
        const url = $(this).val();
        const urlRegex = /^https?:\/\/.+/;
        
        if (url && !urlRegex.test(url)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">请输入正确的网站地址（以http://或https://开头）</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
    
    // 表单提交验证
    $('#applyClubForm').on('submit', function(e) {
        const invalidFields = $(this).find('.is-invalid');
        if (invalidFields.length > 0) {
            e.preventDefault();
            alert('请先修正表单中的错误');
            invalidFields.first().focus();
        }
    });
});
</script>
{% endblock %}
