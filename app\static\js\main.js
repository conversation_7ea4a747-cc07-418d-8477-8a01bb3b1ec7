// 学校社团管理系统 - 前端JavaScript

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 自动隐藏警告框
    $('.alert').each(function() {
        var $alert = $(this);
        if (!$alert.hasClass('alert-danger')) {
            setTimeout(function() {
                $alert.fadeOut();
            }, 5000);
        }
    });

    // 确认删除对话框
    $('.btn-delete').click(function(e) {
        e.preventDefault();
        var $this = $(this);
        var message = $this.data('message') || '确定要删除这个项目吗？';
        
        if (confirm(message)) {
            if ($this.attr('href')) {
                window.location.href = $this.attr('href');
            } else if ($this.closest('form').length) {
                $this.closest('form').submit();
            }
        }
    });

    // 表单提交加载状态
    $('form').submit(function() {
        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        
        if ($submitBtn.length && !$submitBtn.prop('disabled')) {
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true)
                     .html('<i class="fas fa-spinner fa-spin me-2"></i>处理中...');
            
            // 5秒后恢复按钮状态（防止卡死）
            setTimeout(function() {
                $submitBtn.prop('disabled', false).html(originalText);
            }, 5000);
        }
    });

    // 搜索框实时搜索
    $('.search-input').on('input', function() {
        var $input = $(this);
        var query = $input.val().trim();
        var minLength = $input.data('min-length') || 2;
        
        if (query.length >= minLength) {
            // 延迟搜索，避免频繁请求
            clearTimeout($input.data('timeout'));
            $input.data('timeout', setTimeout(function() {
                performSearch(query, $input);
            }, 500));
        }
    });

    // 执行搜索
    function performSearch(query, $input) {
        var searchUrl = $input.data('search-url');
        if (!searchUrl) return;

        $.ajax({
            url: searchUrl,
            data: { q: query },
            method: 'GET',
            beforeSend: function() {
                $input.addClass('loading');
            },
            success: function(data) {
                displaySearchResults(data, $input);
            },
            error: function() {
                console.error('搜索请求失败');
            },
            complete: function() {
                $input.removeClass('loading');
            }
        });
    }

    // 显示搜索结果
    function displaySearchResults(data, $input) {
        var $results = $input.siblings('.search-results');
        if (!$results.length) {
            $results = $('<div class="search-results"></div>').insertAfter($input);
        }

        $results.empty();

        if (data.clubs && data.clubs.length > 0) {
            data.clubs.forEach(function(club) {
                var $item = $('<div class="search-result-item"></div>')
                    .html('<strong>' + club.name + '</strong><br><small>' + club.description + '</small>')
                    .click(function() {
                        window.location.href = '/club/' + club.id;
                    });
                $results.append($item);
            });
            $results.show();
        } else {
            $results.hide();
        }
    }

    // 点击外部隐藏搜索结果
    $(document).click(function(e) {
        if (!$(e.target).closest('.search-input, .search-results').length) {
            $('.search-results').hide();
        }
    });

    // 图片懒加载
    if ('IntersectionObserver' in window) {
        var imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    var img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(function(img) {
            imageObserver.observe(img);
        });
    }

    // 返回顶部按钮
    var $backToTop = $('<button class="btn btn-primary btn-back-to-top" title="返回顶部">' +
                      '<i class="bi bi-arrow-up"></i></button>');
    $('body').append($backToTop);

    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            $backToTop.fadeIn();
        } else {
            $backToTop.fadeOut();
        }
    });

    $backToTop.click(function() {
        $('html, body').animate({scrollTop: 0}, 600);
    });

    // 表格排序
    $('.sortable-table th[data-sort]').click(function() {
        var $th = $(this);
        var $table = $th.closest('table');
        var column = $th.data('sort');
        var order = $th.hasClass('sort-asc') ? 'desc' : 'asc';
        
        // 移除其他列的排序标识
        $table.find('th').removeClass('sort-asc sort-desc');
        $th.addClass('sort-' + order);
        
        // 执行排序
        sortTable($table, column, order);
    });

    function sortTable($table, column, order) {
        var $tbody = $table.find('tbody');
        var $rows = $tbody.find('tr').toArray();
        
        $rows.sort(function(a, b) {
            var aVal = $(a).find('td').eq(column).text().trim();
            var bVal = $(b).find('td').eq(column).text().trim();
            
            // 尝试数字比较
            if (!isNaN(aVal) && !isNaN(bVal)) {
                aVal = parseFloat(aVal);
                bVal = parseFloat(bVal);
            }
            
            if (order === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });
        
        $tbody.empty().append($rows);
    }

    // 批量操作
    $('.select-all').change(function() {
        var checked = $(this).prop('checked');
        $(this).closest('table').find('.select-item').prop('checked', checked);
        updateBatchActions();
    });

    $('.select-item').change(function() {
        updateBatchActions();
    });

    function updateBatchActions() {
        var selectedCount = $('.select-item:checked').length;
        var $batchActions = $('.batch-actions');
        
        if (selectedCount > 0) {
            $batchActions.show().find('.selected-count').text(selectedCount);
        } else {
            $batchActions.hide();
        }
    }

    // 文件上传预览
    $('input[type="file"]').change(function() {
        var $input = $(this);
        var $preview = $input.siblings('.file-preview');
        
        if (!$preview.length) {
            $preview = $('<div class="file-preview mt-2"></div>').insertAfter($input);
        }
        
        $preview.empty();
        
        Array.from(this.files).forEach(function(file) {
            if (file.type.startsWith('image/')) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    var $img = $('<img class="img-thumbnail me-2 mb-2" style="max-width: 100px; max-height: 100px;">');
                    $img.attr('src', e.target.result);
                    $preview.append($img);
                };
                reader.readAsDataURL(file);
            } else {
                var $file = $('<div class="file-item me-2 mb-2 p-2 border rounded">' +
                            '<i class="bi bi-file-earmark me-1"></i>' + file.name + '</div>');
                $preview.append($file);
            }
        });
    });

    // 复制到剪贴板
    $('.copy-to-clipboard').click(function() {
        var text = $(this).data('text') || $(this).text();
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('已复制到剪贴板');
            });
        } else {
            // 兼容旧浏览器
            var $temp = $('<textarea>').val(text).appendTo('body').select();
            document.execCommand('copy');
            $temp.remove();
            showToast('已复制到剪贴板');
        }
    });

    // 显示提示消息
    function showToast(message, type = 'success') {
        var $toast = $('<div class="toast-message toast-' + type + '">' + message + '</div>');
        $('body').append($toast);
        
        setTimeout(function() {
            $toast.addClass('show');
        }, 100);
        
        setTimeout(function() {
            $toast.removeClass('show');
            setTimeout(function() {
                $toast.remove();
            }, 300);
        }, 3000);
    }

    // 表单验证增强
    $('form[data-validate]').each(function() {
        var $form = $(this);
        
        $form.find('input, select, textarea').on('blur', function() {
            validateField($(this));
        });
        
        $form.submit(function(e) {
            var isValid = true;
            
            $form.find('input, select, textarea').each(function() {
                if (!validateField($(this))) {
                    isValid = false;
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showToast('请检查表单中的错误', 'error');
            }
        });
    });

    function validateField($field) {
        var value = $field.val().trim();
        var rules = $field.data('rules');
        var isValid = true;
        var message = '';
        
        if (rules) {
            if (rules.required && !value) {
                isValid = false;
                message = '此字段为必填项';
            } else if (rules.minLength && value.length < rules.minLength) {
                isValid = false;
                message = '最少需要' + rules.minLength + '个字符';
            } else if (rules.maxLength && value.length > rules.maxLength) {
                isValid = false;
                message = '最多允许' + rules.maxLength + '个字符';
            } else if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
                isValid = false;
                message = rules.message || '格式不正确';
            }
        }
        
        if (isValid) {
            $field.removeClass('is-invalid').addClass('is-valid');
            $field.siblings('.invalid-feedback').hide();
        } else {
            $field.removeClass('is-valid').addClass('is-invalid');
            var $feedback = $field.siblings('.invalid-feedback');
            if (!$feedback.length) {
                $feedback = $('<div class="invalid-feedback"></div>').insertAfter($field);
            }
            $feedback.text(message).show();
        }
        
        return isValid;
    }
});

// CSS样式
var css = `
.btn-back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.search-result-item {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.loading {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24'%3E%3Cpath fill='%23999' d='M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z' opacity='.25'/%3E%3Cpath fill='%23999' d='M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z'%3E%3CanimateTransform attributeName='transform' dur='0.75s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
}

.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast-message.show {
    transform: translateX(0);
}

.toast-success {
    background-color: #28a745;
}

.toast-error {
    background-color: #dc3545;
}

.toast-warning {
    background-color: #ffc107;
    color: #212529;
}

.toast-info {
    background-color: #17a2b8;
}

.sortable-table th[data-sort] {
    cursor: pointer;
    user-select: none;
}

.sortable-table th[data-sort]:hover {
    background-color: #f8f9fa;
}

.sortable-table th.sort-asc::after {
    content: " ↑";
}

.sortable-table th.sort-desc::after {
    content: " ↓";
}

.batch-actions {
    background-color: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 8px;
    padding: 10px 15px;
    margin-bottom: 15px;
}
`;

// 添加CSS到页面
$('<style>').text(css).appendTo('head');
