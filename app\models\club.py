#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Clubs表数据模型
社团基础信息和运营状态管理
"""

from app import db
from datetime import datetime, date
import uuid

class Club(db.Model):
    """
    社团模型类
    管理社团基础信息、成员数量、状态等
    """
    
    __tablename__ = 'Clubs'
    
    # 主键：ClubID CHAR(36) PRIMARY KEY
    ClubID = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # ClubName VARCHAR(100) NOT NULL
    ClubName = db.Column(db.String(100), nullable=False, comment='社团名称')
    
    # Description ENUM('学术', '体育', '艺术', '公益', '娱乐', '其他') NOT NULL
    Description = db.Column(db.Enum('学术', '体育', '艺术', '公益', '娱乐', '其他'), 
                           nullable=False, comment='社团类别描述')
    
    # MaxMembers INT CHECK (MaxMembers > 0)
    MaxMembers = db.Column(db.Integer, nullable=True, comment='最大成员数')
    
    # CurrentMembers INT CHECK (CurrentMembers >= 0 AND CurrentMembers <= MaxMembers)
    CurrentMembers = db.Column(db.Integer, nullable=True, default=0, comment='当前成员数')
    
    # PresidentID CHAR(36) NOT NULL, FOREIGN KEY (PresidentID) REFERENCES Members(MemberID)
    PresidentID = db.Column(db.String(36), db.ForeignKey('Members.MemberID'), 
                           nullable=False, comment='社长ID')
    
    # Website VARCHAR(200) CHECK (Website LIKE 'http%')
    Website = db.Column(db.String(200), nullable=True, comment='社团网站')
    
    # FoundationDate DATE NOT NULL
    FoundationDate = db.Column(db.Date, nullable=False, comment='成立日期')
    
    # Category VARCHAR(10) CHECK (Category >= '0')
    Category = db.Column(db.String(10), nullable=True, comment='社团分类编号')
    
    # Status ENUM('活跃', '休眠', '解散') NOT NULL
    Status = db.Column(db.Enum('活跃', '休眠', '解散'), nullable=False, 
                      default='活跃', comment='社团状态')
    
    # 关系定义
    # 社团活动（一对多关系）
    activities = db.relationship('Activity', backref='club', lazy='dynamic')

    # 社团成员关系（一对多关系）
    member_relationships = db.relationship('MemberClub', backref='club', lazy='dynamic')
    
    def __init__(self, **kwargs):
        """初始化社团对象"""
        super(Club, self).__init__(**kwargs)
        if not self.ClubID:
            self.ClubID = str(uuid.uuid4())
        if not self.FoundationDate:
            self.FoundationDate = date.today()
    
    def validate_max_members(self):
        """验证最大成员数约束：MaxMembers > 0"""
        if self.MaxMembers is not None:
            return self.MaxMembers > 0
        return True
    
    def validate_current_members(self):
        """验证当前成员数约束：CurrentMembers >= 0 AND CurrentMembers <= MaxMembers"""
        if self.CurrentMembers is not None:
            if self.CurrentMembers < 0:
                return False
            if self.MaxMembers is not None and self.CurrentMembers > self.MaxMembers:
                return False
        return True
    
    def validate_website(self):
        """验证网站URL约束：Website LIKE 'http%'"""
        if self.Website:
            return self.Website.startswith('http')
        return True
    
    def validate_category(self):
        """验证分类编号约束：Category >= '0'"""
        if self.Category:
            try:
                return int(self.Category) >= 0
            except ValueError:
                return False
        return True
    
    def get_approved_members(self):
        """获取已批准的社团成员"""
        return [mc.member for mc in self.member_relationships.filter_by(Status='已批准').all()]
    
    def get_pending_applications(self):
        """获取待审批的入会申请"""
        return self.member_relationships.filter_by(Status='待审批').all()
    
    def update_current_members(self):
        """更新当前成员数（基于已批准的成员关系）"""
        approved_count = self.member_relationships.filter_by(Status='已批准').count()
        self.CurrentMembers = approved_count
        return approved_count
    
    def can_accept_new_member(self):
        """判断是否可以接受新成员"""
        if self.Status != '活跃':
            return False
        if self.MaxMembers is not None:
            return self.CurrentMembers < self.MaxMembers
        return True
    
    def get_recent_activities(self, limit=5):
        """获取最近的活动"""
        from app.models.activity import Activity
        return self.activities.order_by(Activity.StartTime.desc()).limit(limit).all()
    
    def is_active(self):
        """判断社团是否活跃"""
        return self.Status == '活跃'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'ClubID': self.ClubID,
            'ClubName': self.ClubName,
            'Description': self.Description,
            'MaxMembers': self.MaxMembers,
            'CurrentMembers': self.CurrentMembers,
            'PresidentID': self.PresidentID,
            'Website': self.Website,
            'FoundationDate': self.FoundationDate.isoformat() if self.FoundationDate else None,
            'Category': self.Category,
            'Status': self.Status,
            'PresidentName': self.president.Name if self.president else None
        }
    
    def __repr__(self):
        return f'<Club {self.ClubName}({self.Status})>'
