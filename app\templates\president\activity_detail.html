{% extends "admin_base.html" %}

{% block title %}活动详情 - 会长后台{% endblock %}

{% block breadcrumb_title %}活动详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">{{ activity.ActivityName }}</h2>
            <p class="text-muted mb-0">活动详细信息</p>
        </div>
        <div>
            <a href="{{ url_for('president.activities') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>返回活动管理
            </a>
        </div>
    </div>

    <div class="row">
        <!-- 活动基本信息 -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2 text-primary"></i>活动信息
                        </h5>
                        <span class="badge {% if activity.Status == '计划中' %}bg-warning{% elif activity.Status == '进行中' %}bg-success{% elif activity.Status == '已完成' %}bg-info{% else %}bg-danger{% endif %}">
                            {{ activity.Status }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">活动名称</label>
                            <div class="fw-semibold">{{ activity.ActivityName }}</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">活动类型</label>
                            <div>{{ activity.ActivityType }}</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">所属社团</label>
                            <div>
                                <span class="badge bg-primary">{{ activity.club.ClubName if activity.club else '未知社团' }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">组织者</label>
                            <div>{{ activity.organizer.Name if activity.organizer else '未指定' }}</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">开始时间</label>
                            <div>
                                <i class="fas fa-calendar me-1"></i>
                                {{ activity.StartTime.strftime('%Y-%m-%d %H:%M') if activity.StartTime else '时间待定' }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">结束时间</label>
                            <div>
                                <i class="fas fa-calendar me-1"></i>
                                {{ activity.EndTime.strftime('%Y-%m-%d %H:%M') if activity.EndTime else '时间待定' }}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">活动地点</label>
                            <div>
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ activity.venue.VenueName if activity.venue else '地点待定' }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">参与人数</label>
                            <div>
                                <span class="badge bg-info">{{ activity.ActualParticipant }}人</span>
                                {% if activity.ParticipantLimit %}
                                / {{ activity.ParticipantLimit }}人
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% if activity.Description %}
                    <div class="mb-3">
                        <label class="form-label text-muted">活动描述</label>
                        <div class="border rounded p-3 bg-light">
                            {{ activity.Description }}
                        </div>
                    </div>
                    {% endif %}

                    <!-- 注意：数据库中Activities表没有Requirements字段，移除相关显示 -->
                </div>
            </div>
        </div>

        <!-- 活动统计 -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2 text-info"></i>活动统计
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="fw-bold text-primary">{{ activity.ActualParticipant }}</div>
                            <small class="text-muted">参与人数</small>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="fw-bold text-success">{{ activity.ParticipantLimit or '∞' }}</div>
                            <small class="text-muted">人数限制</small>
                        </div>
                    </div>

                    {% if activity.ParticipantLimit and activity.ParticipantLimit > 0 %}
                    <div class="mb-2">
                        {% set progress = (activity.ActualParticipant / activity.ParticipantLimit * 100) %}
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar {% if progress >= 90 %}bg-danger{% elif progress >= 70 %}bg-warning{% else %}bg-success{% endif %}"
                                 style="width: {{ progress }}%"></div>
                        </div>
                        <small class="text-muted">报名进度：{{ "%.1f"|format(progress) }}%</small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 参加活动的会员信息 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2 text-primary"></i>活动申请信息
                        </h5>
                        <span class="badge bg-info">{{ activity_applications|length }}个申请</span>
                    </div>
                </div>
                <div class="card-body">
                    {% if activity_applications %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>申请人信息</th>
                                    <th>学院</th>
                                    <th>专长</th>
                                    <th>联系方式</th>
                                    <th>申请时间</th>
                                    <th>申请状态</th>
                                    <th>审批意见</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for application in activity_applications %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-3">
                                                {{ application.applicant.Name[0] if application.applicant.Name else application.applicant.Username[0] }}
                                            </div>
                                            <div>
                                                <div class="fw-semibold">{{ application.applicant.Name or application.applicant.Username }}</div>
                                                <small class="text-muted">{{ application.applicant.Username }}</small>
                                                {% if application.applicant.Role == '会长' %}
                                                <span class="badge bg-warning ms-1">会长</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>{{ application.applicant.College or '未填写' }}</div>
                                        {% if application.applicant.Dormitory %}
                                        <small class="text-muted">{{ application.applicant.Dormitory }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ application.applicant.Specialty or '未填写' }}</span>
                                    </td>
                                    <td>
                                        {% if application.applicant.Phone %}
                                        <i class="fas fa-phone me-1"></i>{{ application.applicant.Phone }}
                                        {% else %}
                                        <span class="text-muted">未填写</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>{{ application.RequestTime.strftime('%Y-%m-%d') if application.RequestTime else '未知' }}</div>
                                        <small class="text-muted">{{ application.RequestTime.strftime('%H:%M') if application.RequestTime else '' }}</small>
                                    </td>
                                    <td>
                                        {% if application.Status == '待批' %}
                                        <span class="badge bg-warning">{{ application.get_status_display() }}</span>
                                        {% elif application.Status == '已批' %}
                                        <span class="badge bg-success">{{ application.get_status_display() }}</span>
                                        {% else %}
                                        <span class="badge bg-danger">{{ application.get_status_display() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if application.Comments %}
                                        <span class="text-muted">{{ application.Comments }}</span>
                                        {% else %}
                                        <span class="text-muted">无</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">暂无申请</h5>
                        <p class="text-muted">该活动目前还没有收到任何申请。</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

.card {
    border-radius: 12px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>
{% endblock %}
