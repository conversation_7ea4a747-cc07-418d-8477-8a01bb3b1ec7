{% extends "admin_base.html" %}

{% block title %}活动详情 - 会长后台{% endblock %}

{% block breadcrumb_title %}活动详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">{{ activity.ActivityName }}</h2>
            <p class="text-muted mb-0">活动详细信息</p>
        </div>
        <div>
            <a href="{{ url_for('president.activities') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>返回活动管理
            </a>
        </div>
    </div>

    <div class="row">
        <!-- 活动基本信息 -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2 text-primary"></i>活动信息
                        </h5>
                        <span class="badge {% if activity.Status == '计划中' %}bg-warning{% elif activity.Status == '进行中' %}bg-success{% elif activity.Status == '已完成' %}bg-info{% else %}bg-danger{% endif %}">
                            {{ activity.Status }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">活动名称</label>
                            <div class="fw-semibold">{{ activity.ActivityName }}</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">活动类型</label>
                            <div>{{ activity.ActivityType }}</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">所属社团</label>
                            <div>
                                <span class="badge bg-primary">{{ activity.club.ClubName if activity.club else '未知社团' }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">组织者</label>
                            <div>{{ activity.organizer.Name if activity.organizer else '未指定' }}</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">开始时间</label>
                            <div>
                                <i class="fas fa-calendar me-1"></i>
                                {{ activity.StartTime.strftime('%Y-%m-%d %H:%M') if activity.StartTime else '时间待定' }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">结束时间</label>
                            <div>
                                <i class="fas fa-calendar me-1"></i>
                                {{ activity.EndTime.strftime('%Y-%m-%d %H:%M') if activity.EndTime else '时间待定' }}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label text-muted">活动地点</label>
                            <div>
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ activity.venue.VenueName if activity.venue else '地点待定' }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">参与人数</label>
                            <div>
                                <span class="badge bg-info">{{ activity.ActualParticipant }}人</span>
                                {% if activity.ParticipantLimit %}
                                / {{ activity.ParticipantLimit }}人
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% if activity.Description %}
                    <div class="mb-3">
                        <label class="form-label text-muted">活动描述</label>
                        <div class="border rounded p-3 bg-light">
                            {{ activity.Description }}
                        </div>
                    </div>
                    {% endif %}

                    {% if activity.Requirements %}
                    <div class="mb-3">
                        <label class="form-label text-muted">参与要求</label>
                        <div class="border rounded p-3 bg-light">
                            {{ activity.Requirements }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 操作面板 -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2 text-success"></i>操作面板
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if activity.Status == '计划中' %}
                        <button type="button" class="btn btn-outline-primary" onclick="editActivity()">
                            <i class="fas fa-edit me-1"></i>编辑活动
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="startActivity()">
                            <i class="fas fa-play me-1"></i>开始活动
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="cancelActivity()">
                            <i class="fas fa-times me-1"></i>取消活动
                        </button>
                        {% elif activity.Status == '进行中' %}
                        <button type="button" class="btn btn-outline-info" onclick="completeActivity()">
                            <i class="fas fa-check me-1"></i>完成活动
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="pauseActivity()">
                            <i class="fas fa-pause me-1"></i>暂停活动
                        </button>
                        {% endif %}
                        
                        <hr>
                        
                        <button type="button" class="btn btn-outline-info" onclick="viewParticipants()">
                            <i class="fas fa-users me-1"></i>查看参与者
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportData()">
                            <i class="fas fa-download me-1"></i>导出数据
                        </button>
                    </div>
                </div>
            </div>

            <!-- 活动统计 -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2 text-info"></i>活动统计
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="fw-bold text-primary">{{ activity.ActualParticipant }}</div>
                            <small class="text-muted">参与人数</small>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="fw-bold text-success">{{ activity.ParticipantLimit or '∞' }}</div>
                            <small class="text-muted">人数限制</small>
                        </div>
                    </div>
                    
                    {% if activity.ParticipantLimit and activity.ParticipantLimit > 0 %}
                    <div class="mb-2">
                        {% set progress = (activity.ActualParticipant / activity.ParticipantLimit * 100) %}
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar {% if progress >= 90 %}bg-danger{% elif progress >= 70 %}bg-warning{% else %}bg-success{% endif %}" 
                                 style="width: {{ progress }}%"></div>
                        </div>
                        <small class="text-muted">报名进度：{{ "%.1f"|format(progress) }}%</small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 时间线 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2 text-warning"></i>活动时间线
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">活动创建</h6>
                                <p class="text-muted mb-0">{{ activity.CreateTime.strftime('%Y-%m-%d %H:%M') if activity.CreateTime else '未知时间' }}</p>
                            </div>
                        </div>
                        
                        {% if activity.StartTime %}
                        <div class="timeline-item">
                            <div class="timeline-marker {% if activity.Status in ['进行中', '已完成'] %}bg-success{% else %}bg-warning{% endif %}"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">计划开始</h6>
                                <p class="text-muted mb-0">{{ activity.StartTime.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if activity.EndTime %}
                        <div class="timeline-item">
                            <div class="timeline-marker {% if activity.Status == '已完成' %}bg-info{% else %}bg-secondary{% endif %}"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">计划结束</h6>
                                <p class="text-muted mb-0">{{ activity.EndTime.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content h6 {
    color: #495057;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function editActivity() {
    alert('编辑活动功能开发中');
}

function startActivity() {
    if (confirm('确定要开始这个活动吗？')) {
        alert('开始活动功能开发中');
    }
}

function cancelActivity() {
    if (confirm('确定要取消这个活动吗？此操作不可撤销。')) {
        alert('取消活动功能开发中');
    }
}

function completeActivity() {
    if (confirm('确定要完成这个活动吗？')) {
        alert('完成活动功能开发中');
    }
}

function pauseActivity() {
    if (confirm('确定要暂停这个活动吗？')) {
        alert('暂停活动功能开发中');
    }
}

function viewParticipants() {
    alert('查看参与者功能开发中');
}

function exportData() {
    alert('导出数据功能开发中');
}

$(document).ready(function() {
    // 工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>
{% endblock %}
