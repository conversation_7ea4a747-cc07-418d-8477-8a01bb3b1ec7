#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员控制器
处理系统管理员的业务逻辑，包括场馆管理、审批流程、数据统计等
严格基于所有数据表的管理权限
"""

from app import db
from app.models.member import Member
from app.models.club import Club
from app.models.venue import Venue
from app.models.activity import Activity
from app.models.member_club import MemberClub
from app.models.approval import ApprovalRequest
from app.utils.helpers import generate_uuid, get_china_time
from datetime import datetime, timedelta
from sqlalchemy import func

class AdminController:
    """管理员控制器类"""
    
    @staticmethod
    def get_admin_dashboard_data():
        """
        获取管理员仪表板数据
        
        Returns:
            dict: 仪表板数据
        """
        # 基础统计
        stats = {
            'total_members': Member.query.count(),
            'total_clubs': Club.query.count(),
            'active_clubs': Club.query.filter_by(Status='活跃').count(),
            'total_venues': Venue.query.count(),
            'total_activities': Activity.query.count(),
            'pending_approvals': ApprovalRequest.query.filter_by(Status='待批').count()
        }
        
        # 待审批申请
        pending_approvals = ApprovalRequest.query.filter_by(Status='待批').order_by(
            ApprovalRequest.RequestTime.asc()
        ).limit(10).all()
        
        # 最近注册的用户
        recent_members = Member.query.order_by(Member.MemberID.desc()).limit(5).all()
        
        # 最近创建的社团
        recent_clubs = Club.query.order_by(Club.FoundationDate.desc()).limit(5).all()
        
        # 即将举行的活动
        upcoming_activities = Activity.query.filter(
            Activity.Status == '计划中',
            Activity.StartTime > datetime.now()
        ).order_by(Activity.StartTime.asc()).limit(5).all()
        
        return {
            'stats': stats,
            'pending_approvals': pending_approvals,
            'recent_members': recent_members,
            'recent_clubs': recent_clubs,
            'upcoming_activities': upcoming_activities
        }
    
    @staticmethod
    def create_venue(venue_data):
        """
        创建场馆
        
        Args:
            venue_data: 场馆数据
        
        Returns:
            tuple: (success: bool, message: str)
        """
        # 验证必填字段
        required_fields = ['venue_name', 'location', 'address', 'venue_type', 'availab_time']
        for field in required_fields:
            if not venue_data.get(field):
                return False, f'{field}不能为空'
        
        # 验证字段长度
        if len(venue_data['venue_name']) > 100:
            return False, '场馆名称长度不能超过100个字符'
        
        if len(venue_data['location']) > 100:
            return False, '场馆位置长度不能超过100个字符'
        
        if len(venue_data['address']) > 200:
            return False, '场馆地址长度不能超过200个字符'
        
        if len(venue_data['availab_time']) > 20:
            return False, '可用时间长度不能超过20个字符'
        
        # 验证场馆类型
        valid_types = ['室内', '室外', '多功能厅', '体育馆', '其他']
        if venue_data['venue_type'] not in valid_types:
            return False, '场馆类型无效'
        
        # 验证联系电话（如果提供）
        contact_phone = venue_data.get('contact_phone', '').strip()
        if contact_phone:
            import re
            if not re.match(r'^[0-9]{7,15}$', contact_phone):
                return False, '联系电话格式不正确（7-15位数字）'
        
        # 验证容量（如果提供）
        capacity = venue_data.get('capacity')
        if capacity:
            try:
                capacity = int(capacity)
                if capacity <= 0:
                    return False, '场馆容量必须大于0'
            except ValueError:
                return False, '场馆容量必须为数字'
        
        try:
            # 创建场馆
            venue = Venue(
                VenueID=generate_uuid(),
                VenueName=venue_data['venue_name'],
                Location=venue_data['location'],
                Address=venue_data['address'],
                ContactPhone=contact_phone or None,
                Capacity=capacity,
                VenueType=venue_data['venue_type'],
                AvailabTime=venue_data['availab_time']
            )
            
            db.session.add(venue)
            db.session.commit()
            
            return True, f'场馆"{venue.VenueName}"创建成功'
            
        except Exception as e:
            db.session.rollback()
            return False, f'创建场馆失败：{str(e)}'
    
    @staticmethod
    def update_venue(venue_id, venue_data):
        """
        更新场馆信息
        
        Args:
            venue_id: 场馆ID
            venue_data: 场馆数据
        
        Returns:
            tuple: (success: bool, message: str)
        """
        venue = Venue.query.get(venue_id)
        if not venue:
            return False, '场馆不存在'
        
        try:
            # 更新字段
            if 'venue_name' in venue_data and venue_data['venue_name']:
                if len(venue_data['venue_name']) > 100:
                    return False, '场馆名称长度不能超过100个字符'
                venue.VenueName = venue_data['venue_name']
            
            if 'location' in venue_data and venue_data['location']:
                if len(venue_data['location']) > 100:
                    return False, '场馆位置长度不能超过100个字符'
                venue.Location = venue_data['location']
            
            if 'address' in venue_data and venue_data['address']:
                if len(venue_data['address']) > 200:
                    return False, '场馆地址长度不能超过200个字符'
                venue.Address = venue_data['address']
            
            if 'contact_phone' in venue_data:
                contact_phone = venue_data['contact_phone'].strip() or None
                if contact_phone:
                    import re
                    if not re.match(r'^[0-9]{7,15}$', contact_phone):
                        return False, '联系电话格式不正确（7-15位数字）'
                venue.ContactPhone = contact_phone
            
            if 'capacity' in venue_data and venue_data['capacity']:
                try:
                    capacity = int(venue_data['capacity'])
                    if capacity <= 0:
                        return False, '场馆容量必须大于0'
                    venue.Capacity = capacity
                except ValueError:
                    return False, '场馆容量必须为数字'
            
            if 'venue_type' in venue_data and venue_data['venue_type']:
                valid_types = ['室内', '室外', '多功能厅', '体育馆', '其他']
                if venue_data['venue_type'] not in valid_types:
                    return False, '场馆类型无效'
                venue.VenueType = venue_data['venue_type']
            
            if 'availab_time' in venue_data and venue_data['availab_time']:
                if len(venue_data['availab_time']) > 20:
                    return False, '可用时间长度不能超过20个字符'
                venue.AvailabTime = venue_data['availab_time']
            
            db.session.commit()
            return True, '场馆信息更新成功'
            
        except Exception as e:
            db.session.rollback()
            return False, f'更新失败：{str(e)}'
    
    @staticmethod
    def delete_venue(venue_id):
        """
        删除场馆
        
        Args:
            venue_id: 场馆ID
        
        Returns:
            tuple: (success: bool, message: str)
        """
        venue = Venue.query.get(venue_id)
        if not venue:
            return False, '场馆不存在'
        
        # 检查是否有关联的活动
        related_activities = Activity.query.filter_by(VenueID=venue_id).count()
        if related_activities > 0:
            return False, f'该场馆有{related_activities}个关联活动，无法删除'
        
        try:
            db.session.delete(venue)
            db.session.commit()
            
            return True, f'场馆"{venue.VenueName}"删除成功'
            
        except Exception as e:
            db.session.rollback()
            return False, f'删除失败：{str(e)}'
    
    @staticmethod
    def get_all_venues(page=1, per_page=10, search=None, venue_type=None, capacity_range=None):
        """
        获取所有场馆列表

        Args:
            page: 页码
            per_page: 每页数量
            search: 搜索关键词
            venue_type: 场馆类型筛选
            capacity_range: 容量范围筛选

        Returns:
            dict: 场馆列表和分页信息
        """
        query = Venue.query

        # 搜索功能
        if search:
            query = query.filter(
                Venue.VenueName.contains(search) |
                Venue.Location.contains(search) |
                Venue.Address.contains(search)
            )

        # 场馆类型筛选
        if venue_type:
            query = query.filter_by(VenueType=venue_type)

        # 容量范围筛选
        if capacity_range:
            if capacity_range == '0-50':
                query = query.filter(Venue.Capacity <= 50)
            elif capacity_range == '50-100':
                query = query.filter(Venue.Capacity > 50, Venue.Capacity <= 100)
            elif capacity_range == '100-200':
                query = query.filter(Venue.Capacity > 100, Venue.Capacity <= 200)
            elif capacity_range == '200+':
                query = query.filter(Venue.Capacity > 200)

        pagination = query.order_by(Venue.VenueName.asc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return {
            'venues': pagination.items,
            'pagination': pagination,
            'total': pagination.total
        }
    
    @staticmethod
    def process_approval_request(request_id, action, comments=None):
        """
        处理审批申请
        
        Args:
            request_id: 申请ID
            action: 操作（approve/reject）
            comments: 审批意见
        
        Returns:
            tuple: (success: bool, message: str)
        """
        approval_request = ApprovalRequest.query.get(request_id)
        if not approval_request:
            return False, '申请不存在'
        
        if approval_request.Status != '待批':
            return False, '该申请已被处理'
        
        try:
            if action == 'approve':
                approval_request.approve('admin', comments)
                message = f'{approval_request.RequestType}申请已批准'
            elif action == 'reject':
                approval_request.reject('admin', comments)
                message = f'{approval_request.RequestType}申请已拒绝'
            else:
                return False, '无效的操作'
            
            db.session.commit()
            return True, message
            
        except Exception as e:
            db.session.rollback()
            return False, f'处理申请失败：{str(e)}'
    
    @staticmethod
    def get_system_statistics():
        """
        获取系统统计数据
        
        Returns:
            dict: 统计数据
        """
        # 用户统计
        user_stats = {
            'total_users': Member.query.count(),
            'admins': Member.query.filter_by(Role='管理员').count(),
            'presidents': Member.query.filter_by(Role='会长').count(),
            'members': Member.query.filter_by(Role='会员').count()
        }
        
        # 社团统计
        club_stats = {
            'total_clubs': Club.query.count(),
            'active_clubs': Club.query.filter_by(Status='活跃').count(),
            'dormant_clubs': Club.query.filter_by(Status='休眠').count(),
            'dissolved_clubs': Club.query.filter_by(Status='解散').count()
        }
        
        # 按类别统计社团
        club_by_category = db.session.query(
            Club.Description, func.count(Club.ClubID)
        ).group_by(Club.Description).all()
        
        # 活动统计
        activity_stats = {
            'total_activities': Activity.query.count(),
            'planned_activities': Activity.query.filter_by(Status='计划中').count(),
            'ongoing_activities': Activity.query.filter_by(Status='进行中').count(),
            'completed_activities': Activity.query.filter_by(Status='已完成').count(),
            'cancelled_activities': Activity.query.filter_by(Status='已取消').count()
        }
        
        # 按类型统计活动
        activity_by_type = db.session.query(
            Activity.ActivityType, func.count(Activity.ActivityID)
        ).group_by(Activity.ActivityType).all()
        
        # 场馆统计
        venue_stats = {
            'total_venues': Venue.query.count()
        }
        
        # 按类型统计场馆
        venue_by_type = db.session.query(
            Venue.VenueType, func.count(Venue.VenueID)
        ).group_by(Venue.VenueType).all()
        
        # 申请统计
        approval_stats = {
            'total_requests': ApprovalRequest.query.count(),
            'pending_requests': ApprovalRequest.query.filter_by(Status='待批').count(),
            'approved_requests': ApprovalRequest.query.filter_by(Status='已批').count(),
            'rejected_requests': ApprovalRequest.query.filter_by(Status='已拒').count()
        }
        
        return {
            'user_stats': user_stats,
            'club_stats': club_stats,
            'club_by_category': dict(club_by_category),
            'activity_stats': activity_stats,
            'activity_by_type': dict(activity_by_type),
            'venue_stats': venue_stats,
            'venue_by_type': dict(venue_by_type),
            'approval_stats': approval_stats
        }
    
    @staticmethod
    def get_all_members(page=1, per_page=10, role=None, search=None):
        """
        获取所有用户列表

        Args:
            page: 页码
            per_page: 每页数量
            role: 角色过滤
            search: 搜索关键词

        Returns:
            dict: 用户列表和分页信息
        """
        query = Member.query

        if role:
            query = query.filter_by(Role=role)

        if search:
            query = query.filter(
                Member.Name.contains(search) |
                Member.Username.contains(search)
            )

        pagination = query.order_by(Member.MemberID.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return {
            'members': pagination.items,
            'pagination': pagination,
            'total': pagination.total
        }
    
    @staticmethod
    def get_all_clubs(page=1, per_page=20, search=None):
        """
        获取所有社团列表

        Args:
            page: 页码
            per_page: 每页数量
            search: 搜索关键词

        Returns:
            dict: 社团列表和分页信息
        """
        query = Club.query

        if search:
            query = query.filter(Club.ClubName.contains(search))

        pagination = query.order_by(Club.FoundationDate.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return {
            'clubs': pagination.items,
            'pagination': pagination,
            'total': pagination.total
        }

    @staticmethod
    def create_member(member_data):
        """
        创建新用户

        Args:
            member_data: 用户数据字典

        Returns:
            tuple: (success: bool, message: str, member_id: str)
        """
        try:
            # 验证必填字段
            if not member_data.get('username'):
                return False, '用户名不能为空', None
            if not member_data.get('name'):
                return False, '姓名不能为空', None

            # 验证用户名唯一性
            if Member.query.filter_by(Username=member_data['username']).first():
                return False, '用户名已存在', None

            # 验证角色
            role = member_data.get('role', '会员')
            if role not in ['管理员', '会长', '会员']:
                return False, '无效的角色', None

            # 验证性别
            gender = member_data.get('gender', '男')
            if gender not in ['男', '女', '其他']:
                return False, '无效的性别', None

            # 验证年龄
            age = member_data.get('age')
            if age is not None and (not isinstance(age, int) or age <= 0 or age >= 150):
                return False, '年龄必须在1-149之间', None

            # 验证电话号码
            phone = member_data.get('phone')
            if phone:
                import re
                if not re.match(r'^[0-9]{7,15}$', phone):
                    return False, '电话号码格式不正确（应为7-15位数字）', None

            # 创建新用户
            member = Member(
                MemberID=generate_uuid(),
                Username=member_data['username'],
                Password=member_data.get('password', '123456'),  # 默认密码123456
                Name=member_data['name'],
                Role=role,
                College=member_data.get('college') if member_data.get('college') else None,
                Specialty=member_data.get('specialty') if member_data.get('specialty') else None,
                Phone=phone if phone else None,
                Age=age,
                Gender=gender,
                Dormitory=member_data.get('dormitory') if member_data.get('dormitory') else None
            )

            db.session.add(member)
            db.session.commit()

            return True, f'用户"{member.Name or member.Username}"创建成功', member.MemberID

        except Exception as e:
            db.session.rollback()
            return False, f'创建用户失败：{str(e)}', None

    @staticmethod
    def update_member(member_id, member_data):
        """
        更新用户信息

        Args:
            member_id: 用户ID
            member_data: 更新的用户数据

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            member = Member.query.get(member_id)
            if not member:
                return False, '用户不存在'

            # 检查用户名唯一性（如果修改了用户名）
            if 'username' in member_data and member_data['username'] != member.Username:
                if Member.query.filter_by(Username=member_data['username']).first():
                    return False, '用户名已存在'
                member.Username = member_data['username']

            # 更新姓名（必填字段）
            if 'name' in member_data:
                if not member_data['name']:
                    return False, '姓名不能为空'
                member.Name = member_data['name']

            # 更新角色
            if 'role' in member_data:
                if member_data['role'] not in ['管理员', '会长', '会员']:
                    return False, '无效的角色'
                member.Role = member_data['role']

            # 更新电话（验证格式）
            if 'phone' in member_data:
                phone = member_data['phone']
                if phone:
                    import re
                    if not re.match(r'^[0-9]{7,15}$', phone):
                        return False, '电话号码格式不正确（应为7-15位数字）'
                member.Phone = phone if phone else None

            # 更新学院
            if 'college' in member_data:
                member.College = member_data['college'] if member_data['college'] else None

            # 更新专长
            if 'specialty' in member_data:
                member.Specialty = member_data['specialty'] if member_data['specialty'] else None

            # 更新年龄（验证范围）
            if 'age' in member_data:
                age = member_data['age']
                if age is not None:
                    if not isinstance(age, int) or age <= 0 or age >= 150:
                        return False, '年龄必须在1-149之间'
                member.Age = age

            # 更新性别（验证枚举值）
            if 'gender' in member_data:
                gender = member_data['gender']
                if gender and gender not in ['男', '女', '其他']:
                    return False, '无效的性别'
                # 如果性别为空，设置默认值
                if not gender:
                    gender = '男'
                member.Gender = gender

            # 更新宿舍
            if 'dormitory' in member_data:
                member.Dormitory = member_data['dormitory'] if member_data['dormitory'] else None

            # 如果修改了密码
            if 'password' in member_data and member_data['password']:
                member.Password = member_data['password']  # 注意：实际应用中需要加密

            db.session.commit()

            return True, f'用户"{member.Name or member.Username}"更新成功'

        except Exception as e:
            db.session.rollback()
            return False, f'更新用户失败：{str(e)}'

    @staticmethod
    def delete_member(member_id):
        """
        删除用户

        Args:
            member_id: 用户ID

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            member = Member.query.get(member_id)
            if not member:
                return False, '用户不存在'

            # 检查是否为管理员（防止删除管理员）
            if member.Role == '管理员':
                return False, '不能删除管理员用户'

            member_name = member.Name or member.Username

            # 删除相关的社团关系记录
            from app.models.member_club import MemberClub
            MemberClub.query.filter_by(MemberID=member_id).delete()

            # 删除相关的审批申请记录
            ApprovalRequest.query.filter_by(ApplicantID=member_id).delete()

            # 删除用户
            db.session.delete(member)
            db.session.commit()

            return True, f'用户"{member_name}"删除成功'

        except Exception as e:
            db.session.rollback()
            return False, f'删除用户失败：{str(e)}'

    @staticmethod
    def get_member_by_id(member_id):
        """
        根据ID获取用户信息

        Args:
            member_id: 用户ID

        Returns:
            Member: 用户对象或None
        """
        return Member.query.get(member_id)

    @staticmethod
    def create_club(club_data):
        """
        创建新社团

        Args:
            club_data: 社团数据字典

        Returns:
            tuple: (success: bool, message: str, club_id: str)
        """
        try:
            # 验证社团名称唯一性
            if Club.query.filter_by(ClubName=club_data['club_name']).first():
                return False, '社团名称已存在', None

            # 验证会长是否存在
            president = Member.query.get(club_data['president_id'])
            if not president:
                return False, '指定的会长不存在', None

            # 处理成立日期
            foundation_date = club_data.get('foundation_date')
            if foundation_date:
                from datetime import datetime
                foundation_date = datetime.strptime(foundation_date, '%Y-%m-%d').date()
            else:
                foundation_date = get_china_time().date()

            # 处理网站字段 - 如果为空则设为None，避免约束检查
            website = club_data.get('website', '').strip()
            if not website:
                website = None

            # 创建新社团
            club = Club(
                ClubID=generate_uuid(),
                ClubName=club_data['club_name'],
                Description=club_data.get('description', '学术'),
                PresidentID=club_data['president_id'],
                FoundationDate=foundation_date,
                Status=club_data.get('status', '活跃'),
                CurrentMembers=1,  # 会长自动成为成员
                MaxMembers=club_data.get('max_members'),
                Website=website,
                Category='1'  # 默认类别编号
            )

            db.session.add(club)

            # 自动将会长加入社团
            from app.models.member_club import MemberClub
            from flask import session

            # 获取当前用户ID，如果没有则使用admin-001
            current_user_id = session.get('user_id', 'admin-001')

            member_club = MemberClub(
                RecordID=generate_uuid(),
                MemberID=club_data['president_id'],
                ClubID=club.ClubID,
                ApplyTime=get_china_time(),
                Status='已批准',
                ApprovalId=current_user_id,
                ApprovalTime=get_china_time(),
                ApplicationReason='社团创建时自动加入',
                Rejoinable='是'
            )
            db.session.add(member_club)

            db.session.commit()

            return True, f'社团"{club.ClubName}"创建成功', club.ClubID

        except Exception as e:
            db.session.rollback()
            return False, f'创建社团失败：{str(e)}', None

    @staticmethod
    def update_club(club_id, club_data):
        """
        更新社团信息

        Args:
            club_id: 社团ID
            club_data: 更新的社团数据

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            club = Club.query.get(club_id)
            if not club:
                return False, '社团不存在'

            # 检查社团名称唯一性（如果修改了名称）
            if 'club_name' in club_data and club_data['club_name'] != club.ClubName:
                if Club.query.filter_by(ClubName=club_data['club_name']).first():
                    return False, '社团名称已存在'
                club.ClubName = club_data['club_name']

            # 更新其他字段
            if 'description' in club_data:
                club.Description = club_data['description']
            if 'status' in club_data:
                club.Status = club_data['status']
            if 'max_members' in club_data:
                club.MaxMembers = club_data['max_members']
            if 'website' in club_data:
                website = club_data['website'].strip()
                club.Website = website if website else None
            if 'foundation_date' in club_data and club_data['foundation_date']:
                from datetime import datetime
                club.FoundationDate = datetime.strptime(club_data['foundation_date'], '%Y-%m-%d').date()

            # 如果修改了会长
            if 'president_id' in club_data and club_data['president_id'] != club.PresidentID:
                new_president = Member.query.get(club_data['president_id'])
                if not new_president:
                    return False, '指定的新会长不存在'
                club.PresidentID = club_data['president_id']

            db.session.commit()

            return True, f'社团"{club.ClubName}"更新成功'

        except Exception as e:
            db.session.rollback()
            return False, f'更新社团失败：{str(e)}'

    @staticmethod
    def delete_club(club_id):
        """
        删除社团

        Args:
            club_id: 社团ID

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            club = Club.query.get(club_id)
            if not club:
                return False, '社团不存在'

            club_name = club.ClubName

            # 删除相关的成员关系记录
            from app.models.member_club import MemberClub
            MemberClub.query.filter_by(ClubID=club_id).delete()

            # 删除相关的活动记录
            Activity.query.filter_by(ClubID=club_id).delete()

            # 删除相关的审批申请记录
            ApprovalRequest.query.filter_by(RelatedID=club_id).delete()

            # 删除社团
            db.session.delete(club)
            db.session.commit()

            return True, f'社团"{club_name}"删除成功'

        except Exception as e:
            db.session.rollback()
            return False, f'删除社团失败：{str(e)}'

    @staticmethod
    def get_club_by_id(club_id):
        """
        根据ID获取社团信息

        Args:
            club_id: 社团ID

        Returns:
            Club: 社团对象或None
        """
        return Club.query.get(club_id)

    @staticmethod
    def get_club_members(club_id, page=1, per_page=20):
        """
        获取社团成员列表

        Args:
            club_id: 社团ID
            page: 页码
            per_page: 每页数量

        Returns:
            dict: 成员列表和分页信息
        """
        from app.models.member_club import MemberClub

        query = MemberClub.query.filter_by(ClubID=club_id)

        pagination = query.order_by(MemberClub.ApplyTime.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return {
            'members': pagination.items,
            'pagination': pagination,
            'total': pagination.total
        }

    @staticmethod
    def remove_club_member(club_id, record_id):
        """
        移除社团成员

        Args:
            club_id: 社团ID
            record_id: 成员记录ID (RecordID)

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            from app.models.member_club import MemberClub

            # 查找成员记录
            member_club = MemberClub.query.get(record_id)
            if not member_club:
                return False, '成员记录不存在'

            # 验证是否属于指定社团
            if member_club.ClubID != club_id:
                return False, '成员记录与社团不匹配'

            # 检查是否为已批准状态
            if member_club.Status != '已批准':
                return False, '只能移除已批准的成员'

            # 获取成员信息
            member_name = member_club.member.Name if member_club.member else '该成员'

            # 更新状态为已退出
            member_club.Status = '已退出'
            member_club.ApprovalTime = get_china_time()
            member_club.ApplicationReason = '管理员移除'

            # 更新社团当前成员数
            club = Club.query.get(club_id)
            if club:
                club.update_current_members()

            db.session.commit()

            return True, f'成功移除成员"{member_name}"'

        except Exception as e:
            db.session.rollback()
            return False, f'移除成员失败：{str(e)}'

    @staticmethod
    def approve_club_member(club_id, record_id):
        """
        批准社团成员申请

        Args:
            club_id: 社团ID
            record_id: 成员记录ID (RecordID)

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            from app.models.member_club import MemberClub
            from flask import session

            # 查找成员记录
            member_club = MemberClub.query.get(record_id)
            if not member_club:
                return False, '成员记录不存在'

            # 验证是否属于指定社团
            if member_club.ClubID != club_id:
                return False, '成员记录与社团不匹配'

            # 检查是否为待审批状态
            if member_club.Status != '待审批':
                return False, '只能批准待审批的申请'

            # 获取成员信息
            member_name = member_club.member.Name if member_club.member else '该成员'

            # 更新状态为已批准
            member_club.Status = '已批准'
            member_club.ApprovalId = session.get('user_id', 'admin-001')  # 使用session中的用户ID
            member_club.ApprovalTime = get_china_time()
            member_club.Rejoinable = '是'

            # 更新社团当前成员数
            club = Club.query.get(club_id)
            if club:
                club.update_current_members()

            db.session.commit()

            return True, f'成功批准成员"{member_name}"的申请'

        except Exception as e:
            db.session.rollback()
            return False, f'批准申请失败：{str(e)}'

    @staticmethod
    def reject_club_member(club_id, record_id):
        """
        拒绝社团成员申请

        Args:
            club_id: 社团ID
            record_id: 成员记录ID (RecordID)

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            from app.models.member_club import MemberClub
            from flask import session

            # 查找成员记录
            member_club = MemberClub.query.get(record_id)
            if not member_club:
                return False, '成员记录不存在'

            # 验证是否属于指定社团
            if member_club.ClubID != club_id:
                return False, '成员记录与社团不匹配'

            # 检查是否为待审批状态
            if member_club.Status != '待审批':
                return False, '只能拒绝待审批的申请'

            # 获取成员信息
            member_name = member_club.member.Name if member_club.member else '该成员'

            # 更新状态为已拒绝
            member_club.Status = '已拒绝'
            member_club.ApprovalId = session.get('user_id', 'admin-001')  # 使用session中的用户ID
            member_club.ApprovalTime = get_china_time()
            member_club.Rejoinable = '是'

            db.session.commit()

            return True, f'已拒绝成员"{member_name}"的申请'

        except Exception as e:
            db.session.rollback()
            return False, f'拒绝申请失败：{str(e)}'

    @staticmethod
    def get_club_activities(club_id, page=1, per_page=20, status=None):
        """
        获取社团活动列表

        Args:
            club_id: 社团ID
            page: 页码
            per_page: 每页数量
            status: 状态过滤

        Returns:
            dict: 活动列表和分页信息
        """
        query = Activity.query.filter_by(ClubID=club_id)

        if status:
            query = query.filter_by(Status=status)

        pagination = query.order_by(Activity.StartTime.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return {
            'activities': pagination.items,
            'pagination': pagination,
            'total': pagination.total
        }

    @staticmethod
    def get_available_presidents():
        """
        获取可以担任会长的用户列表（只包括角色为"会长"的用户）

        Returns:
            list: 用户列表
        """
        # 获取角色为"会长"的用户
        available_users = Member.query.filter(
            Member.Role == '会长'
        ).all()

        return available_users

    @staticmethod
    def get_all_activities(page=1, per_page=10, search=None, status=None, activity_type=None, club_id=None, date_range=None):
        """
        获取所有活动列表

        Args:
            page: 页码
            per_page: 每页数量
            search: 搜索关键词
            status: 状态筛选
            activity_type: 活动类型筛选
            club_id: 社团筛选
            date_range: 时间范围筛选

        Returns:
            dict: 活动列表和分页信息
        """
        from datetime import datetime, timedelta

        query = Activity.query

        # 搜索功能
        if search:
            query = query.filter(Activity.ActivityName.contains(search))

        # 状态筛选
        if status:
            query = query.filter_by(Status=status)

        # 活动类型筛选
        if activity_type:
            query = query.filter_by(ActivityType=activity_type)

        # 社团筛选
        if club_id:
            query = query.filter_by(ClubID=club_id)

        # 时间范围筛选
        if date_range:
            now = get_china_time()
            if date_range == 'today':
                start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
                end_of_day = start_of_day + timedelta(days=1)
                query = query.filter(Activity.StartTime >= start_of_day, Activity.StartTime < end_of_day)
            elif date_range == 'week':
                start_of_week = now - timedelta(days=now.weekday())
                start_of_week = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)
                end_of_week = start_of_week + timedelta(days=7)
                query = query.filter(Activity.StartTime >= start_of_week, Activity.StartTime < end_of_week)
            elif date_range == 'month':
                start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                if now.month == 12:
                    end_of_month = start_of_month.replace(year=now.year + 1, month=1)
                else:
                    end_of_month = start_of_month.replace(month=now.month + 1)
                query = query.filter(Activity.StartTime >= start_of_month, Activity.StartTime < end_of_month)
            elif date_range == 'upcoming':
                query = query.filter(Activity.StartTime >= now)

        pagination = query.order_by(Activity.StartTime.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return {
            'activities': pagination.items,
            'pagination': pagination,
            'total': pagination.total
        }

    @staticmethod
    def create_activity(activity_data):
        """
        创建新活动

        Args:
            activity_data: 活动数据字典

        Returns:
            tuple: (success: bool, message: str, activity_id: str)
        """
        try:
            # 验证必填字段
            if not activity_data.get('activity_name'):
                return False, '活动名称不能为空', None
            if not activity_data.get('club_id'):
                return False, '必须选择主办社团', None
            if not activity_data.get('organizer_id'):
                return False, '必须选择组织者', None
            if not activity_data.get('venue_id'):
                return False, '必须选择活动场馆', None
            if not activity_data.get('start_time'):
                return False, '必须设置开始时间', None
            if not activity_data.get('end_time'):
                return False, '必须设置结束时间', None

            # 验证时间格式和逻辑
            from datetime import datetime
            try:
                start_time = datetime.strptime(activity_data['start_time'], '%Y-%m-%dT%H:%M')
                end_time = datetime.strptime(activity_data['end_time'], '%Y-%m-%dT%H:%M')
            except ValueError:
                return False, '时间格式不正确', None

            if end_time <= start_time:
                return False, '结束时间必须晚于开始时间', None

            # 验证场馆可用性
            venue = Venue.query.get(activity_data['venue_id'])
            if not venue:
                return False, '指定的场馆不存在', None

            if not venue.check_availability(start_time, end_time):
                return False, '该时间段场馆已被占用', None

            # 验证参与人数限制
            participant_limit = activity_data.get('participant_limit')
            if participant_limit is not None:
                try:
                    participant_limit = int(participant_limit)
                    if participant_limit < 0:
                        return False, '参与人数限制不能为负数', None
                except ValueError:
                    return False, '参与人数限制必须为数字', None

            # 创建新活动
            activity = Activity(
                ActivityID=generate_uuid(),
                ClubID=activity_data['club_id'],
                ActivityName=activity_data['activity_name'],
                ActivityType=activity_data.get('activity_type', '其他'),
                StartTime=start_time,
                EndTime=end_time,
                Description=activity_data.get('description', ''),
                OrganizerID=activity_data['organizer_id'],
                VenueID=activity_data['venue_id'],
                ParticipantLimit=participant_limit,
                Status=activity_data.get('status', '计划中'),
                ActualParticipant=0
            )

            db.session.add(activity)
            db.session.commit()

            return True, f'活动"{activity.ActivityName}"创建成功', activity.ActivityID

        except Exception as e:
            db.session.rollback()
            return False, f'创建活动失败：{str(e)}', None

    @staticmethod
    def update_activity(activity_id, activity_data):
        """
        更新活动信息

        Args:
            activity_id: 活动ID
            activity_data: 更新的活动数据

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            activity = Activity.query.get(activity_id)
            if not activity:
                return False, '活动不存在'

            # 更新活动名称
            if 'activity_name' in activity_data:
                if not activity_data['activity_name']:
                    return False, '活动名称不能为空'
                activity.ActivityName = activity_data['activity_name']

            # 更新时间
            if 'start_time' in activity_data and 'end_time' in activity_data:
                from datetime import datetime
                try:
                    start_time = datetime.strptime(activity_data['start_time'], '%Y-%m-%dT%H:%M')
                    end_time = datetime.strptime(activity_data['end_time'], '%Y-%m-%dT%H:%M')
                except ValueError:
                    return False, '时间格式不正确'

                if end_time <= start_time:
                    return False, '结束时间必须晚于开始时间'

                # 检查场馆可用性（排除当前活动）
                if 'venue_id' in activity_data:
                    venue = Venue.query.get(activity_data['venue_id'])
                    if not venue:
                        return False, '指定的场馆不存在'
                    if not venue.check_availability(start_time, end_time, activity_id):
                        return False, '该时间段场馆已被占用'
                    activity.VenueID = activity_data['venue_id']
                elif not activity.venue.check_availability(start_time, end_time, activity_id):
                    return False, '该时间段场馆已被占用'

                activity.StartTime = start_time
                activity.EndTime = end_time

            # 更新其他字段
            if 'activity_type' in activity_data:
                activity.ActivityType = activity_data['activity_type']
            if 'description' in activity_data:
                activity.Description = activity_data['description']
            if 'participant_limit' in activity_data:
                participant_limit = activity_data['participant_limit']
                if participant_limit is not None:
                    try:
                        participant_limit = int(participant_limit)
                        if participant_limit < 0:
                            return False, '参与人数限制不能为负数'
                    except ValueError:
                        return False, '参与人数限制必须为数字'
                activity.ParticipantLimit = participant_limit
            if 'status' in activity_data:
                activity.Status = activity_data['status']
            if 'actual_participant' in activity_data:
                try:
                    actual_participant = int(activity_data['actual_participant'])
                    if actual_participant < 0:
                        return False, '实际参与人数不能为负数'
                    activity.ActualParticipant = actual_participant
                except ValueError:
                    return False, '实际参与人数必须为数字'

            db.session.commit()

            return True, f'活动"{activity.ActivityName}"更新成功'

        except Exception as e:
            db.session.rollback()
            return False, f'更新活动失败：{str(e)}'

    @staticmethod
    def delete_activity(activity_id):
        """
        删除活动

        Args:
            activity_id: 活动ID

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            activity = Activity.query.get(activity_id)
            if not activity:
                return False, '活动不存在'

            activity_name = activity.ActivityName

            # 删除活动
            db.session.delete(activity)
            db.session.commit()

            return True, f'活动"{activity_name}"删除成功'

        except Exception as e:
            db.session.rollback()
            return False, f'删除活动失败：{str(e)}'

    @staticmethod
    def get_activity_by_id(activity_id):
        """
        根据ID获取活动信息

        Args:
            activity_id: 活动ID

        Returns:
            Activity: 活动对象或None
        """
        return Activity.query.get(activity_id)
