#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证视图模块
处理用户登录、注册、登出等认证相关的路由
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from app.controllers.auth_controller import AuthController
from app.utils.decorators import login_required
from app.utils.helpers import get_current_user

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """
    用户登录页面
    GET: 显示登录表单
    POST: 处理登录请求
    """
    # 如果已登录，重定向到首页
    if 'user_id' in session:
        return redirect(url_for('common.index'))
    
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        remember_me = request.form.get('remember_me') == 'on'
        
        # 调用控制器处理登录
        success, message, user = AuthController.login(username, password)
        
        if success:
            flash(message, 'success')
            
            # 设置会话持久化
            if remember_me:
                session.permanent = True
            
            # 重定向到原来要访问的页面或首页
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            
            # 根据用户角色重定向到不同页面
            if user.is_admin():
                return redirect(url_for('admin.dashboard'))
            elif user.is_president():
                return redirect(url_for('president.dashboard'))
            else:
                # 普通用户登录后跳转到系统首页
                return redirect(url_for('common.index'))
        else:
            flash(message, 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """
    用户注册页面
    GET: 显示注册表单
    POST: 处理注册请求
    """
    # 如果已登录，重定向到首页
    if 'user_id' in session:
        return redirect(url_for('common.index'))
    
    if request.method == 'POST':
        # 收集表单数据（严格对应Members表字段）
        form_data = {
            'username': request.form.get('username', '').strip(),
            'password': request.form.get('password', ''),
            'confirm_password': request.form.get('confirm_password', ''),
            'name': request.form.get('name', '').strip(),
            'age': request.form.get('age', '').strip(),
            'gender': request.form.get('gender', ''),
            'college': request.form.get('college', '').strip(),
            'dormitory': request.form.get('dormitory', '').strip(),
            'phone': request.form.get('phone', '').strip(),
            'specialty': request.form.get('specialty', '').strip(),
            'role': request.form.get('role', '')
        }
        
        # 验证密码确认
        if form_data['password'] != form_data['confirm_password']:
            flash('两次输入的密码不一致', 'error')
        else:
            # 调用控制器处理注册
            success, message, user = AuthController.register(form_data)
            
            if success:
                flash(message, 'success')
                return redirect(url_for('auth.login'))
            else:
                flash(message, 'error')
    
    return render_template('auth/register.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """
    用户登出
    """
    success, message = AuthController.logout()
    flash(message, 'success' if success else 'error')
    return redirect(url_for('common.index'))

@auth_bp.route('/profile')
@login_required
def profile():
    """
    用户个人资料页面
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/profile.html', user=user)

@auth_bp.route('/edit_profile', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """
    编辑个人资料
    GET: 显示编辑表单
    POST: 处理更新请求
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    if request.method == 'POST':
        # 收集表单数据
        form_data = {
            'name': request.form.get('name', '').strip(),
            'age': request.form.get('age', '').strip(),
            'gender': request.form.get('gender', ''),
            'college': request.form.get('college', '').strip(),
            'dormitory': request.form.get('dormitory', '').strip(),
            'phone': request.form.get('phone', '').strip(),
            'specialty': request.form.get('specialty', '').strip()
        }
        
        # 调用控制器处理更新
        success, message = AuthController.update_profile(user.MemberID, form_data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('auth.profile'))
        else:
            flash(message, 'error')
    
    return render_template('auth/edit_profile.html', user=user)

@auth_bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """
    修改密码
    GET: 显示修改密码表单
    POST: 处理密码修改请求
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    if request.method == 'POST':
        old_password = request.form.get('old_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')
        
        # 验证新密码确认
        if new_password != confirm_password:
            flash('两次输入的新密码不一致', 'error')
        else:
            # 调用控制器处理密码修改
            success, message = AuthController.change_password(
                user.MemberID, old_password, new_password
            )
            
            if success:
                flash(message, 'success')
                return redirect(url_for('auth.profile'))
            else:
                flash(message, 'error')
    
    return render_template('auth/change_password.html', user=user)

@auth_bp.route('/check_username')
def check_username():
    """
    检查用户名是否可用（AJAX接口）
    """
    username = request.args.get('username', '').strip()
    if not username:
        return {'available': False, 'message': '用户名不能为空'}
    
    available = AuthController.is_username_available(username)
    return {
        'available': available,
        'message': '用户名可用' if available else '用户名已存在'
    }
