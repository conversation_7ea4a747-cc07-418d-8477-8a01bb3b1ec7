#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会员视图模块
处理普通会员的页面路由和请求
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from app import db
from app.controllers.member_controller import MemberController
from app.utils.decorators import login_required, role_required
from app.utils.helpers import get_current_user
from app.models.club import Club
from app.models.activity import Activity

# 创建会员蓝图
member_bp = Blueprint('member', __name__)

@member_bp.route('/dashboard')
@login_required
@role_required('会员', '会长', '管理员')  # 允许所有角色访问会员功能
def dashboard():
    """
    会员仪表板
    显示个人信息、加入的社团、待审批申请、推荐社团等
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    # 获取仪表板数据
    dashboard_data = MemberController.get_member_dashboard_data(user.MemberID)
    
    if not dashboard_data:
        flash('获取用户数据失败', 'error')
        return redirect(url_for('auth.login'))
    
    return render_template('member/dashboard.html', **dashboard_data)

@member_bp.route('/clubs')
@login_required
@role_required('会员', '会长', '管理员')
def clubs():
    """
    可申请的社团列表
    支持搜索和分类过滤
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    # 获取查询参数
    search_query = request.args.get('search', '').strip()
    category = request.args.get('category', 'all')
    page = request.args.get('page', 1, type=int)
    per_page = 12  # 每页显示12个社团
    
    # 获取社团列表
    result = MemberController.get_available_clubs(
        user.MemberID, search_query, category, page, per_page
    )
    
    # 获取所有社团类别（用于过滤器）
    categories = db.session.query(Club.Description).distinct().all()
    categories = [cat[0] for cat in categories]
    
    return render_template('member/clubs.html',
                         clubs=result['clubs'],
                         pagination=result['pagination'],
                         search_query=search_query,
                         category=category,
                         categories=categories)

@member_bp.route('/club/<club_id>')
@login_required
@role_required('会员', '会长', '管理员')
def club_detail(club_id):
    """
    社团详情页面
    显示社团信息和申请入会按钮
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    club = Club.query.get_or_404(club_id)
    
    # 检查用户与该社团的关系
    from app.models.member_club import MemberClub
    member_club = MemberClub.query.filter_by(
        MemberID=user.MemberID,
        ClubID=club_id
    ).first()
    
    # 获取社团最近的活动
    recent_activities = club.activities.filter(
        Activity.Status.in_(['计划中', '进行中', '已完成'])
    ).order_by(Activity.StartTime.desc()).limit(5).all()

    # 获取社团成员数统计
    member_count = club.member_relationships.filter_by(Status='已批准').count()

    # 获取社团成员列表（只显示已批准的成员）
    club_members = MemberClub.query.filter_by(
        ClubID=club_id,
        Status='已批准'
    ).order_by(MemberClub.ApprovalTime.desc()).limit(20).all()

    # 获取用户与社团的关系状态
    user_club_status = None
    if member_club:
        user_club_status = member_club.Status

    return render_template('common/club_detail.html',
                         club=club,
                         member_club=member_club,
                         recent_activities=recent_activities,
                         member_count=member_count,
                         club_members=club_members,
                         user_club_status=user_club_status)

@member_bp.route('/apply_club/<club_id>', methods=['POST'])
@login_required
@role_required('会员', '会长', '管理员')
def apply_club(club_id):
    """
    申请加入社团
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    application_reason = request.form.get('application_reason', '').strip()
    
    # 调用控制器处理申请
    success, message = MemberController.apply_to_club(
        user.MemberID, club_id, application_reason
    )
    
    flash(message, 'success' if success else 'error')
    
    # 重定向回社团详情页面
    return redirect(url_for('member.club_detail', club_id=club_id))

@member_bp.route('/withdraw_club/<club_id>', methods=['POST'])
@login_required
@role_required('会员', '会长', '管理员')
def withdraw_club(club_id):
    """
    申请退出社团
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    withdraw_reason = request.form.get('withdraw_reason', '').strip()

    # 调用控制器处理退出申请
    success, message = MemberController.withdraw_from_club(
        user.MemberID, club_id, withdraw_reason
    )

    flash(message, 'success' if success else 'error')

    # 重定向回社团详情页面
    return redirect(url_for('member.club_detail', club_id=club_id))

@member_bp.route('/cancel_application/<club_id>', methods=['POST'])
@login_required
@role_required('会员', '会长', '管理员')
def cancel_application(club_id):
    """
    取消社团申请
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 调用控制器处理取消申请
    success, message = MemberController.cancel_club_application(
        user.MemberID, club_id
    )

    flash(message, 'success' if success else 'error')

    # 根据来源页面决定重定向位置
    referrer = request.referrer
    if referrer and 'my_clubs' in referrer:
        return redirect(url_for('member.my_clubs'))
    else:
        return redirect(url_for('member.club_detail', club_id=club_id))

@member_bp.route('/my_clubs')
@login_required
@role_required('会员', '会长', '管理员')
def my_clubs():
    """
    我的社团页面
    显示已加入、申请中、已拒绝的社团
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    # 获取不同状态的社团关系
    joined_clubs = MemberController.get_my_clubs(user.MemberID, '已批准')
    pending_clubs = MemberController.get_my_clubs(user.MemberID, '申请中')
    rejected_clubs = MemberController.get_my_clubs(user.MemberID, '已拒绝')
    withdrawn_clubs = MemberController.get_my_clubs(user.MemberID, '已退出')
    
    return render_template('member/my_clubs.html',
                         joined_clubs=joined_clubs,
                         pending_clubs=pending_clubs,
                         rejected_clubs=rejected_clubs,
                         withdrawn_clubs=withdrawn_clubs)




@member_bp.route('/my_activities')
@login_required
@role_required('会员', '会长', '管理员')
def my_activities():
    """
    我的活动页面
    显示参与的社团活动
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    from app.models.activity import Activity
    from app.models.member_club import MemberClub
    from datetime import datetime

    # 获取用户加入的社团
    user_clubs = MemberClub.query.filter_by(
        MemberID=user.MemberID,
        Status='已批准'
    ).all()

    club_ids = [mc.ClubID for mc in user_clubs]

    # 获取这些社团的活动
    all_activities = []
    upcoming_activities = []
    attended_activities = []

    if club_ids:
        activities = Activity.query.filter(Activity.OrganizerID.in_(club_ids)).order_by(Activity.StartTime.desc()).all()

        now = datetime.now()
        for activity in activities:
            # 模拟参与状态（实际应该从ActivityParticipants表获取）
            activity.attendance_status = '已报名'  # 默认状态

            if activity.StartTime and activity.StartTime > now:
                upcoming_activities.append(activity)
            elif activity.StartTime and activity.StartTime <= now:
                activity.attendance_status = '已签到'
                attended_activities.append(activity)

            all_activities.append(activity)

    # 统计数据
    stats = {
        'signed_up': len([a for a in all_activities if a.attendance_status == '已报名']),
        'attended': len([a for a in all_activities if a.attendance_status == '已签到']),
        'upcoming': len(upcoming_activities),
        'missed': len([a for a in all_activities if a.attendance_status == '缺席'])
    }

    return render_template('member/my_activities.html',
                         all_activities=all_activities,
                         upcoming_activities=upcoming_activities[:5],  # 最近5个
                         attended_activities=attended_activities[:5],  # 最近5个
                         stats=stats)

@member_bp.route('/activity/<activity_id>')
@login_required
@role_required('会员', '会长', '管理员')
def activity_detail(activity_id):
    """
    活动详情页面
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    from app.models.activity import Activity
    activity = Activity.query.get_or_404(activity_id)
    
    # 注释：取消活动查看的社团成员限制，允许所有用户查看活动详情
    # 检查用户是否有权限查看该活动（已取消社团成员限制）
    from app.models.member_club import MemberClub
    member_club = MemberClub.query.filter_by(
        MemberID=user.MemberID,
        ClubID=activity.ClubID,
        Status='已批准'
    ).first()

    # 取消权限限制，允许所有登录用户查看活动详情
    # if not member_club and not user.is_admin():
    #     flash('您没有权限查看该活动', 'error')
    #     return redirect(url_for('member.my_activities'))
    
    return render_template('common/activity_detail.html',
                         activity=activity,
                         member_club=member_club)

@member_bp.route('/join_activity/<activity_id>', methods=['POST'])
@login_required
@role_required('会员', '会长', '管理员')
def join_activity(activity_id):
    """
    报名参加活动
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    from app.models.activity import Activity
    activity = Activity.query.get_or_404(activity_id)

    # 注释：取消社团成员限制，允许所有用户报名参加活动
    # 检查用户是否是该社团成员（已取消此限制）
    # from app.models.member_club import MemberClub
    # member_club = MemberClub.query.filter_by(
    #     MemberID=user.MemberID,
    #     ClubID=activity.OrganizerID,
    #     Status='已批准'
    # ).first()

    # if not member_club:
    #     flash('只有社团成员才能报名参加活动', 'error')
    #     return redirect(url_for('common.activity_detail', activity_id=activity_id))

    # 检查活动状态
    if activity.Status != '计划中':
        flash('该活动当前不接受报名', 'error')
        return redirect(url_for('common.activity_detail', activity_id=activity_id))

    # 调用控制器处理活动报名
    success, message = MemberController.join_activity(user.MemberID, activity_id)

    flash(message, 'success' if success else 'error')
    return redirect(url_for('common.activity_detail', activity_id=activity_id))

@member_bp.route('/search_clubs')
@login_required
@role_required('会员', '会长', '管理员')
def search_clubs():
    """
    AJAX搜索社团接口
    """
    user = get_current_user()
    if not user:
        return jsonify({'error': '用户未登录'})
    
    query = request.args.get('q', '').strip()
    if not query:
        return jsonify({'clubs': []})
    
    # 搜索社团
    result = MemberController.get_available_clubs(
        user.MemberID, query, None, 1, 10
    )
    
    clubs_data = []
    for club in result['clubs']:
        clubs_data.append({
            'id': club.ClubID,
            'name': club.ClubName,
            'description': club.Description,
            'current_members': club.CurrentMembers,
            'max_members': club.MaxMembers,
            'president_name': club.president.Name if club.president else ''
        })
    
    return jsonify({'clubs': clubs_data})

@member_bp.route('/profile')
@login_required
@role_required('会员', '会长', '管理员')
def profile():
    """
    个人资料页面重定向到个人中心
    """
    return redirect(url_for('member.dashboard'))

@member_bp.route('/update_profile', methods=['POST'])
@login_required
@role_required('会员', '会长', '管理员')
def update_profile():
    """
    更新个人资料
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    try:
        # 更新用户信息
        user.Name = request.form.get('name', '').strip()
        user.Phone = request.form.get('phone', '').strip()
        user.Email = request.form.get('email', '').strip()
        user.College = request.form.get('college', '').strip()
        user.Specialty = request.form.get('specialty', '').strip()
        user.Dormitory = request.form.get('dormitory', '').strip()
        user.Gender = request.form.get('gender', '').strip()

        age_str = request.form.get('age', '').strip()
        if age_str:
            user.Age = int(age_str)

        db.session.commit()
        flash('个人资料更新成功', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'更新失败：{str(e)}', 'error')

    return redirect(url_for('member.dashboard'))

@member_bp.route('/apply_create_club', methods=['GET', 'POST'])
@login_required
@role_required('会员', '会长', '管理员')
def apply_create_club():
    """
    申请创建社团
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    if request.method == 'POST':
        # 获取表单数据
        club_data = {
            'club_name': request.form.get('club_name', '').strip(),
            'description': request.form.get('description', '学术'),
            'max_members': request.form.get('max_members', type=int),
            'website': request.form.get('website', '').strip(),
            'application_reason': request.form.get('application_reason', '').strip()
        }

        # 基本验证
        if not club_data['club_name']:
            flash('社团名称不能为空', 'error')
            return render_template('member/apply_create_club.html')

        if not club_data['application_reason']:
            flash('申请理由不能为空', 'error')
            return render_template('member/apply_create_club.html')

        # 调用控制器处理申请
        success, message = MemberController.apply_create_club(user.MemberID, club_data)

        if success:
            flash(message, 'success')
            return redirect(url_for('member.dashboard'))
        else:
            flash(message, 'error')

    return render_template('member/apply_create_club.html')
