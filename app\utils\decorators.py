#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权限装饰器模块
基于Members表的Role字段实现三级权限控制：管理员、会长、会员
"""

from functools import wraps
from flask import session, redirect, url_for, flash, request
from app.models.member import Member

def login_required(f):
    """
    登录验证装饰器
    验证用户是否已登录
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """
    管理员权限装饰器
    验证用户是否为管理员（Members.Role = '管理员'）
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        
        user = Member.query.get(session['user_id'])
        if not user or not user.is_admin():
            flash('权限不足，需要管理员权限', 'error')
            return redirect(url_for('common.index'))
        
        return f(*args, **kwargs)
    return decorated_function

def president_required(f):
    """
    会长权限装饰器
    验证用户是否为会长（Members.Role = '会长'）
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        
        user = Member.query.get(session['user_id'])
        if not user or not user.is_president():
            flash('权限不足，需要会长权限', 'error')
            return redirect(url_for('common.index'))
        
        return f(*args, **kwargs)
    return decorated_function

def member_required(f):
    """
    会员权限装饰器
    验证用户是否为会员（Members.Role = '会员'）
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        
        user = Member.query.get(session['user_id'])
        if not user or not user.is_member():
            flash('权限不足，需要会员权限', 'error')
            return redirect(url_for('common.index'))
        
        return f(*args, **kwargs)
    return decorated_function

def role_required(*allowed_roles):
    """
    角色权限装饰器（通用）
    验证用户是否具有指定角色之一
    
    Args:
        allowed_roles: 允许的角色列表，如 '管理员', '会长', '会员'
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                flash('请先登录', 'warning')
                return redirect(url_for('auth.login', next=request.url))
            
            user = Member.query.get(session['user_id'])
            if not user or user.Role not in allowed_roles:
                flash(f'权限不足，需要以下角色之一：{", ".join(allowed_roles)}', 'error')
                return redirect(url_for('common.index'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def club_president_required(club_id_param='club_id'):
    """
    社团会长权限装饰器
    验证用户是否为指定社团的会长
    
    Args:
        club_id_param: URL参数中社团ID的参数名
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                flash('请先登录', 'warning')
                return redirect(url_for('auth.login', next=request.url))
            
            user = Member.query.get(session['user_id'])
            if not user:
                flash('用户不存在', 'error')
                return redirect(url_for('auth.login'))
            
            # 管理员拥有所有权限
            if user.is_admin():
                return f(*args, **kwargs)
            
            # 检查是否为指定社团的会长
            club_id = kwargs.get(club_id_param) or request.view_args.get(club_id_param)
            if not club_id:
                flash('社团ID参数缺失', 'error')
                return redirect(url_for('common.index'))
            
            from app.models.club import Club
            club = Club.query.get(club_id)
            if not club:
                flash('社团不存在', 'error')
                return redirect(url_for('common.index'))
            
            if club.PresidentID != user.MemberID:
                flash('权限不足，您不是该社团的会长', 'error')
                return redirect(url_for('common.index'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
