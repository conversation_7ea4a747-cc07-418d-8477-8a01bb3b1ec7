{% extends "admin_base.html" %}

{% block title %}系统信息 - 管理后台{% endblock %}
{% block page_title %}系统信息{% endblock %}
{% block title_icon %}<i class="fas fa-info-circle"></i>{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-nav">
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.dashboard') }}">
                <i class="fas fa-home"></i>
            </a>
        </li>
        <li class="breadcrumb-item active">系统信息</li>
    </ol>
</nav>
{% endblock %}

{% block page_actions %}
<div class="d-flex justify-content-between align-items-center mt-3">
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-primary" onclick="refreshSystemInfo()">
            <i class="fas fa-sync me-1"></i>刷新信息
        </button>
        <button type="button" class="btn btn-outline-secondary" onclick="exportSystemInfo()">
            <i class="fas fa-download me-1"></i>导出信息
        </button>
        <button type="button" class="btn btn-outline-warning" onclick="clearCache()">
            <i class="fas fa-broom me-1"></i>清理缓存
        </button>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 系统状态概览 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle fa-3x"></i>
                </div>
                <h5 class="card-title">系统状态</h5>
                <p class="card-text text-success fw-bold">正常运行</p>
                <small class="text-muted">运行时间: {{ system_info.uptime }}</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-primary mb-2">
                    <i class="fas fa-database fa-3x"></i>
                </div>
                <h5 class="card-title">数据库</h5>
                <p class="card-text text-success fw-bold">连接正常</p>
                <small class="text-muted">响应时间: {{ system_info.db_response_time }}ms</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-warning mb-2">
                    <i class="fas fa-memory fa-3x"></i>
                </div>
                <h5 class="card-title">内存使用</h5>
                <p class="card-text fw-bold">{{ system_info.memory_usage }}%</p>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar bg-warning" style="width: {{ system_info.memory_usage }}%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-info mb-2">
                    <i class="fas fa-hdd fa-3x"></i>
                </div>
                <h5 class="card-title">磁盘使用</h5>
                <p class="card-text fw-bold">{{ system_info.disk_usage }}%</p>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar bg-info" style="width: {{ system_info.disk_usage }}%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细信息 -->
<div class="row">
    <!-- 系统环境信息 -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2 text-primary"></i>系统环境
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tbody>
                        <tr>
                            <td class="fw-bold">操作系统</td>
                            <td>{{ system_info.os_name }} {{ system_info.os_version }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Python版本</td>
                            <td>{{ system_info.python_version }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Flask版本</td>
                            <td>{{ system_info.flask_version }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">数据库版本</td>
                            <td>{{ system_info.db_version }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">服务器时间</td>
                            <td>{{ system_info.server_time }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">时区</td>
                            <td>{{ system_info.timezone }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 应用信息 -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-graduation-cap me-2 text-success"></i>应用信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tbody>
                        <tr>
                            <td class="fw-bold">应用名称</td>
                            <td>学校社团管理系统</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">版本号</td>
                            <td>v1.0.0</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">开发者</td>
                            <td>Augment Code AI</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">部署时间</td>
                            <td>{{ system_info.deploy_time }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">最后更新</td>
                            <td>{{ system_info.last_update }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">运行模式</td>
                            <td>
                                <span class="badge bg-{{ 'danger' if system_info.debug_mode else 'success' }}">
                                    {{ '开发模式' if system_info.debug_mode else '生产模式' }}
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 数据库统计 -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-database me-2 text-warning"></i>数据库统计
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tbody>
                        <tr>
                            <td class="fw-bold">数据库大小</td>
                            <td>{{ system_info.db_size }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">表数量</td>
                            <td>{{ system_info.table_count }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">总记录数</td>
                            <td>{{ system_info.total_records }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">用户表记录</td>
                            <td>{{ system_info.member_count }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">社团表记录</td>
                            <td>{{ system_info.club_count }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">活动表记录</td>
                            <td>{{ system_info.activity_count }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 系统配置 -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2 text-info"></i>系统配置
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tbody>
                        <tr>
                            <td class="fw-bold">CSRF保护</td>
                            <td>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>已启用
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">会话安全</td>
                            <td>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>已启用
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">数据库连接池</td>
                            <td>{{ system_info.db_pool_size }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">最大文件上传</td>
                            <td>{{ system_info.max_file_size }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">会话超时</td>
                            <td>{{ system_info.session_timeout }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">日志级别</td>
                            <td>
                                <span class="badge bg-{{ 'warning' if system_info.log_level == 'DEBUG' else 'info' }}">
                                    {{ system_info.log_level }}
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 系统日志 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2 text-secondary"></i>系统日志
                </h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshLogs()">
                        <i class="fas fa-sync me-1"></i>刷新
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="downloadLogs()">
                        <i class="fas fa-download me-1"></i>下载
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="clearLogs()">
                        <i class="fas fa-trash me-1"></i>清空
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-sm table-hover mb-0">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th width="15%">时间</th>
                                <th width="10%">级别</th>
                                <th width="15%">模块</th>
                                <th width="60%">消息</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in system_logs %}
                            <tr>
                                <td class="text-muted small">{{ log.timestamp }}</td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if log.level == 'ERROR' else 'warning' if log.level == 'WARNING' else 'info' if log.level == 'INFO' else 'secondary' }}">
                                        {{ log.level }}
                                    </span>
                                </td>
                                <td class="text-muted small">{{ log.module }}</td>
                                <td class="small">{{ log.message }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center py-4 text-muted">
                                    <i class="fas fa-file-alt" style="font-size: 2rem;"></i>
                                    <div class="mt-2">暂无日志记录</div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统维护 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2 text-warning"></i>系统维护
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="optimizeDatabase()">
                            <i class="fas fa-database me-2"></i>
                            <div>优化数据库</div>
                            <small class="text-muted">清理冗余数据</small>
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-success w-100" onclick="backupDatabase()">
                            <i class="fas fa-save me-2"></i>
                            <div>备份数据库</div>
                            <small class="text-muted">创建数据备份</small>
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-warning w-100" onclick="clearCache()">
                            <i class="fas fa-broom me-2"></i>
                            <div>清理缓存</div>
                            <small class="text-muted">清空系统缓存</small>
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-danger w-100" onclick="restartSystem()">
                            <i class="fas fa-redo me-2"></i>
                            <div>重启系统</div>
                            <small class="text-muted">重启应用服务</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 刷新系统信息
function refreshSystemInfo() {
    location.reload();
}

// 导出系统信息
function exportSystemInfo() {
    window.open('/admin/system-info/export', '_blank');
}

// 清理缓存
function clearCache() {
    if (confirm('确定要清理系统缓存吗？')) {
        fetch('/admin/system-info/clear-cache', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('缓存清理成功');
                location.reload();
            } else {
                alert('缓存清理失败：' + data.message);
            }
        })
        .catch(error => {
            alert('缓存清理失败：' + error.message);
        });
    }
}

// 优化数据库
function optimizeDatabase() {
    if (confirm('确定要优化数据库吗？此操作可能需要一些时间。')) {
        fetch('/admin/system-info/optimize-database', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('数据库优化成功');
                location.reload();
            } else {
                alert('数据库优化失败：' + data.message);
            }
        })
        .catch(error => {
            alert('数据库优化失败：' + error.message);
        });
    }
}

// 备份数据库
function backupDatabase() {
    if (confirm('确定要备份数据库吗？')) {
        fetch('/admin/system-info/backup-database', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('数据库备份成功，备份文件：' + data.backup_file);
            } else {
                alert('数据库备份失败：' + data.message);
            }
        })
        .catch(error => {
            alert('数据库备份失败：' + error.message);
        });
    }
}

// 重启系统
function restartSystem() {
    if (confirm('确定要重启系统吗？这将中断所有用户的连接。')) {
        fetch('/admin/system-info/restart-system', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('系统重启指令已发送，请稍等片刻后刷新页面。');
                setTimeout(() => {
                    location.reload();
                }, 5000);
            } else {
                alert('系统重启失败：' + data.message);
            }
        })
        .catch(error => {
            alert('系统重启失败：' + error.message);
        });
    }
}

// 刷新日志
function refreshLogs() {
    fetch('/admin/system-info/logs')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新日志表格
                location.reload();
            } else {
                alert('刷新日志失败：' + data.message);
            }
        })
        .catch(error => {
            alert('刷新日志失败：' + error.message);
        });
}

// 下载日志
function downloadLogs() {
    window.open('/admin/system-info/download-logs', '_blank');
}

// 清空日志
function clearLogs() {
    if (confirm('确定要清空所有日志吗？此操作不可恢复。')) {
        fetch('/admin/system-info/clear-logs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('日志清空成功');
                location.reload();
            } else {
                alert('日志清空失败：' + data.message);
            }
        })
        .catch(error => {
            alert('日志清空失败：' + error.message);
        });
    }
}

// 自动刷新系统状态
setInterval(function() {
    // 每30秒刷新一次系统状态
    fetch('/admin/api/system-status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新内存和磁盘使用率
                document.querySelector('.progress-bar.bg-warning').style.width = data.memory_usage + '%';
                document.querySelector('.progress-bar.bg-info').style.width = data.disk_usage + '%';
            }
        })
        .catch(error => {
            console.error('获取系统状态失败:', error);
        });
}, 30000);
</script>
{% endblock %}
