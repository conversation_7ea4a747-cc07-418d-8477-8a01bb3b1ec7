{% extends "base.html" %}

{% block title %}我的社团{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-2">
                        <i class="fas fa-layer-group me-2 text-primary"></i>我的社团
                    </h2>
                    <p class="text-muted mb-0">管理你的社团申请和参与情况</p>
                </div>
                <div>
                    <a href="{{ url_for('member.dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回个人中心
                    </a>
                    <a href="{{ url_for('common.clubs') }}" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>浏览更多社团
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">{{ joined_clubs|length }}</h4>
                    <p class="text-muted mb-0">已加入</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">{{ pending_clubs|length }}</h4>
                    <p class="text-muted mb-0">申请中</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-danger mb-2">
                        <i class="fas fa-times-circle" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">{{ rejected_clubs|length }}</h4>
                    <p class="text-muted mb-0">已拒绝</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-secondary mb-2">
                        <i class="fas fa-sign-out-alt" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mb-1">{{ withdrawn_clubs|length }}</h4>
                    <p class="text-muted mb-0">已退出</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 已加入的社团 -->
    {% if joined_clubs %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle me-2 text-success"></i>已加入的社团
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for member_club in joined_clubs %}
                        <div class="col-lg-6 mb-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        <div class="flex-shrink-0">
                                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-layer-group"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="mb-1">{{ member_club.club.ClubName }}</h6>
                                            <p class="text-muted mb-2 small">{{ member_club.club.Description }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-success">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ member_club.ApprovalTime.strftime('%Y年%m月%d日') if member_club.ApprovalTime else '未知' }} 加入
                                                </small>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ url_for('common.club_detail', club_id=member_club.ClubID) }}" 
                                                       class="btn btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i>查看
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger"
                                                            onclick="withdrawClub('{{ member_club.ClubID }}', '{{ member_club.club.ClubName }}')">
                                                        <i class="fas fa-sign-out-alt me-1"></i>申请退出
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 申请中的社团 -->
    {% if pending_clubs %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-warning"></i>申请中的社团
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for member_club in pending_clubs %}
                        <div class="col-lg-6 mb-3">
                            <div class="card border-warning">
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        <div class="flex-shrink-0">
                                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="mb-1">{{ member_club.club.ClubName }}</h6>
                                            <p class="text-muted mb-2 small">{{ member_club.club.Description }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-warning">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ member_club.ApplyTime.strftime('%Y年%m月%d日') }} 申请
                                                </small>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ url_for('common.club_detail', club_id=member_club.ClubID) }}" 
                                                       class="btn btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i>查看
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="cancelApplication('{{ member_club.ClubID }}', '{{ member_club.club.ClubName }}')">
                                                        <i class="fas fa-times me-1"></i>取消申请
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 已拒绝的社团 -->
    {% if rejected_clubs %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-times-circle me-2 text-danger"></i>已拒绝的申请
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for member_club in rejected_clubs %}
                        <div class="col-lg-6 mb-3">
                            <div class="card border-danger">
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        <div class="flex-shrink-0">
                                            <div class="bg-danger text-white rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-times-circle"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="mb-1">{{ member_club.club.ClubName }}</h6>
                                            <p class="text-muted mb-2 small">{{ member_club.club.Description }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-danger">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ member_club.ApprovalTime.strftime('%Y年%m月%d日') if member_club.ApprovalTime else '未知' }} 拒绝
                                                </small>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ url_for('common.club_detail', club_id=member_club.ClubID) }}" 
                                                       class="btn btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i>查看
                                                    </a>
                                                    {% if member_club.Rejoinable == '是' %}
                                                    <button type="button" class="btn btn-outline-success" 
                                                            onclick="reapplyClub('{{ member_club.ClubID }}', '{{ member_club.club.ClubName }}')">
                                                        <i class="fas fa-redo me-1"></i>重新申请
                                                    </button>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 已退出的社团 -->
    {% if withdrawn_clubs %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-sign-out-alt me-2 text-secondary"></i>已退出的社团
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for member_club in withdrawn_clubs %}
                        <div class="col-lg-6 mb-3">
                            <div class="card border-secondary">
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        <div class="flex-shrink-0">
                                            <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-sign-out-alt"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="mb-1">{{ member_club.club.ClubName }}</h6>
                                            <p class="text-muted mb-2 small">{{ member_club.club.Description }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-secondary">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ member_club.ApprovalTime.strftime('%Y年%m月%d日') if member_club.ApprovalTime else '未知' }} 退出
                                                </small>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ url_for('common.club_detail', club_id=member_club.ClubID) }}" 
                                                       class="btn btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i>查看
                                                    </a>
                                                    {% if member_club.Rejoinable == '是' %}
                                                    <button type="button" class="btn btn-outline-success" 
                                                            onclick="reapplyClub('{{ member_club.ClubID }}', '{{ member_club.club.ClubName }}')">
                                                        <i class="fas fa-redo me-1"></i>重新申请
                                                    </button>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 空状态 -->
    {% if not joined_clubs and not pending_clubs and not rejected_clubs and not withdrawn_clubs %}
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-layer-group text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">还没有社团记录</h4>
                <p class="text-muted mb-4">快去申请加入你感兴趣的社团吧！</p>
                <a href="{{ url_for('common.clubs') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-search me-2"></i>浏览社团
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 退出社团模态框 -->
<div class="modal fade" id="withdrawModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">申请退出社团</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="withdrawForm" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <p>您正在申请退出 "<span id="withdrawClubName"></span>"</p>
                    <div class="mb-3">
                        <label for="withdrawReason" class="form-label">退出理由</label>
                        <textarea class="form-control" id="withdrawReason" name="withdraw_reason" rows="4"
                                  placeholder="请简要说明您退出该社团的理由..." required></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        退出申请提交后，需要等待社团会长审批。审批通过后您将失去社团成员身份。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">提交退出申请</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 退出社团
function withdrawClub(clubId, clubName) {
    document.getElementById('withdrawClubName').textContent = clubName;
    document.getElementById('withdrawForm').action = `/member/withdraw_club/${clubId}`;
    new bootstrap.Modal(document.getElementById('withdrawModal')).show();
}

// 取消申请
function cancelApplication(clubId, clubName) {
    if (confirm(`确定要取消申请加入"${clubName}"吗？`)) {
        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/member/cancel_application/${clubId}`;

        // 添加CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '{{ csrf_token() }}';
        form.appendChild(csrfInput);

        // 提交表单
        document.body.appendChild(form);
        form.submit();
    }
}

// 重新申请
function reapplyClub(clubId, clubName) {
    if (confirm(`确定要重新申请加入"${clubName}"吗？`)) {
        window.location.href = `/club/${clubId}`;
    }
}
</script>
{% endblock %}
