{% extends "base.html" %}

{% block title %}我的活动 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="fas fa-calendar-alt me-2 text-primary"></i>我的活动
            </h2>
            <p class="text-muted">查看您参与的所有社团活动</p>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-3">
                        <i class="bi bi-calendar-check" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="mb-1">{{ stats.signed_up }}</h3>
                    <p class="text-muted mb-0">已报名活动</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-3">
                        <i class="bi bi-check-circle" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="mb-1">{{ stats.attended }}</h3>
                    <p class="text-muted mb-0">已参加活动</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-3">
                        <i class="bi bi-clock-history" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="mb-1">{{ stats.upcoming }}</h3>
                    <p class="text-muted mb-0">即将参加</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-danger mb-3">
                        <i class="bi bi-x-circle" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="mb-1">{{ stats.missed }}</h3>
                    <p class="text-muted mb-0">缺席活动</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 即将参与的活动 -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2 text-warning"></i>即将参与的活动
                        </h5>
                        <a href="{{ url_for('common.activities') }}" class="btn btn-sm btn-outline-primary">
                            浏览更多
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if upcoming_activities %}
                        {% for activity in upcoming_activities %}
                        <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                            <div class="flex-shrink-0">
                                <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="bi bi-calendar-event"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ activity.ActivityName }}</h6>
                                <p class="text-muted mb-1 small">{{ activity.club.ClubName if activity.club else '未知社团' }}</p>
                                <small class="text-warning">
                                    <i class="bi bi-calendar me-1"></i>
                                    {{ activity.StartTime.strftime('%m月%d日 %H:%M') if activity.StartTime else '时间待定' }}
                                </small>
                            </div>
                            <div class="flex-shrink-0">
                                <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}"
                                   class="btn btn-sm btn-outline-primary">
                                    详情
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">暂无即将参与的活动</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 已参加的活动 -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2 text-success"></i>已参加的活动
                        </h5>
                        <span class="badge bg-success">{{ attended_activities|length }}</span>
                    </div>
                </div>
                <div class="card-body">
                    {% if attended_activities %}
                        {% for activity in attended_activities %}
                        <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                            <div class="flex-shrink-0">
                                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ activity.ActivityName }}</h6>
                                <p class="text-muted mb-1 small">{{ activity.club.ClubName if activity.club else '未知社团' }}</p>
                                <small class="text-success">
                                    <i class="bi bi-calendar me-1"></i>
                                    {{ activity.StartTime.strftime('%m月%d日') if activity.StartTime else '时间待定' }}
                                </small>
                            </div>
                            <div class="flex-shrink-0">
                                <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}"
                                   class="btn btn-sm btn-outline-primary">
                                    详情
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-check-circle text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">暂无已参加的活动</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 所有活动记录 -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2 text-info"></i>所有活动记录
                </h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary active" data-filter="all">全部</button>
                    <button type="button" class="btn btn-outline-secondary" data-filter="signed">已报名</button>
                    <button type="button" class="btn btn-outline-secondary" data-filter="attended">已参加</button>
                    <button type="button" class="btn btn-outline-secondary" data-filter="missed">缺席</button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            {% if all_activities %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>活动名称</th>
                                <th>社团</th>
                                <th>开始时间</th>
                                <th>地点</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for activity in all_activities %}
                            <tr data-status="{{ activity.attendance_status }}">
                                <td>
                                    <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}" 
                                       class="text-decoration-none fw-medium">
                                        {{ activity.ActivityName }}
                                    </a>
                                </td>
                                <td>{{ activity.club.ClubName if activity.club else '未知社团' }}</td>
                                <td>
                                    <small>{{ activity.StartTime.strftime('%Y-%m-%d %H:%M') if activity.StartTime else '时间待定' }}</small>
                                </td>
                                <td>{{ activity.Location or '地点待定' }}</td>
                                <td>
                                    {% if activity.attendance_status == '已报名' %}
                                        <span class="badge bg-primary">已报名</span>
                                    {% elif activity.attendance_status == '已签到' %}
                                        <span class="badge bg-success">已参加</span>
                                    {% elif activity.attendance_status == '缺席' %}
                                        <span class="badge bg-danger">缺席</span>
                                    {% else %}
                                        <span class="badge bg-secondary">未知</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if activity.attendance_status == '已报名' and activity.Status == '计划中' %}
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="cancelActivity('{{ activity.ActivityID }}')">
                                            取消报名
                                        </button>
                                    {% else %}
                                        <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            查看详情
                                        </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="p-4 text-center text-muted">
                    <i class="fas fa-calendar-times" style="font-size: 3rem;"></i>
                    <div class="mt-3">您还没有参加过任何活动</div>
                    <div class="mt-2">
                        <a href="{{ url_for('common.activities') }}" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>浏览活动
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 活动状态筛选
document.querySelectorAll('[data-filter]').forEach(button => {
    button.addEventListener('click', function() {
        const filter = this.dataset.filter;
        
        // 更新按钮状态
        document.querySelectorAll('[data-filter]').forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');
        
        // 筛选表格行
        document.querySelectorAll('tbody tr[data-status]').forEach(row => {
            if (filter === 'all') {
                row.style.display = '';
            } else {
                const status = row.dataset.status;
                if ((filter === 'signed' && status === '已报名') ||
                    (filter === 'attended' && status === '已签到') ||
                    (filter === 'missed' && status === '缺席')) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });
    });
});

// 取消活动报名
function cancelActivity(activityId) {
    if (confirm('确定要取消报名吗？')) {
        fetch(`/member/cancel_activity/${activityId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '取消报名失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请重试');
        });
    }
}
</script>
{% endblock %}
