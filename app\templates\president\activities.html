{% extends "admin_base.html" %}

{% block title %}活动管理 - 会长后台{% endblock %}

{% block breadcrumb_title %}活动管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">活动管理</h2>
            <p class="text-muted mb-0">管理所有社团的活动信息</p>
        </div>
        <div>
            {% if clubs %}
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-plus me-1"></i>创建活动
                </button>
                <ul class="dropdown-menu">
                    {% for club in clubs %}
                    <li>
                        <a class="dropdown-item" href="{{ url_for('president.create_activity', club_id=club.ClubID) }}">
                            <i class="fas fa-layer-group me-2"></i>{{ club.ClubName }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchInput" placeholder="搜索活动名称、社团、地点...">
            </div>
        </div>
        <div class="col-md-4">
            <select class="form-select" id="statusFilter">
                <option value="">全部状态</option>
                <option value="计划中">计划中</option>
                <option value="进行中">进行中</option>
                <option value="已完成">已完成</option>
                <option value="已取消">已取消</option>
            </select>
        </div>
    </div>

    <!-- 活动数据列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2 text-primary"></i>活动列表
                        </h5>
                        <div class="d-flex gap-2">
                            <span class="badge bg-warning">计划中: {{ activities_data.planned|length if activities_data.planned else 0 }}</span>
                            <span class="badge bg-success">进行中: {{ activities_data.ongoing|length if activities_data.ongoing else 0 }}</span>
                            <span class="badge bg-info">已完成: {{ activities_data.completed|length if activities_data.completed else 0 }}</span>
                            <span class="badge bg-danger">已取消: {{ activities_data.cancelled|length if activities_data.cancelled else 0 }}</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% set all_activities = (activities_data.planned + activities_data.ongoing + activities_data.completed + activities_data.cancelled) if activities_data else [] %}
                    {% if all_activities %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0" id="activitiesTable">
                            <thead class="table-light">
                                <tr>
                                    <th>活动名称</th>
                                    <th>活动类型</th>
                                    <th>所属社团</th>
                                    <th>活动时间</th>
                                    <th>活动地点</th>
                                    <th>参与人数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in all_activities %}
                                <tr data-status="{{ activity.Status }}" data-search="{{ activity.ActivityName|lower }} {{ activity.club.ClubName|lower if activity.club else '' }} {{ activity.venue.VenueName|lower if activity.venue else '' }}">
                                    <td>
                                        <div class="fw-semibold">{{ activity.ActivityName }}</div>
                                        {% if activity.Description %}
                                        <small class="text-muted">{{ activity.Description[:50] }}{% if activity.Description|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ activity.ActivityType }}</span>
                                    </td>
                                    <td>
                                        <div>{{ activity.club.ClubName if activity.club else '未知社团' }}</div>
                                        {% if activity.organizer %}
                                        <small class="text-muted">组织者: {{ activity.organizer.Name or activity.organizer.Username }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>{{ activity.StartTime.strftime('%Y-%m-%d') if activity.StartTime else '未知' }}</div>
                                        <small class="text-muted">
                                            {{ activity.StartTime.strftime('%H:%M') if activity.StartTime else '' }}
                                            {% if activity.EndTime %} - {{ activity.EndTime.strftime('%H:%M') }}{% endif %}
                                        </small>
                                    </td>
                                    <td>
                                        <div>{{ activity.venue.VenueName if activity.venue else '未知地点' }}</div>
                                        {% if activity.venue and activity.venue.Location %}
                                        <small class="text-muted">{{ activity.venue.Location }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <div class="fw-bold">{{ activity.ActualParticipant }}</div>
                                            <small class="text-muted">
                                                {% if activity.ParticipantLimit %}
                                                / {{ activity.ParticipantLimit }}
                                                {% else %}
                                                / 无限制
                                                {% endif %}
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if activity.Status == '计划中' %}
                                        <span class="badge bg-warning">{{ activity.Status }}</span>
                                        {% elif activity.Status == '进行中' %}
                                        <span class="badge bg-success">{{ activity.Status }}</span>
                                        {% elif activity.Status == '已完成' %}
                                        <span class="badge bg-info">{{ activity.Status }}</span>
                                        {% elif activity.Status == '已取消' %}
                                        <span class="badge bg-danger">{{ activity.Status }}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ activity.Status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('president.activity_detail', activity_id=activity.ActivityID) }}"
                                               class="btn btn-outline-primary btn-sm" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if activity.Status == '计划中' %}
                                            <a href="{{ url_for('president.edit_activity', activity_id=activity.ActivityID) }}"
                                               class="btn btn-outline-secondary btn-sm" title="编辑活动">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-alt text-muted" style="font-size: 4rem;"></i>
                        <h4 class="mt-3 text-muted">暂无活动</h4>
                        <p class="text-muted">开始创建您的第一个活动吧！</p>
                        {% if clubs %}
                        <div class="dropdown">
                            <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-plus me-1"></i>创建活动
                            </button>
                            <ul class="dropdown-menu">
                                {% for club in clubs %}
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('president.create_activity', club_id=club.ClubID) }}">
                                        <i class="fas fa-layer-group me-2"></i>{{ club.ClubName }}
                                    </a>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    {% if not clubs %}
    <!-- 空状态 -->
    <div class="text-center py-5">
        <i class="fas fa-calendar-alt text-muted" style="font-size: 4rem;"></i>
        <h4 class="mt-3 text-muted">暂无管理的社团</h4>
        <p class="text-muted">您当前没有管理任何社团，无法进行活动管理。</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    border-radius: 12px;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

.badge {
    font-size: 0.75rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();

    // 搜索功能
    $('#searchInput').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterTable();
    });

    // 状态筛选功能
    $('#statusFilter').on('change', function() {
        filterTable();
    });

    // 表格筛选函数
    function filterTable() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const statusFilter = $('#statusFilter').val();

        $('#activitiesTable tbody tr').each(function() {
            const $row = $(this);
            const searchData = $row.data('search') || '';
            const status = $row.data('status') || '';

            let showRow = true;

            // 搜索筛选
            if (searchTerm && !searchData.includes(searchTerm)) {
                showRow = false;
            }

            // 状态筛选
            if (statusFilter && status !== statusFilter) {
                showRow = false;
            }

            if (showRow) {
                $row.show();
            } else {
                $row.hide();
            }
        });

        // 更新显示的行数
        updateRowCount();
    }

    // 更新行数显示
    function updateRowCount() {
        const totalRows = $('#activitiesTable tbody tr').length;
        const visibleRows = $('#activitiesTable tbody tr:visible').length;

        if (totalRows !== visibleRows) {
            if ($('#rowCount').length === 0) {
                $('#activitiesTable').after('<div id="rowCount" class="text-muted small mt-2"></div>');
            }
            $('#rowCount').text(`显示 ${visibleRows} / ${totalRows} 个活动`);
        } else {
            $('#rowCount').remove();
        }
    }

    // 表格排序功能（简单实现）
    $('.table th').click(function() {
        const table = $(this).parents('table').eq(0);
        const rows = table.find('tr:gt(0)').toArray().sort(comparer($(this).index()));
        this.asc = !this.asc;
        if (!this.asc) {
            rows.reverse();
        }
        for (let i = 0; i < rows.length; i++) {
            table.append(rows[i]);
        }

        // 更新排序指示器
        $('.table th').removeClass('sorted-asc sorted-desc');
        $(this).addClass(this.asc ? 'sorted-asc' : 'sorted-desc');
    });

    function comparer(index) {
        return function(a, b) {
            const valA = getCellValue(a, index);
            const valB = getCellValue(b, index);
            return $.isNumeric(valA) && $.isNumeric(valB) ? valA - valB : valA.toString().localeCompare(valB);
        };
    }

    function getCellValue(row, index) {
        return $(row).children('td').eq(index).text();
    }
});
</script>
{% endblock %}
