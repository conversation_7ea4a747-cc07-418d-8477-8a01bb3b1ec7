{% extends "admin_base.html" %}

{% block title %}活动管理 - 会长后台{% endblock %}

{% block breadcrumb_title %}活动管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">活动管理</h2>
            <p class="text-muted mb-0">管理所有社团的活动信息</p>
        </div>
        <div>
            {% if clubs %}
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-plus me-1"></i>创建活动
                </button>
                <ul class="dropdown-menu">
                    {% for club in clubs %}
                    <li>
                        <a class="dropdown-item" href="{{ url_for('president.create_activity', club_id=club.ClubID) }}">
                            <i class="fas fa-layer-group me-2"></i>{{ club.ClubName }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 活动统计卡片 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ activities_data.planned|length if activities_data.planned else 0 }}</h4>
                    <p class="text-muted mb-0">计划中</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-play fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ activities_data.ongoing|length if activities_data.ongoing else 0 }}</h4>
                    <p class="text-muted mb-0">进行中</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-check fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ activities_data.completed|length if activities_data.completed else 0 }}</h4>
                    <p class="text-muted mb-0">已完成</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-danger mb-2">
                        <i class="fas fa-times fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ activities_data.cancelled|length if activities_data.cancelled else 0 }}</h4>
                    <p class="text-muted mb-0">已取消</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 活动列表 -->
    <div class="row">
        <!-- 计划中的活动 -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-warning"></i>计划中的活动
                    </h5>
                </div>
                <div class="card-body">
                    {% if activities_data.planned %}
                    <div class="list-group list-group-flush">
                        {% for activity in activities_data.planned %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ activity.ActivityName }}</h6>
                                    <p class="mb-1 small text-muted">
                                        <i class="fas fa-layer-group me-1"></i>{{ activity.club.ClubName if activity.club else '未知社团' }}
                                    </p>
                                    <div class="d-flex flex-wrap gap-2 small text-muted">
                                        <span>
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ activity.StartTime.strftime('%m-%d %H:%M') if activity.StartTime else '时间待定' }}
                                        </span>
                                        <span>
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            {{ activity.venue.VenueName if activity.venue else '地点待定' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('president.activity_detail', activity_id=activity.ActivityID) }}" 
                                       class="btn btn-outline-primary btn-sm" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-clock text-muted" style="font-size: 2rem;"></i>
                        <p class="mt-2 text-muted mb-0">暂无计划中的活动</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 进行中的活动 -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-play me-2 text-success"></i>进行中的活动
                    </h5>
                </div>
                <div class="card-body">
                    {% if activities_data.ongoing %}
                    <div class="list-group list-group-flush">
                        {% for activity in activities_data.ongoing %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ activity.ActivityName }}</h6>
                                    <p class="mb-1 small text-muted">
                                        <i class="fas fa-layer-group me-1"></i>{{ activity.club.ClubName if activity.club else '未知社团' }}
                                    </p>
                                    <div class="d-flex flex-wrap gap-2 small text-muted">
                                        <span>
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ activity.StartTime.strftime('%m-%d %H:%M') if activity.StartTime else '时间待定' }}
                                        </span>
                                        <span>
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            {{ activity.venue.VenueName if activity.venue else '地点待定' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('president.activity_detail', activity_id=activity.ActivityID) }}" 
                                       class="btn btn-outline-success btn-sm" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-play text-muted" style="font-size: 2rem;"></i>
                        <p class="mt-2 text-muted mb-0">暂无进行中的活动</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 最近完成的活动 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-check me-2 text-info"></i>最近完成的活动
                    </h5>
                </div>
                <div class="card-body">
                    {% if activities_data.completed %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>活动名称</th>
                                    <th>社团</th>
                                    <th>活动时间</th>
                                    <th>地点</th>
                                    <th>参与人数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in activities_data.completed %}
                                <tr>
                                    <td>
                                        <div class="fw-semibold">{{ activity.ActivityName }}</div>
                                        <small class="text-muted">{{ activity.ActivityType }}</small>
                                    </td>
                                    <td>{{ activity.club.ClubName if activity.club else '未知社团' }}</td>
                                    <td>
                                        <div>{{ activity.StartTime.strftime('%Y-%m-%d') if activity.StartTime else '未知' }}</div>
                                        <small class="text-muted">{{ activity.StartTime.strftime('%H:%M') if activity.StartTime else '' }}</small>
                                    </td>
                                    <td>{{ activity.venue.VenueName if activity.venue else '未知地点' }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ activity.ActualParticipant }}人</span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('president.activity_detail', activity_id=activity.ActivityID) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>查看
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check text-muted" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 text-muted">暂无已完成的活动</h6>
                        <p class="text-muted">开始创建和举办活动吧！</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    {% if not clubs %}
    <!-- 空状态 -->
    <div class="text-center py-5">
        <i class="fas fa-calendar-alt text-muted" style="font-size: 4rem;"></i>
        <h4 class="mt-3 text-muted">暂无管理的社团</h4>
        <p class="text-muted">您当前没有管理任何社团，无法进行活动管理。</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.list-group-item {
    transition: background-color 0.2s ease-in-out;
}

.list-group-item:hover {
    background-color: rgba(0,123,255,0.05);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // 卡片悬停效果
    $('.card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
});
</script>
{% endblock %}
