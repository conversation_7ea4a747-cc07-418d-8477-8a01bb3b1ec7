{% extends "admin_base.html" %}

{% block title %}活动管理 - 会长后台{% endblock %}

{% block breadcrumb_title %}活动管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">活动管理</h2>
            <p class="text-muted mb-0">管理所有社团的活动信息</p>
        </div>
        <div>
            {% if clubs %}
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-plus me-1"></i>创建活动
                </button>
                <ul class="dropdown-menu">
                    {% for club in clubs %}
                    <li>
                        <a class="dropdown-item" href="{{ url_for('president.create_activity', club_id=club.ClubID) }}">
                            <i class="fas fa-layer-group me-2"></i>{{ club.ClubName }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <form method="GET" action="{{ url_for('president.activities') }}" class="d-flex">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search"
                           value="{{ search_term }}" placeholder="搜索活动名称、描述...">
                    <input type="hidden" name="status" value="{{ current_status }}">
                    <button type="submit" class="btn btn-outline-primary">搜索</button>
                </div>
            </form>
        </div>
        <div class="col-md-4">
            <form method="GET" action="{{ url_for('president.activities') }}">
                <select class="form-select" name="status" onchange="this.form.submit()">
                    <option value="all" {% if current_status == 'all' %}selected{% endif %}>全部状态</option>
                    <option value="计划中" {% if current_status == '计划中' %}selected{% endif %}>计划中</option>
                    <option value="进行中" {% if current_status == '进行中' %}selected{% endif %}>进行中</option>
                    <option value="已完成" {% if current_status == '已完成' %}selected{% endif %}>已完成</option>
                    <option value="已取消" {% if current_status == '已取消' %}selected{% endif %}>已取消</option>
                </select>
                <input type="hidden" name="search" value="{{ search_term }}">
            </form>
        </div>
    </div>

    <!-- 活动数据列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2 text-primary"></i>活动列表
                        </h5>
                        <div class="d-flex gap-2">
                            <span class="badge bg-warning">计划中: {{ activities_data.planned if activities_data.planned else 0 }}</span>
                            <span class="badge bg-success">进行中: {{ activities_data.ongoing if activities_data.ongoing else 0 }}</span>
                            <span class="badge bg-info">已完成: {{ activities_data.completed if activities_data.completed else 0 }}</span>
                            <span class="badge bg-danger">已取消: {{ activities_data.cancelled if activities_data.cancelled else 0 }}</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if activities_data.current_activities %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0" id="activitiesTable">
                            <thead class="table-light">
                                <tr>
                                    <th>活动名称</th>
                                    <th>活动类型</th>
                                    <th>所属社团</th>
                                    <th>活动时间</th>
                                    <th>活动地点</th>
                                    <th>参与人数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in activities_data.current_activities %}
                                <tr data-status="{{ activity.Status }}" data-search="{{ activity.ActivityName|lower }} {{ activity.club.ClubName|lower if activity.club else '' }} {{ activity.venue.VenueName|lower if activity.venue else '' }}">
                                    <td>
                                        <div class="fw-semibold">{{ activity.ActivityName }}</div>
                                        {% if activity.Description %}
                                        <small class="text-muted">{{ activity.Description[:50] }}{% if activity.Description|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ activity.ActivityType }}</span>
                                    </td>
                                    <td>
                                        <div>{{ activity.club.ClubName if activity.club else '未知社团' }}</div>
                                        {% if activity.organizer %}
                                        <small class="text-muted">组织者: {{ activity.organizer.Name or activity.organizer.Username }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>{{ activity.StartTime.strftime('%Y-%m-%d') if activity.StartTime else '未知' }}</div>
                                        <small class="text-muted">
                                            {{ activity.StartTime.strftime('%H:%M') if activity.StartTime else '' }}
                                            {% if activity.EndTime %} - {{ activity.EndTime.strftime('%H:%M') }}{% endif %}
                                        </small>
                                    </td>
                                    <td>
                                        <div>{{ activity.venue.VenueName if activity.venue else '未知地点' }}</div>
                                        {% if activity.venue and activity.venue.Location %}
                                        <small class="text-muted">{{ activity.venue.Location }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <div class="fw-bold">{{ activity.ActualParticipant }}</div>
                                            <small class="text-muted">
                                                {% if activity.ParticipantLimit %}
                                                / {{ activity.ParticipantLimit }}
                                                {% else %}
                                                / 无限制
                                                {% endif %}
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if activity.Status == '计划中' %}
                                        <span class="badge bg-warning">{{ activity.Status }}</span>
                                        {% elif activity.Status == '进行中' %}
                                        <span class="badge bg-success">{{ activity.Status }}</span>
                                        {% elif activity.Status == '已完成' %}
                                        <span class="badge bg-info">{{ activity.Status }}</span>
                                        {% elif activity.Status == '已取消' %}
                                        <span class="badge bg-danger">{{ activity.Status }}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ activity.Status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('president.activity_detail', activity_id=activity.ActivityID) }}"
                                               class="btn btn-outline-primary btn-sm" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if activity.Status == '计划中' %}
                                            <a href="{{ url_for('president.edit_activity', activity_id=activity.ActivityID) }}"
                                               class="btn btn-outline-secondary btn-sm" title="编辑活动">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger btn-sm"
                                                    onclick="confirmDelete('{{ activity.ActivityID }}', '{{ activity.ActivityName }}')"
                                                    title="删除活动">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                            {% if activity.Status in ['计划中', '进行中'] %}
                                            <button type="button" class="btn btn-outline-warning btn-sm"
                                                    onclick="requestCancel('{{ activity.ActivityID }}', '{{ activity.ActivityName }}')"
                                                    title="申请取消">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                            {% endif %}
                                            {% if activity.Status == '进行中' %}
                                            <button type="button" class="btn btn-outline-success btn-sm"
                                                    onclick="completeActivity('{{ activity.ActivityID }}', '{{ activity.ActivityName }}')"
                                                    title="完成活动">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页导航 -->
                    {% if pagination and pagination.pages > 1 %}
                    <nav aria-label="活动列表分页">
                        <ul class="pagination justify-content-center mt-4">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('president.activities', page=pagination.prev_num, status=current_status, search=search_term) }}">
                                    <i class="fas fa-chevron-left"></i> 上一页
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('president.activities', page=page_num, status=current_status, search=search_term) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('president.activities', page=pagination.next_num, status=current_status, search=search_term) }}">
                                    下一页 <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>

                        <div class="text-center text-muted small">
                            显示第 {{ pagination.per_page * (pagination.page - 1) + 1 }} -
                            {{ pagination.per_page * (pagination.page - 1) + pagination.items|length }} 条，
                            共 {{ pagination.total }} 条记录
                        </div>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-alt text-muted" style="font-size: 4rem;"></i>
                        <h4 class="mt-3 text-muted">暂无活动</h4>
                        <p class="text-muted">
                            {% if search_term %}
                            没有找到包含 "{{ search_term }}" 的活动
                            {% else %}
                            开始创建您的第一个活动吧！
                            {% endif %}
                        </p>
                        {% if clubs and not search_term %}
                        <div class="dropdown">
                            <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-plus me-1"></i>创建活动
                            </button>
                            <ul class="dropdown-menu">
                                {% for club in clubs %}
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('president.create_activity', club_id=club.ClubID) }}">
                                        <i class="fas fa-layer-group me-2"></i>{{ club.ClubName }}
                                    </a>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">确认删除活动</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除活动 "<span id="activityNameToDelete"></span>" 吗？</p>
                    <p class="text-danger small">此操作不可撤销！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-danger">确认删除</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 申请取消活动模态框 -->
    <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="cancelModalLabel">申请取消活动</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="cancelForm" method="POST">
                    <div class="modal-body">
                        <p>您要申请取消活动 "<span id="activityNameToCancel"></span>" 吗？</p>
                        <div class="mb-3">
                            <label for="cancelReason" class="form-label">取消原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="cancelReason" name="reason" rows="3"
                                      placeholder="请详细说明取消活动的原因..." required></textarea>
                        </div>
                        <p class="text-warning small">申请提交后需要等待管理员审批。</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-warning">提交申请</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 完成活动模态框 -->
    <div class="modal fade" id="completeModal" tabindex="-1" aria-labelledby="completeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="completeModalLabel">完成活动</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="completeForm" method="POST">
                    <div class="modal-body">
                        <p>您要标记活动 "<span id="activityNameToComplete"></span>" 为已完成吗？</p>
                        <div class="mb-3">
                            <label for="actualParticipant" class="form-label">实际参与人数</label>
                            <input type="number" class="form-control" id="actualParticipant" name="actual_participant"
                                   min="0" placeholder="请输入实际参与人数">
                            <div class="form-text">可选，用于统计活动效果</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-success">确认完成</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {% if not clubs %}
    <!-- 空状态 -->
    <div class="text-center py-5">
        <i class="fas fa-calendar-alt text-muted" style="font-size: 4rem;"></i>
        <h4 class="mt-3 text-muted">暂无管理的社团</h4>
        <p class="text-muted">您当前没有管理任何社团，无法进行活动管理。</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    border-radius: 12px;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

.badge {
    font-size: 0.75rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();
});

// 删除确认函数
function confirmDelete(activityId, activityName) {
    $('#activityNameToDelete').text(activityName);
    $('#deleteForm').attr('action', '{{ url_for("president.delete_activity", activity_id="ACTIVITY_ID") }}'.replace('ACTIVITY_ID', activityId));
    $('#deleteModal').modal('show');
}

// 申请取消活动函数
function requestCancel(activityId, activityName) {
    $('#activityNameToCancel').text(activityName);
    $('#cancelForm').attr('action', '{{ url_for("president.request_cancel_activity", activity_id="ACTIVITY_ID") }}'.replace('ACTIVITY_ID', activityId));
    $('#cancelReason').val(''); // 清空原因输入框
    $('#cancelModal').modal('show');
}

// 完成活动函数
function completeActivity(activityId, activityName) {
    $('#activityNameToComplete').text(activityName);
    $('#completeForm').attr('action', '{{ url_for("president.complete_activity", activity_id="ACTIVITY_ID") }}'.replace('ACTIVITY_ID', activityId));
    $('#actualParticipant').val(''); // 清空参与人数输入框
    $('#completeModal').modal('show');
}
</script>
{% endblock %}
