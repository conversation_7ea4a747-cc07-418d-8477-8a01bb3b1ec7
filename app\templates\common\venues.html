{% extends "base.html" %}

{% block title %}场馆列表 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="fas fa-building me-2 text-primary"></i>场馆列表
            </h2>
            <p class="text-muted">查看校园内可用的活动场馆</p>
        </div>
    </div>

    <!-- 场馆列表 -->
    <div class="row">
        {% for venue in venues %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-header bg-{{ loop.cycle('primary', 'success', 'warning', 'info', 'secondary') }} text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>{{ venue.VenueName }}
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        <i class="fas fa-tag me-2"></i><strong>类型：</strong>{{ venue.VenueType }}
                    </p>
                    <p class="card-text">
                        <i class="fas fa-map-marker-alt me-2"></i><strong>位置：</strong>{{ venue.Location }}
                    </p>
                    {% if venue.Address %}
                    <p class="card-text">
                        <i class="fas fa-location-arrow me-2"></i><strong>地址：</strong>{{ venue.Address }}
                    </p>
                    {% endif %}
                    {% if venue.Capacity %}
                    <p class="card-text">
                        <i class="fas fa-users me-2"></i><strong>容量：</strong>{{ venue.Capacity }}人
                    </p>
                    {% endif %}
                    <p class="card-text">
                        <i class="fas fa-clock me-2"></i><strong>开放时间：</strong>{{ venue.AvailabTime }}
                    </p>
                    {% if venue.ContactPhone %}
                    <p class="card-text">
                        <i class="fas fa-phone me-2"></i><strong>联系电话：</strong>{{ venue.ContactPhone }}
                    </p>
                    {% endif %}
                </div>

            </div>
        </div>
        {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-building text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">暂无可用场馆</h4>
                <p class="text-muted">请稍后再来查看</p>
            </div>
        </div>
        {% endfor %}
    </div>


</div>
{% endblock %}
