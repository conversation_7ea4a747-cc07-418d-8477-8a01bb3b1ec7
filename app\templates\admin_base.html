<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}管理后台 - 学校社团管理系统{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS (本地) -->
    <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- FontAwesome (本地) -->
    <link href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/admin.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
    
    <style>
    /* 现代化管理后台样式 */
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: #f8f9fa;
    }
    
    .admin-sidebar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        box-shadow: 2px 0 15px rgba(0,0,0,0.1);
        position: fixed;
        top: 0;
        left: 0;
        width: 280px;
        z-index: 1000;
        transition: all 0.3s ease;
        overflow-y: auto;
    }
    
    .admin-sidebar.collapsed {
        width: 70px;
    }
    
    .sidebar-brand {
        padding: 1.5rem 1rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        color: white;
        text-decoration: none;
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }
    
    .sidebar-brand:hover {
        color: white;
        text-decoration: none;
        background: rgba(255,255,255,0.05);
    }
    
    .sidebar-brand .brand-icon {
        width: 40px;
        height: 40px;
        background: rgba(255,255,255,0.2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 1.2rem;
        transition: all 0.3s ease;
    }
    
    .sidebar-brand:hover .brand-icon {
        background: rgba(255,255,255,0.3);
        transform: scale(1.05);
    }
    
    .sidebar-nav {
        padding: 1rem 0;
    }
    
    .nav-item {
        margin: 0.2rem 1rem;
    }
    
    .nav-link {
        color: rgba(255,255,255,0.8) !important;
        padding: 0.75rem 1rem;
        border-radius: 12px;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        text-decoration: none;
        position: relative;
        font-weight: 500;
    }
    
    .nav-link:hover {
        background: rgba(255,255,255,0.15);
        color: white !important;
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .nav-link.active {
        background: rgba(255,255,255,0.2);
        color: white !important;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        transform: translateX(5px);
    }
    
    .nav-link.active::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 25px;
        background: white;
        border-radius: 0 2px 2px 0;
    }
    
    .nav-icon {
        width: 20px;
        margin-right: 12px;
        text-align: center;
        font-size: 1rem;
    }
    
    .admin-topbar {
        background: white;
        box-shadow: 0 2px 15px rgba(0,0,0,0.08);
        padding: 1rem 2rem;
        position: fixed;
        top: 0;
        right: 0;
        left: 280px;
        z-index: 999;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
        border-bottom: 1px solid #e9ecef;
    }
    

    
    .topbar-left {
        display: flex;
        align-items: center;
    }
    

    
    .breadcrumb-nav {
        background: none;
        padding: 0;
        margin: 0;
        font-size: 0.9rem;
    }
    
    .breadcrumb-nav .breadcrumb-item {
        color: #6c757d;
    }
    
    .breadcrumb-nav .breadcrumb-item.active {
        color: #495057;
        font-weight: 600;
    }
    
    .topbar-right {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .notification-bell {
        position: relative;
        background: none;
        border: none;
        color: #6c757d;
        font-size: 1.1rem;
        padding: 0.6rem;
        border-radius: 50%;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .notification-bell:hover {
        background: #f8f9fa;
        color: #495057;
        transform: scale(1.1);
    }
    
    .notification-badge {
        position: absolute;
        top: 2px;
        right: 2px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .user-dropdown {
        display: flex;
        align-items: center;
        background: none;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
        cursor: pointer;
    }
    
    .user-dropdown:hover {
        background: #f8f9fa;
        color: inherit;
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .user-avatar {
        width: 38px;
        height: 38px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        margin-right: 0.75rem;
        font-size: 0.9rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .user-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
    
    .user-name {
        font-weight: 600;
        font-size: 0.9rem;
        color: #2c3e50;
        margin: 0;
    }
    
    .user-role {
        font-size: 0.75rem;
        color: #6c757d;
        margin: 0;
    }
    
    .admin-content {
        margin-left: 280px;
        margin-top: 85px;
        padding: 2rem;
        transition: all 0.3s ease;
        min-height: calc(100vh - 85px);
    }
    

    

    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .admin-sidebar {
            transform: translateX(-100%);
        }
        
        .admin-sidebar.show {
            transform: translateX(0);
        }
        
        .admin-topbar {
            margin-left: 0;
            left: 0;
        }
        
        .admin-content {
            margin-left: 0;
        }
        
        .user-info {
            display: none;
        }
        
        .page-title {
            font-size: 1.2rem;
        }
        
        .page-title .title-icon {
            width: 40px;
            height: 40px;
            font-size: 1.1rem;
        }
    }
    
    /* 滚动条样式 */
    .admin-sidebar::-webkit-scrollbar {
        width: 6px;
    }
    
    .admin-sidebar::-webkit-scrollbar-track {
        background: rgba(255,255,255,0.1);
    }
    
    .admin-sidebar::-webkit-scrollbar-thumb {
        background: rgba(255,255,255,0.3);
        border-radius: 3px;
    }
    
    .admin-sidebar::-webkit-scrollbar-thumb:hover {
        background: rgba(255,255,255,0.5);
    }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="admin-sidebar" id="adminSidebar">
        <a href="{{ url_for('admin.dashboard') }}" class="sidebar-brand">
            <div class="brand-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <span class="brand-text">社团管理系统</span>
        </a>
        
        <div class="sidebar-nav">
            <ul class="nav flex-column">
                {% if session.role == '管理员' %}
                    <!-- 管理员菜单 -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.dashboard' %}active{% endif %}" 
                           href="{{ url_for('admin.dashboard') }}">
                            <i class="nav-icon fas fa-tachometer-alt"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'admin.members' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('admin.members') }}">
                            <i class="nav-icon fas fa-users"></i>用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'admin.clubs' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('admin.clubs') }}">
                            <i class="nav-icon fas fa-layer-group"></i>社团管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'admin.venues' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('admin.venues') }}">
                            <i class="nav-icon fas fa-building"></i>场馆管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'admin.activities' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('admin.activities') }}">
                            <i class="nav-icon fas fa-calendar-alt"></i>活动管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'admin.approvals' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('admin.approvals') }}">
                            <i class="nav-icon fas fa-check-circle"></i>审批管理
                        </a>
                    </li>


                {% else %}
                    <!-- 会长菜单 - 4个核心功能 -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'president.dashboard' %}active{% endif %}"
                           href="{{ url_for('president.dashboard') }}">
                            <i class="nav-icon fas fa-tachometer-alt"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'president.members' in request.endpoint %}active{% endif %}"
                           href="{{ url_for('president.members') }}">
                            <i class="nav-icon fas fa-users"></i>成员管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'president.activities' in request.endpoint %}active{% endif %}"
                           href="{{ url_for('president.activities') }}">
                            <i class="nav-icon fas fa-calendar-alt"></i>活动管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'president.registrations' in request.endpoint %}active{% endif %}"
                           href="{{ url_for('president.registrations') }}">
                            <i class="nav-icon fas fa-clipboard-check"></i>报名审批
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <!-- 顶部导航栏 -->
    <div class="admin-topbar" id="adminTopbar">
        <div class="topbar-left">
            <!-- 面包屑导航 -->
            {% block breadcrumb %}
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-nav">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('admin.dashboard') }}">
                            <i class="fas fa-home"></i>
                        </a>
                    </li>
                    <li class="breadcrumb-item active">{% block breadcrumb_title %}仪表板{% endblock %}</li>
                </ol>
            </nav>
            {% endblock %}
        </div>
        
        <div class="topbar-right">
            <!-- 用户下拉菜单 -->
            <div class="dropdown">
                <button class="user-dropdown" data-bs-toggle="dropdown">
                    <div class="user-avatar">
                        {{ session.username[0].upper() if session.username else 'U' }}
                    </div>
                    <div class="user-info">
                        <div class="user-name">{{ session.username }}</div>
                        <div class="user-role">{{ session.role }}</div>
                    </div>
                    <i class="fas fa-chevron-down ms-2"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                        <i class="fas fa-user me-2"></i>个人资料
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                        <i class="fas fa-key me-2"></i>修改密码
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('common.index') }}" target="_blank">
                        <i class="fas fa-home me-2"></i>前台首页
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                        <i class="fas fa-sign-out-alt me-2"></i>退出登录
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="admin-content" id="adminContent">


        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </main>

    <!-- jQuery (本地) -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <!-- Bootstrap 5 JS (本地) -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <!-- Chart.js (本地) -->
    <script src="{{ url_for('static', filename='vendor/chart.js/chart.min.js') }}"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/admin.js') }}"></script>
    
    <script>
    // 现代化管理后台交互
    $(document).ready(function() {
        // 平滑滚动
        $('a[href^="#"]').on('click', function(event) {
            var target = $(this.getAttribute('href'));
            if (target.length) {
                event.preventDefault();
                $('html, body').stop().animate({
                    scrollTop: target.offset().top - 100
                }, 1000);
            }
        });

        // 工具提示
        $('[data-bs-toggle="tooltip"]').tooltip();

        // 自动隐藏提示消息
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
