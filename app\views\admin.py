#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员视图模块
处理系统管理员的页面路由和请求
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from app import db
from app.controllers.admin_controller import AdminController
from app.controllers.approval_controller import ApprovalController
from app.utils.decorators import login_required, admin_required
from app.utils.helpers import get_current_user
from app.models.venue import Venue
from app.models.approval import ApprovalRequest
from app.models.club import Club
from app.models.member import Member

# 创建管理员蓝图
admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """
    管理员仪表板
    显示系统概览、待处理事项等
    """
    # 获取仪表板数据
    dashboard_data = AdminController.get_admin_dashboard_data()
    
    return render_template('admin/dashboard.html', **dashboard_data)

@admin_bp.route('/venues')
@login_required
@admin_required
def venues():
    """
    场馆管理页面
    """
    page = request.args.get('page', 1, type=int)
    per_page = 10
    search = request.args.get('search', '').strip()
    venue_type = request.args.get('venue_type', '').strip()
    capacity_range = request.args.get('capacity_range', '').strip()

    # 获取场馆列表
    result = AdminController.get_all_venues(
        page=page,
        per_page=per_page,
        search=search if search else None,
        venue_type=venue_type if venue_type else None,
        capacity_range=capacity_range if capacity_range else None
    )

    return render_template('admin/venues.html',
                         venues=result['venues'],
                         pagination=result['pagination'])

@admin_bp.route('/venues/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_venue():
    """
    创建场馆页面
    """
    if request.method == 'POST':
        # 收集表单数据
        venue_data = {
            'venue_name': request.form.get('venue_name', '').strip(),
            'location': request.form.get('location', '').strip(),
            'address': request.form.get('address', '').strip(),
            'contact_phone': request.form.get('contact_phone', '').strip(),
            'capacity': request.form.get('capacity', '').strip(),
            'venue_type': request.form.get('venue_type', ''),
            'availab_time': request.form.get('availab_time', '').strip()
        }
        
        # 调用控制器创建场馆
        success, message = AdminController.create_venue(venue_data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('admin.venues'))
        else:
            flash(message, 'error')
    
    return render_template('admin/create_venue.html')

@admin_bp.route('/venues/<venue_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_venue(venue_id):
    """
    编辑场馆页面
    """
    venue = Venue.query.get_or_404(venue_id)
    
    if request.method == 'POST':
        # 收集表单数据
        venue_data = {
            'venue_name': request.form.get('venue_name', '').strip(),
            'location': request.form.get('location', '').strip(),
            'address': request.form.get('address', '').strip(),
            'contact_phone': request.form.get('contact_phone', '').strip(),
            'capacity': request.form.get('capacity', '').strip(),
            'venue_type': request.form.get('venue_type', ''),
            'availab_time': request.form.get('availab_time', '').strip()
        }
        
        # 调用控制器更新场馆
        success, message = AdminController.update_venue(venue_id, venue_data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('admin.venues'))
        else:
            flash(message, 'error')
    
    return render_template('admin/edit_venue.html', venue=venue)

@admin_bp.route('/venues/<venue_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_venue(venue_id):
    """
    删除场馆
    """
    # 调用控制器删除场馆
    success, message = AdminController.delete_venue(venue_id)
    
    flash(message, 'success' if success else 'error')
    return redirect(url_for('admin.venues'))

@admin_bp.route('/approvals')
@login_required
@admin_required
def approvals():
    """
    审批管理页面
    """
    # 获取查询参数
    request_type = request.args.get('request_type', 'all')
    status = request.args.get('status', '待批')
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 获取审批申请列表
    result = ApprovalController.get_approval_requests(
        request_type=request_type if request_type != 'all' else None,
        status=status if status != 'all' else None,
        page=page,
        per_page=per_page
    )

    # 获取统计数据
    stats = {
        'pending': ApprovalRequest.query.filter_by(Status='待批').count(),
        'approved': ApprovalRequest.query.filter_by(Status='已批').count(),
        'rejected': ApprovalRequest.query.filter_by(Status='已拒').count(),
        'total': ApprovalRequest.query.count()
    }

    return render_template('admin/approvals.html',
                         approvals=result['approvals'],
                         pagination=result['pagination'],
                         current_type=request_type,
                         current_status=status,
                         current_date_range=request.args.get('date_range', 'all'),
                         stats=stats)

@admin_bp.route('/approvals/<request_id>/approve', methods=['POST'])
@login_required
@admin_required
def approve_request(request_id):
    """
    批准审批申请
    """
    comments = request.form.get('comments', '').strip()
    
    # 调用控制器处理批准
    success, message = AdminController.process_approval_request(
        request_id, 'approve', comments
    )
    
    flash(message, 'success' if success else 'error')
    return redirect(url_for('admin.approvals'))

@admin_bp.route('/approvals/<request_id>/reject', methods=['POST'])
@login_required
@admin_required
def reject_request(request_id):
    """
    拒绝审批申请
    """
    comments = request.form.get('comments', '').strip()
    
    # 调用控制器处理拒绝
    success, message = AdminController.process_approval_request(
        request_id, 'reject', comments
    )
    
    flash(message, 'success' if success else 'error')
    return redirect(url_for('admin.approvals'))

@admin_bp.route('/approvals/<request_id>/detail')
@login_required
@admin_required
def approval_detail(request_id):
    """
    获取审批申请详情
    """
    approval = ApprovalRequest.query.get(request_id)
    if not approval:
        return jsonify({'success': False, 'message': '申请不存在'})

    # 构建详情HTML
    detail_html = f"""
    <div class="row">
        <div class="col-md-6">
            <p><strong>申请人：</strong>{approval.applicant.Name if approval.applicant else '未知'}</p>
            <p><strong>申请类型：</strong>{approval.RequestType}</p>
            <p><strong>申请时间：</strong>{approval.RequestTime.strftime('%Y-%m-%d %H:%M:%S') if approval.RequestTime else '未知'}</p>
        </div>
        <div class="col-md-6">
            <p><strong>状态：</strong>{approval.Status}</p>
            <p><strong>审批时间：</strong>{approval.ApprovalTime.strftime('%Y-%m-%d %H:%M:%S') if approval.ApprovalTime else '未审批'}</p>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-12">
            <p><strong>申请内容：</strong></p>
            <p>{approval.Comments or '无详细内容'}</p>
        </div>
    </div>
    """

    return jsonify({
        'success': True,
        'html': detail_html,
        'approval': {
            'Status': approval.Status
        }
    })

@admin_bp.route('/members')
@login_required
@admin_required
def members():
    """
    用户管理页面
    """
    # 获取查询参数
    role = request.args.get('role', 'all')
    search = request.args.get('search', '').strip()
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 获取用户列表
    result = AdminController.get_all_members(
        page=page,
        per_page=per_page,
        role=role if role != 'all' else None,
        search=search if search else None
    )

    return render_template('admin/members.html',
                         members=result['members'],
                         pagination=result['pagination'],
                         current_role=role,
                         search_query=search)

@admin_bp.route('/members/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_member():
    """
    创建用户页面和处理
    """
    if request.method == 'POST':
        # 获取表单数据
        member_data = {
            'username': request.form.get('username', '').strip(),
            'password': '123456',  # 默认密码
            'name': request.form.get('name', '').strip(),
            'role': request.form.get('role', '会员'),
            'phone': request.form.get('phone', '').strip(),
            'college': request.form.get('college', '').strip(),
            'specialty': request.form.get('specialty', '').strip(),
            'gender': request.form.get('gender', '男'),
            'dormitory': request.form.get('dormitory', '').strip()
        }

        # 处理年龄字段（可能为空）
        age_str = request.form.get('age', '').strip()
        if age_str:
            try:
                member_data['age'] = int(age_str)
            except ValueError:
                flash('年龄必须是有效的数字', 'error')
                return render_template('admin/create_member.html')
        else:
            member_data['age'] = None

        # 基本验证
        if not member_data['username']:
            flash('用户名不能为空', 'error')
            return render_template('admin/create_member.html')

        if not member_data['name']:
            flash('姓名不能为空', 'error')
            return render_template('admin/create_member.html')

        # 验证角色
        if member_data['role'] not in ['管理员', '会长', '会员']:
            flash('无效的角色', 'error')
            return render_template('admin/create_member.html')

        # 验证性别
        if member_data['gender'] not in ['男', '女', '其他']:
            flash('无效的性别', 'error')
            return render_template('admin/create_member.html')

        # 验证年龄范围
        if member_data['age'] is not None and (member_data['age'] <= 0 or member_data['age'] >= 150):
            flash('年龄必须在1-149之间', 'error')
            return render_template('admin/create_member.html')

        # 验证电话号码格式
        if member_data['phone']:
            import re
            if not re.match(r'^[0-9]{7,15}$', member_data['phone']):
                flash('电话号码格式不正确（应为7-15位数字）', 'error')
                return render_template('admin/create_member.html')

        # 调用控制器创建用户
        success, message, member_id = AdminController.create_member(member_data)

        if success:
            flash(message, 'success')
            return redirect(url_for('admin.members'))
        else:
            flash(message, 'error')

    return render_template('admin/create_member.html')

@admin_bp.route('/members/<member_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_member(member_id):
    """
    编辑用户页面和处理
    """
    member = AdminController.get_member_by_id(member_id)
    if not member:
        flash('用户不存在', 'error')
        return redirect(url_for('admin.members'))

    if request.method == 'POST':
        # 获取表单数据
        member_data = {
            'username': request.form.get('username', '').strip(),
            'name': request.form.get('name', '').strip(),
            'role': request.form.get('role', '会员'),
            'phone': request.form.get('phone', '').strip(),
            'college': request.form.get('college', '').strip(),
            'specialty': request.form.get('specialty', '').strip(),
            'gender': request.form.get('gender', '').strip(),
            'dormitory': request.form.get('dormitory', '').strip()
        }

        # 处理年龄字段（可能为空）
        age_str = request.form.get('age', '').strip()
        if age_str:
            try:
                member_data['age'] = int(age_str)
            except ValueError:
                flash('年龄必须是有效的数字', 'error')
                return render_template('admin/edit_member.html', member=member)
        else:
            member_data['age'] = None

        # 如果提供了新密码
        new_password = request.form.get('password', '').strip()
        if new_password:
            member_data['password'] = new_password

        # 基本验证
        if not member_data['username']:
            flash('用户名不能为空', 'error')
            return render_template('admin/edit_member.html', member=member)

        if not member_data['name']:
            flash('姓名不能为空', 'error')
            return render_template('admin/edit_member.html', member=member)

        # 验证角色
        if member_data['role'] not in ['管理员', '会长', '会员']:
            flash('无效的角色', 'error')
            return render_template('admin/edit_member.html', member=member)

        # 验证性别
        if member_data['gender'] and member_data['gender'] not in ['男', '女', '其他']:
            flash('无效的性别', 'error')
            return render_template('admin/edit_member.html', member=member)

        # 验证年龄范围
        if member_data['age'] is not None and (member_data['age'] <= 0 or member_data['age'] >= 150):
            flash('年龄必须在1-149之间', 'error')
            return render_template('admin/edit_member.html', member=member)

        # 验证电话号码格式
        if member_data['phone']:
            import re
            if not re.match(r'^[0-9]{7,15}$', member_data['phone']):
                flash('电话号码格式不正确（应为7-15位数字）', 'error')
                return render_template('admin/edit_member.html', member=member)

        # 调用控制器更新用户
        success, message = AdminController.update_member(member_id, member_data)

        if success:
            flash(message, 'success')
            return redirect(url_for('admin.members'))
        else:
            flash(message, 'error')

    # 获取用户统计信息
    member_stats = {
        'joined_clubs': 0,
        'organized_activities': 0,
        'pending_requests': 0
    }

    try:
        # 获取用户加入的社团数量
        from app.models.member_club import MemberClub
        member_stats['joined_clubs'] = MemberClub.query.filter_by(
            MemberID=member.MemberID,
            Status='已批准'
        ).count()

        # 获取用户组织的活动数量
        member_stats['organized_activities'] = member.activities_as_organizer.count()

        # 获取用户的待审批申请数量
        member_stats['pending_requests'] = member.approval_requests.filter_by(Status='待批').count()
    except Exception as e:
        # 如果获取统计信息失败，使用默认值
        pass

    return render_template('admin/edit_member.html', member=member, member_stats=member_stats)

@admin_bp.route('/members/<member_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_member(member_id):
    """
    删除用户
    """
    success, message = AdminController.delete_member(member_id)

    if request.is_json:
        return jsonify({'success': success, 'message': message})
    else:
        flash(message, 'success' if success else 'error')
        return redirect(url_for('admin.members'))

@admin_bp.route('/members/batch-change-role', methods=['POST'])
@login_required
@admin_required
def batch_change_role():
    """
    批量修改用户角色
    """
    try:
        data = request.get_json()
        member_ids = data.get('member_ids', [])
        new_role = data.get('new_role', '')

        if not member_ids:
            return jsonify({'success': False, 'message': '请选择要修改的用户'})

        if new_role not in ['管理员', '会长', '会员']:
            return jsonify({'success': False, 'message': '无效的角色'})

        # 批量更新角色
        from app.models.member import Member
        updated_count = 0

        for member_id in member_ids:
            member = Member.query.get(member_id)
            if member and member.Role != '管理员':  # 不能修改管理员角色
                member.Role = new_role
                updated_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功修改了{updated_count}个用户的角色为{new_role}'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'批量修改失败：{str(e)}'})

@admin_bp.route('/members/<member_id>/reset-password', methods=['POST'])
@login_required
@admin_required
def reset_member_password(member_id):
    """
    重置用户密码
    """
    try:
        member = AdminController.get_member_by_id(member_id)
        if not member:
            return jsonify({'success': False, 'message': '用户不存在'})

        # 重置密码为123456
        member.Password = '123456'
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'用户"{member.Name or member.Username}"的密码已重置为123456'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'重置密码失败：{str(e)}'})

@admin_bp.route('/clubs')
@login_required
@admin_required
def clubs():
    """
    社团管理页面
    """
    # 获取查询参数
    search = request.args.get('search', '').strip()
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    # 获取社团列表
    result = AdminController.get_all_clubs(
        page=page,
        per_page=per_page,
        search=search if search else None
    )

    return render_template('admin/clubs.html',
                         clubs=result['clubs'],
                         pagination=result['pagination'],
                         search_query=search)

@admin_bp.route('/clubs/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_club():
    """
    创建社团页面和处理
    """
    if request.method == 'POST':
        # 获取表单数据
        club_data = {
            'club_name': request.form.get('club_name', '').strip(),
            'description': request.form.get('description', '学术'),
            'president_id': request.form.get('president_id', '').strip(),
            'status': request.form.get('status', '活跃'),
            'max_members': request.form.get('max_members', type=int),
            'website': request.form.get('website', '').strip(),
            'foundation_date': request.form.get('foundation_date', '').strip()
        }

        # 基本验证
        if not club_data['club_name']:
            flash('社团名称不能为空', 'error')
            available_presidents = AdminController.get_available_presidents()
            return render_template('admin/create_club.html',
                                 available_presidents=available_presidents)

        if not club_data['president_id']:
            flash('必须指定社团会长', 'error')
            available_presidents = AdminController.get_available_presidents()
            return render_template('admin/create_club.html',
                                 available_presidents=available_presidents)

        # 调用控制器创建社团
        success, message, club_id = AdminController.create_club(club_data)

        if success:
            flash(message, 'success')
            return redirect(url_for('admin.clubs'))
        else:
            flash(message, 'error')

    # 获取可用的会长列表
    available_presidents = AdminController.get_available_presidents()
    return render_template('admin/create_club.html',
                         available_presidents=available_presidents)

@admin_bp.route('/clubs/<club_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_club(club_id):
    """
    编辑社团页面和处理
    """
    club = AdminController.get_club_by_id(club_id)
    if not club:
        flash('社团不存在', 'error')
        return redirect(url_for('admin.clubs'))

    if request.method == 'POST':
        # 获取表单数据
        club_data = {
            'club_name': request.form.get('club_name', '').strip(),
            'description': request.form.get('description', '学术'),
            'president_id': request.form.get('president_id', '').strip(),
            'status': request.form.get('status', '活跃'),
            'max_members': request.form.get('max_members', type=int),
            'website': request.form.get('website', '').strip(),
            'foundation_date': request.form.get('foundation_date', '').strip()
        }

        # 基本验证
        if not club_data['club_name']:
            flash('社团名称不能为空', 'error')
            available_presidents = AdminController.get_available_presidents()
            return render_template('admin/edit_club.html',
                                 club=club, available_presidents=available_presidents)

        if not club_data['president_id']:
            flash('必须指定社团会长', 'error')
            available_presidents = AdminController.get_available_presidents()
            return render_template('admin/edit_club.html',
                                 club=club, available_presidents=available_presidents)

        # 调用控制器更新社团
        success, message = AdminController.update_club(club_id, club_data)

        if success:
            flash(message, 'success')
            return redirect(url_for('admin.clubs'))
        else:
            flash(message, 'error')

    # 获取可用的会长列表（包括当前会长）
    available_presidents = AdminController.get_available_presidents()
    # 如果当前会长不在列表中，添加进去
    if club.president and club.president not in available_presidents:
        available_presidents.append(club.president)

    return render_template('admin/edit_club.html',
                         club=club, available_presidents=available_presidents)

@admin_bp.route('/clubs/<club_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_club(club_id):
    """
    删除社团
    """
    success, message = AdminController.delete_club(club_id)

    if request.is_json:
        return jsonify({'success': success, 'message': message})
    else:
        flash(message, 'success' if success else 'error')
        return redirect(url_for('admin.clubs'))

@admin_bp.route('/clubs/<club_id>/members')
@login_required
@admin_required
def club_members(club_id):
    """
    社团成员管理页面
    """
    club = AdminController.get_club_by_id(club_id)
    if not club:
        flash('社团不存在', 'error')
        return redirect(url_for('admin.clubs'))

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 获取成员列表
    result = AdminController.get_club_members(
        club_id,
        page=page,
        per_page=per_page
    )

    return render_template('admin/club_members.html',
                         club=club,
                         members=result['members'],
                         pagination=result['pagination'])

@admin_bp.route('/clubs/<club_id>/members/<record_id>/remove', methods=['POST'])
@login_required
@admin_required
def remove_club_member(club_id, record_id):
    """
    移除社团成员
    """
    success, message = AdminController.remove_club_member(club_id, record_id)

    if request.is_json:
        return jsonify({'success': success, 'message': message})
    else:
        flash(message, 'success' if success else 'error')
        return redirect(url_for('admin.club_members', club_id=club_id))

@admin_bp.route('/clubs/<club_id>/members/<record_id>/approve', methods=['POST'])
@login_required
@admin_required
def approve_club_member(club_id, record_id):
    """
    批准社团成员申请
    """
    success, message = AdminController.approve_club_member(club_id, record_id)

    if request.is_json:
        return jsonify({'success': success, 'message': message})
    else:
        flash(message, 'success' if success else 'error')
        return redirect(url_for('admin.club_members', club_id=club_id))

@admin_bp.route('/clubs/<club_id>/members/<record_id>/reject', methods=['POST'])
@login_required
@admin_required
def reject_club_member(club_id, record_id):
    """
    拒绝社团成员申请
    """
    success, message = AdminController.reject_club_member(club_id, record_id)

    if request.is_json:
        return jsonify({'success': success, 'message': message})
    else:
        flash(message, 'success' if success else 'error')
        return redirect(url_for('admin.club_members', club_id=club_id))

@admin_bp.route('/clubs/<club_id>/activities')
@login_required
@admin_required
def club_activities(club_id):
    """
    社团活动管理页面
    """
    club = AdminController.get_club_by_id(club_id)
    if not club:
        flash('社团不存在', 'error')
        return redirect(url_for('admin.clubs'))

    # 获取查询参数
    status = request.args.get('status', 'all')
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 获取活动列表
    result = AdminController.get_club_activities(
        club_id,
        page=page,
        per_page=per_page,
        status=status if status != 'all' else None
    )

    return render_template('admin/club_activities.html',
                         club=club,
                         activities=result['activities'],
                         pagination=result['pagination'],
                         current_status=status)

@admin_bp.route('/statistics')
@login_required
@admin_required
def statistics():
    """
    系统统计页面
    """
    # 获取统计数据
    stats_data = AdminController.get_system_statistics()
    
    return render_template('admin/statistics.html', **stats_data)

@admin_bp.route('/activities')
@login_required
@admin_required
def activities():
    """
    活动管理页面
    """
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = 10
    search = request.args.get('search', '').strip()
    status = request.args.get('status', 'all')
    activity_type = request.args.get('activity_type', 'all')
    club_id = request.args.get('club_id', 'all')
    date_range = request.args.get('date_range', 'all')

    # 获取活动列表
    result = AdminController.get_all_activities(
        page=page,
        per_page=per_page,
        search=search if search else None,
        status=status if status != 'all' else None,
        activity_type=activity_type if activity_type != 'all' else None,
        club_id=club_id if club_id != 'all' else None,
        date_range=date_range if date_range != 'all' else None
    )

    # 获取所有社团用于筛选
    clubs = Club.query.filter_by(Status='活跃').all()

    return render_template('admin/activities.html',
                         activities=result['activities'],
                         pagination=result['pagination'],
                         clubs=clubs,
                         current_status=status,
                         current_type=activity_type,
                         current_club=club_id,
                         current_date_range=date_range)

@admin_bp.route('/activities/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_activity():
    """
    创建活动页面
    """
    if request.method == 'POST':
        # 获取表单数据
        activity_data = {
            'activity_name': request.form.get('activity_name', '').strip(),
            'activity_type': request.form.get('activity_type', '其他'),
            'club_id': request.form.get('club_id', '').strip(),
            'organizer_id': request.form.get('organizer_id', '').strip(),
            'venue_id': request.form.get('venue_id', '').strip(),
            'start_time': request.form.get('start_time', '').strip(),
            'end_time': request.form.get('end_time', '').strip(),
            'description': request.form.get('description', '').strip(),
            'participant_limit': request.form.get('participant_limit', type=int),
            'actual_participant': request.form.get('actual_participant', type=int),
            'status': request.form.get('status', '计划中')
        }

        # 调用控制器创建活动
        success, message, activity_id = AdminController.create_activity(activity_data)

        if success:
            flash(message, 'success')
            return redirect(url_for('admin.activities'))
        else:
            flash(message, 'error')

    # 获取创建活动所需的数据
    clubs = Club.query.filter_by(Status='活跃').all()
    venues = Venue.query.all()
    organizers = Member.query.filter(Member.Role.in_(['管理员', '会长'])).all()

    return render_template('admin/create_activity.html',
                         clubs=clubs,
                         venues=venues,
                         organizers=organizers)

@admin_bp.route('/activities/<activity_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_activity(activity_id):
    """
    编辑活动页面
    """
    activity = AdminController.get_activity_by_id(activity_id)
    if not activity:
        flash('活动不存在', 'error')
        return redirect(url_for('admin.activities'))

    if request.method == 'POST':
        # 获取表单数据
        activity_data = {
            'activity_name': request.form.get('activity_name', '').strip(),
            'activity_type': request.form.get('activity_type', '其他'),
            'club_id': request.form.get('club_id', '').strip(),
            'organizer_id': request.form.get('organizer_id', '').strip(),
            'venue_id': request.form.get('venue_id', '').strip(),
            'start_time': request.form.get('start_time', '').strip(),
            'end_time': request.form.get('end_time', '').strip(),
            'description': request.form.get('description', '').strip(),
            'participant_limit': request.form.get('participant_limit', type=int),
            'status': request.form.get('status', '计划中'),
            'actual_participant': request.form.get('actual_participant', type=int)
        }

        # 调用控制器更新活动
        success, message = AdminController.update_activity(activity_id, activity_data)

        if success:
            flash(message, 'success')
            return redirect(url_for('admin.activities'))
        else:
            flash(message, 'error')

    # 获取编辑活动所需的数据
    clubs = Club.query.filter_by(Status='活跃').all()
    venues = Venue.query.all()
    organizers = Member.query.filter(Member.Role.in_(['管理员', '会长'])).all()

    return render_template('admin/edit_activity.html',
                         activity=activity,
                         clubs=clubs,
                         venues=venues,
                         organizers=organizers)

@admin_bp.route('/activities/<activity_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_activity(activity_id):
    """
    删除活动
    """
    success, message = AdminController.delete_activity(activity_id)

    if request.is_json:
        return jsonify({'success': success, 'message': message})
    else:
        flash(message, 'success' if success else 'error')
        return redirect(url_for('admin.activities'))

@admin_bp.route('/api/club/<club_id>/president', methods=['GET'])
@login_required
@admin_required
def get_club_president(club_id):
    """
    获取社团会长信息的API接口
    """
    try:
        club = Club.query.get(club_id)
        if not club:
            return jsonify({'success': False, 'message': '社团不存在'})

        # 获取会长信息
        president = Member.query.get(club.PresidentID)
        if not president:
            return jsonify({'success': False, 'message': '会长信息不存在'})

        return jsonify({
            'success': True,
            'president': {
                'id': president.MemberID,
                'name': president.Name,
                'role': president.Role
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取会长信息失败：{str(e)}'})

@admin_bp.route('/system_info')
@login_required
@admin_required
def system_info():
    """
    系统信息页面
    """
    import sys
    import platform
    from flask import __version__ as flask_version
    
    system_info = {
        'python_version': sys.version,
        'platform': platform.platform(),
        'flask_version': flask_version,
        'database_url': 'MySQL (ClubManagementSystem)',
        'timezone': 'Asia/Shanghai (UTC+8)'
    }
    
    return render_template('admin/system_info.html', system_info=system_info)

@admin_bp.route('/logs')
@login_required
@admin_required
def logs():
    """
    系统日志页面
    """
    # 这里可以实现日志查看功能
    # 暂时返回空页面
    return render_template('admin/logs.html')

@admin_bp.route('/backup')
@login_required
@admin_required
def backup():
    """
    数据备份页面
    """
    # 这里可以实现数据备份功能
    # 暂时返回空页面
    return render_template('admin/backup.html')

@admin_bp.route('/api/stats')
@login_required
@admin_required
def api_stats():
    """
    统计数据API接口（用于图表）
    """
    stats_data = AdminController.get_system_statistics()
    return jsonify(stats_data)
