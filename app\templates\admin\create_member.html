{% extends "admin_base.html" %}

{% block title %}添加用户 - 管理后台{% endblock %}
{% block page_title %}添加用户{% endblock %}
{% block title_icon %}<i class="fas fa-user-plus"></i>{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-nav">
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.dashboard') }}">
                <i class="fas fa-home"></i>
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.members') }}">用户管理</a>
        </li>
        <li class="breadcrumb-item active">添加用户</li>
    </ol>
</nav>
{% endblock %}

{% block page_actions %}
<div class="d-flex gap-2">
    <a href="{{ url_for('admin.members') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回列表
    </a>
</div>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2 text-primary"></i>用户基本信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.create_member') }}" id="createMemberForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <!-- 账户信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-key me-1"></i>账户信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username"
                                   value="{{ request.form.get('username', '') }}" required
                                   pattern="[a-zA-Z0-9_]{3,20}"
                                   title="用户名只能包含字母、数字和下划线，长度3-20位">
                            <div class="form-text">默认使用手机号作为用户名，可自行修改</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">默认密码</label>
                            <input type="text" class="form-control" value="123456" readonly>
                            <div class="form-text">新用户默认密码为123456，可在编辑页面重置</div>
                        </div>
                        <div class="col-md-6">
                            <label for="role" class="form-label">用户角色 <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">请选择角色</option>
                                <option value="会员" {% if request.form.get('role') == '会员' %}selected{% endif %}>会员</option>
                                <option value="会长" {% if request.form.get('role') == '会长' %}selected{% endif %}>会长</option>
                                <option value="管理员" {% if request.form.get('role') == '管理员' %}selected{% endif %}>管理员</option>
                            </select>
                        </div>
                    </div>

                    <!-- 个人信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-user me-1"></i>个人信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="name" class="form-label">真实姓名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ request.form.get('name', '') }}" required maxlength="50">
                        </div>
                        <div class="col-md-6">
                            <label for="gender" class="form-label">性别</label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="">请选择</option>
                                <option value="男" {% if request.form.get('gender') == '男' %}selected{% endif %}>男</option>
                                <option value="女" {% if request.form.get('gender') == '女' %}selected{% endif %}>女</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="age" class="form-label">年龄</label>
                            <input type="number" class="form-control" id="age" name="age" 
                                   value="{{ request.form.get('age', '') }}" min="16" max="100">
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">手机号码</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ request.form.get('phone', '') }}" pattern="[0-9]{11}"
                                   title="请输入11位手机号码">
                        </div>

                    </div>

                    <!-- 学校信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-graduation-cap me-1"></i>学校信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="college" class="form-label">学院</label>
                            <input type="text" class="form-control" id="college" name="college" 
                                   value="{{ request.form.get('college', '') }}" maxlength="100">
                        </div>
                        <div class="col-md-6">
                            <label for="specialty" class="form-label">专业</label>
                            <input type="text" class="form-control" id="specialty" name="specialty" 
                                   value="{{ request.form.get('specialty', '') }}" maxlength="100">
                        </div>
                        <div class="col-md-6">
                            <label for="dormitory" class="form-label">宿舍</label>
                            <input type="text" class="form-control" id="dormitory" name="dormitory" 
                                   value="{{ request.form.get('dormitory', '') }}" maxlength="50">
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('admin.members') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="reset" class="btn btn-outline-warning">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>创建用户
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {

    
    // 用户名唯一性检查
    let usernameTimeout;
    $('#username').on('input', function() {
        const username = $(this).val();
        if (username.length >= 3) {
            clearTimeout(usernameTimeout);
            usernameTimeout = setTimeout(function() {
                checkUsernameAvailability(username);
            }, 500);
        }
    });
    
    // 手机号格式验证和自动填充用户名
    $('#phone').on('input', function() {
        const phone = $(this).val();
        const phoneRegex = /^1[3-9]\d{9}$/;

        // 自动填充用户名（如果用户名为空）
        if (phone && $('#username').val() === '') {
            $('#username').val(phone);
        }

        if (phone && !phoneRegex.test(phone)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">请输入正确的手机号码</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    
    // 表单提交验证
    $('#createMemberForm').on('submit', function(e) {
        const invalidFields = $(this).find('.is-invalid');
        if (invalidFields.length > 0) {
            e.preventDefault();
            alert('请先修正表单中的错误');
            invalidFields.first().focus();
        }
    });
});



// 检查用户名可用性
function checkUsernameAvailability(username) {
    fetch('/admin/api/check-username', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({username: username})
    })
    .then(response => response.json())
    .then(data => {
        const usernameField = $('#username');
        if (data.available) {
            usernameField.removeClass('is-invalid').addClass('is-valid');
            usernameField.next('.invalid-feedback').remove();
            if (!usernameField.next('.valid-feedback').length) {
                usernameField.after('<div class="valid-feedback">用户名可用</div>');
            }
        } else {
            usernameField.removeClass('is-valid').addClass('is-invalid');
            usernameField.next('.valid-feedback').remove();
            if (!usernameField.next('.invalid-feedback').length) {
                usernameField.after('<div class="invalid-feedback">用户名已存在</div>');
            }
        }
    })
    .catch(error => {
        console.error('检查用户名失败:', error);
    });
}
</script>
{% endblock %}
