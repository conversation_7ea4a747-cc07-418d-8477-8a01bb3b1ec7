#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会员控制器
处理普通会员的业务逻辑，包括个人信息管理、社团浏览、入会申请等
Members、Clubs、MemberClub、ApprovalRequests表的数据结构
"""

from app import db
from app.models.member import Member
from app.models.club import Club
from app.models.member_club import MemberClub
from app.models.approval import ApprovalRequest
from app.models.activity import Activity
from app.utils.helpers import generate_uuid, get_china_time
from datetime import datetime, timedelta

class MemberController:
    """会员控制器类"""
    
    @staticmethod
    def get_member_dashboard_data(member_id):
        """
        获取会员仪表板数据
        
        Args:
            member_id: 会员ID
        
        Returns:
            dict: 仪表板数据
        """
        member = Member.query.get(member_id)
        if not member:
            return None
        
        # 获取会员加入的社团（已批准状态）
        joined_clubs = MemberClub.query.filter_by(
            MemberID=member_id,
            Status='已批准'
        ).all()
        
        # 获取待审批的申请
        pending_applications = MemberClub.query.filter_by(
            MemberID=member_id,
            Status='待审批'
        ).all()
        
        # 获取最近的活动（来自已加入的社团）
        club_ids = [mc.ClubID for mc in joined_clubs]
        recent_activities = []
        if club_ids:
            recent_activities = Activity.query.filter(
                Activity.ClubID.in_(club_ids),
                Activity.Status.in_(['计划中', '进行中'])
            ).order_by(Activity.StartTime.asc()).limit(5).all()
        
        # 获取推荐社团（基于专长匹配）
        recommended_clubs = MemberController._get_recommended_clubs(member)

        return {
            'member': member,
            'joined_clubs': joined_clubs,
            'pending_applications': pending_applications,
            'recent_activities': recent_activities,
            'recommended_clubs': recommended_clubs,
            'stats': {
                'joined_count': len(joined_clubs),
                'pending_count': len(pending_applications),
                'activity_count': len(recent_activities)
            }
        }
    
    @staticmethod
    def _get_recommended_clubs(member):
        """
        获取推荐社团（基于专长匹配）
        
        Args:
            member: 会员对象
        
        Returns:
            list: 推荐社团列表
        """
        # 获取会员已加入或申请的社团ID
        existing_club_ids = [
            mc.ClubID for mc in MemberClub.query.filter_by(MemberID=member.MemberID).all()
        ]
        
        # 基于专长推荐社团
        recommended_clubs = []
        if member.Specialty:
            specialty_keywords = member.Specialty.lower().split()
            
            # 查找活跃社团
            active_clubs = Club.query.filter_by(Status='活跃').all()
            
            for club in active_clubs:
                if club.ClubID in existing_club_ids:
                    continue
                
                # 简单的关键词匹配
                club_name_lower = club.ClubName.lower()
                club_desc_lower = club.Description.lower()
                
                match_score = 0
                for keyword in specialty_keywords:
                    if keyword in club_name_lower or keyword in club_desc_lower:
                        match_score += 1
                
                if match_score > 0:
                    recommended_clubs.append((club, match_score))
        
        # 如果基于专长没有推荐结果，推荐一些热门社团
        if not recommended_clubs:
            popular_clubs = Club.query.filter(
                Club.Status == '活跃',
                ~Club.ClubID.in_(existing_club_ids)
            ).order_by(Club.CurrentMembers.desc()).limit(3).all()
            
            recommended_clubs = [(club, 0) for club in popular_clubs]
        
        # 按匹配度排序，返回前5个
        recommended_clubs.sort(key=lambda x: x[1], reverse=True)
        return [club for club, score in recommended_clubs[:5]]
    
    @staticmethod
    def get_available_clubs(member_id, search_query=None, category=None, page=1, per_page=20):
        """
        获取可申请的社团列表
        
        Args:
            member_id: 会员ID
            search_query: 搜索关键词
            category: 社团类别过滤
            page: 页码
            per_page: 每页数量
        
        Returns:
            dict: 社团列表和分页信息
        """
        # 获取会员已加入或申请的社团ID
        existing_club_ids = [
            mc.ClubID for mc in MemberClub.query.filter_by(MemberID=member_id).all()
        ]
        
        # 构建查询
        query = Club.query.filter(
            Club.Status == '活跃',
            ~Club.ClubID.in_(existing_club_ids)
        )
        
        # 搜索过滤
        if search_query:
            query = query.filter(Club.ClubName.contains(search_query))
        
        # 类别过滤
        if category and category != 'all':
            query = query.filter(Club.Description == category)
        
        # 分页
        pagination = query.order_by(Club.FoundationDate.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return {
            'clubs': pagination.items,
            'pagination': pagination,
            'total': pagination.total
        }
    
    @staticmethod
    def apply_to_club(member_id, club_id, application_reason):
        """
        申请加入社团
        
        Args:
            member_id: 会员ID
            club_id: 社团ID
            application_reason: 申请理由
        
        Returns:
            tuple: (success: bool, message: str)
        """
        # 验证会员存在
        member = Member.query.get(member_id)
        if not member:
            return False, '会员不存在'
        
        # 验证社团存在且活跃
        club = Club.query.get(club_id)
        if not club:
            return False, '社团不存在'
        
        if club.Status != '活跃':
            return False, '该社团当前不接受新成员申请'
        
        # 检查是否已有申请记录
        existing_record = MemberClub.query.filter_by(
            MemberID=member_id,
            ClubID=club_id
        ).first()
        
        if existing_record:
            if existing_record.Status == '待审批':
                return False, '您已经申请过该社团，请等待审批'
            elif existing_record.Status == '已批准':
                return False, '您已经是该社团的成员'
            elif existing_record.Status == '已拒绝' and existing_record.Rejoinable == '否':
                return False, '您已被该社团拒绝且不允许重新申请'
        
        # 检查社团是否已满员
        if not club.can_accept_new_member():
            return False, '该社团已满员，暂时无法接受新成员'
        
        # 验证申请理由长度
        if application_reason and len(application_reason) > 200:
            return False, '申请理由长度不能超过200个字符'
        
        try:
            # 如果存在旧记录，删除或更新
            if existing_record:
                if existing_record.Status in ['已拒绝', '已退出']:
                    # 重新申请，更新记录
                    existing_record.Status = '待审批'
                    existing_record.ApplyTime = get_china_time()
                    existing_record.ApplicationReason = application_reason
                    existing_record.ApprovalTime = None
                    existing_record.Rejoinable = '是'
                else:
                    return False, '申请状态异常'
            else:
                # 创建新的申请记录
                member_club = MemberClub(
                    RecordID=generate_uuid(),
                    MemberID=member_id,
                    ClubID=club_id,
                    ApplyTime=get_china_time(),
                    Status='待审批',
                    ApplicationReason=application_reason,
                    ApprovalId=club.PresidentID,  # 设置审批人为社团会长
                    Rejoinable='是'  # 设置默认可重新加入
                )
                db.session.add(member_club)
            
            db.session.commit()
            return True, f'成功申请加入"{club.ClubName}"，请等待会长审批'
            
        except Exception as e:
            db.session.rollback()
            return False, f'申请失败：{str(e)}'

    @staticmethod
    def withdraw_from_club(member_id, club_id, withdraw_reason):
        """
        申请退出社团

        Args:
            member_id: 会员ID
            club_id: 社团ID
            withdraw_reason: 退出理由

        Returns:
            tuple: (success: bool, message: str)
        """
        # 验证会员存在
        member = Member.query.get(member_id)
        if not member:
            return False, '会员不存在'

        # 验证社团存在
        club = Club.query.get(club_id)
        if not club:
            return False, '社团不存在'

        # 检查是否是该社团的已批准成员
        member_club = MemberClub.query.filter_by(
            MemberID=member_id,
            ClubID=club_id,
            Status='已批准'
        ).first()

        if not member_club:
            return False, '您不是该社团的成员'

        try:
            # 创建退出申请记录
            approval_request = ApprovalRequest(
                RequestID=generate_uuid(),
                ApplicantID=member_id,
                RequestType='退会',
                RequestTime=get_china_time(),
                Status='待批',
                RelatedID=club_id,
                Comments=withdraw_reason
            )
            db.session.add(approval_request)
            db.session.commit()

            return True, f'成功提交退出"{club.ClubName}"的申请，请等待会长审批'

        except Exception as e:
            db.session.rollback()
            return False, f'申请失败：{str(e)}'

    @staticmethod
    def cancel_club_application(member_id, club_id):
        """
        取消社团申请

        Args:
            member_id: 会员ID
            club_id: 社团ID

        Returns:
            tuple: (success: bool, message: str)
        """
        # 查找待审批的申请记录
        member_club = MemberClub.query.filter_by(
            MemberID=member_id,
            ClubID=club_id,
            Status='待审批'
        ).first()

        if not member_club:
            return False, '未找到待审批的申请记录'

        try:
            # 删除申请记录
            db.session.delete(member_club)
            db.session.commit()

            club = Club.query.get(club_id)
            club_name = club.ClubName if club else '该社团'

            return True, f'已成功取消加入"{club_name}"的申请'

        except Exception as e:
            db.session.rollback()
            return False, f'取消申请失败：{str(e)}'

    
    @staticmethod
    def get_my_clubs(member_id, status=None):
        """
        获取会员的社团列表
        
        Args:
            member_id: 会员ID
            status: 状态过滤
        
        Returns:
            list: 社团关系列表
        """
        query = MemberClub.query.filter_by(MemberID=member_id)
        
        if status:
            query = query.filter_by(Status=status)
        
        return query.order_by(MemberClub.ApplyTime.desc()).all()
    
    @staticmethod
    def get_my_activities(member_id, status=None, limit=None):
        """
        获取会员参与的活动
        
        Args:
            member_id: 会员ID
            status: 活动状态过滤
            limit: 限制数量
        
        Returns:
            list: 活动列表
        """
        # 获取会员加入的社团
        joined_clubs = MemberClub.query.filter_by(
            MemberID=member_id,
            Status='已批准'
        ).all()
        
        club_ids = [mc.ClubID for mc in joined_clubs]
        
        if not club_ids:
            return []
        
        # 查询这些社团的活动
        query = Activity.query.filter(Activity.ClubID.in_(club_ids))
        
        if status:
            query = query.filter_by(Status=status)
        
        query = query.order_by(Activity.StartTime.desc())
        
        if limit:
            query = query.limit(limit)
        
        return query.all()

    @staticmethod
    def apply_create_club(member_id, club_data):
        """
        申请创建社团

        Args:
            member_id: 申请人ID
            club_data: 社团数据

        Returns:
            tuple: (success: bool, message: str)
        """
        # 验证申请人存在
        member = Member.query.get(member_id)
        if not member:
            return False, '申请人不存在'

        # 检查社团名称是否已存在
        existing_club = Club.query.filter_by(ClubName=club_data['club_name']).first()
        if existing_club:
            return False, '社团名称已存在，请选择其他名称'

        # 验证申请理由长度
        if len(club_data['application_reason']) > 500:
            return False, '申请理由长度不能超过500个字符'

        try:
            # 创建审批申请记录
            approval_request = ApprovalRequest(
                RequestID=generate_uuid(),
                ApplicantID=member_id,
                RequestType='其他',  # 创建社团申请
                RequestTime=get_china_time(),
                Status='待批',
                RelatedID=None,  # 暂时为空，等审批通过后创建社团
                Comments=f"申请创建社团：{club_data['club_name']}\n"
                        f"类别：{club_data['description']}\n"
                        f"最大成员数：{club_data.get('max_members', '未设置')}\n"
                        f"官网：{club_data.get('website', '未设置')}\n"
                        f"申请理由：{club_data['application_reason']}"
            )
            db.session.add(approval_request)
            db.session.commit()

            return True, f'成功提交创建社团"{club_data["club_name"]}"的申请，请等待管理员审批'

        except Exception as e:
            db.session.rollback()
            return False, f'申请失败：{str(e)}'

