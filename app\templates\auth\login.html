{% extends "base.html" %}

{% block title %}登录 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>用户登录
                    </h3>
                </div>
                <div class="card-body p-5">
                    <form method="POST" id="loginForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <div class="mb-4">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-1"></i>用户名
                            </label>
                            <input type="text" class="form-control form-control-lg" 
                                   id="username" name="username" required 
                                   placeholder="请输入用户名">
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>密码
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control form-control-lg"
                                       id="password" name="password" required
                                       placeholder="请输入密码">
                                <button class="btn btn-outline-secondary" type="button"
                                        id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    记住我
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>登录
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center">
                        <p class="text-muted mb-3">还没有账户？</p>
                        <a href="{{ url_for('auth.register') }}" class="btn btn-outline-primary">
                            <i class="bi bi-person-plus me-2"></i>立即注册
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 角色说明 -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="bi bi-info-circle me-2"></i>用户角色说明
                    </h6>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="p-2">
                                <i class="bi bi-person text-primary" style="font-size: 1.5rem;"></i>
                                <div class="small mt-1">普通会员</div>
                                <div class="text-muted" style="font-size: 0.8rem;">浏览申请社团</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-2">
                                <i class="bi bi-star text-warning" style="font-size: 1.5rem;"></i>
                                <div class="small mt-1">社团会长</div>
                                <div class="text-muted" style="font-size: 0.8rem;">管理社团活动</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-2">
                                <i class="bi bi-shield-check text-success" style="font-size: 1.5rem;"></i>
                                <div class="small mt-1">系统管理员</div>
                                <div class="text-muted" style="font-size: 0.8rem;">系统全面管理</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 密码显示/隐藏切换
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('bi-eye').addClass('bi-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('bi-eye-slash').addClass('bi-eye');
        }
    });
    
    // 表单验证
    $('#loginForm').on('submit', function(e) {
        const username = $('#username').val().trim();
        const password = $('#password').val();
        
        if (!username) {
            e.preventDefault();
            alert('请输入用户名');
            $('#username').focus();
            return false;
        }
        
        if (!password) {
            e.preventDefault();
            alert('请输入密码');
            $('#password').focus();
            return false;
        }
        
        // 显示加载状态
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>登录中...');
    });
    
    // 回车键提交
    $('#username, #password').keypress(function(e) {
        if (e.which === 13) {
            $('#loginForm').submit();
        }
    });
});
</script>
{% endblock %}
