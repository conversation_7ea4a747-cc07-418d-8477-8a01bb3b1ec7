{% extends "admin_base.html" %}

{% block title %}编辑成员信息 - {{ club.ClubName }} - 会长中心{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-1">
            <i class="fas fa-user-edit me-2"></i>编辑成员信息
        </h4>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('president.dashboard') }}">会长中心</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('president.clubs') }}">我的社团</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('president.club_members', club_id=club.ClubID) }}">成员管理</a></li>
                <li class="breadcrumb-item active">编辑成员</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('president.club_members', club_id=club.ClubID) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回成员列表
        </a>
    </div>
</div>

<!-- 成员基本信息卡片 -->
<div class="row">
    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>成员基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="user-avatar-large mx-auto mb-3">
                        {{ member_club.member.Name[0] if member_club.member.Name else member_club.member.Username[0] }}
                    </div>
                    <h5 class="mb-1">{{ member_club.member.Name or '未设置姓名' }}</h5>
                    <p class="text-muted mb-2">@{{ member_club.member.Username }}</p>
                    <span class="badge bg-{{ 'success' if member_club.Status == '已批准' else 'warning' if member_club.Status == '待审批' else 'danger' if member_club.Status == '已拒绝' else 'secondary' }}">
                        {{ member_club.Status }}
                    </span>
                </div>
                
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="text-muted small">性别</div>
                            <div class="fw-bold">{{ member_club.member.Gender or '未设置' }}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="text-muted small">年龄</div>
                            <div class="fw-bold">{{ member_club.member.Age or '未设置' }}{% if member_club.member.Age %}岁{% endif %}</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="text-center">
                            <div class="text-muted small">联系电话</div>
                            <div class="fw-bold">{{ member_club.member.Phone or '未设置' }}</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="text-center">
                            <div class="text-muted small">学院</div>
                            <div class="fw-bold">{{ member_club.member.College or '未设置' }}</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="text-center">
                            <div class="text-muted small">宿舍</div>
                            <div class="fw-bold">{{ member_club.member.Dormitory or '未设置' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 申请信息 -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>申请信息
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="text-muted small">申请时间</div>
                    <div class="fw-bold">{{ member_club.ApplyTime.strftime('%Y年%m月%d日 %H:%M') }}</div>
                </div>
                {% if member_club.ApprovalTime %}
                <div class="mb-3">
                    <div class="text-muted small">审批时间</div>
                    <div class="fw-bold text-success">{{ member_club.ApprovalTime.strftime('%Y年%m月%d日 %H:%M') }}</div>
                </div>
                {% endif %}
                {% if member_club.ApprovalId %}
                <div class="mb-3">
                    <div class="text-muted small">审批人</div>
                    <div class="fw-bold">{{ member_club.ApprovalId }}</div>
                </div>
                {% endif %}
                <div class="mb-0">
                    <div class="text-muted small">可重新加入</div>
                    <div class="fw-bold">
                        <span class="badge bg-{{ 'success' if member_club.Rejoinable == '是' else 'secondary' }}">
                            {{ member_club.Rejoinable }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 编辑表单 -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>编辑成员信息
                </h5>
            </div>
            <div class="card-body">
                {% if member_club.Status != '已批准' %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    只能编辑已批准成员的信息。当前成员状态：{{ member_club.Status }}
                </div>
                {% else %}
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <div class="row g-3">
                        <!-- 专长技能 -->
                        <div class="col-12">
                            <label for="specialty" class="form-label">
                                <i class="fas fa-star me-1"></i>专长技能
                            </label>
                            <textarea class="form-control" id="specialty" name="specialty" rows="3"
                                      placeholder="请输入成员的专长技能..."
                                      maxlength="200">{{ member_club.member.Specialty or '' }}</textarea>
                            <div class="form-text">最多200个字符，用于展示成员的特长和技能</div>
                        </div>
                        
                        <!-- 申请理由/备注 -->
                        <div class="col-12">
                            <label for="application_reason" class="form-label">
                                <i class="fas fa-comment me-1"></i>成员备注
                            </label>
                            <textarea class="form-control" id="application_reason" name="application_reason" rows="4"
                                      placeholder="请输入对该成员的备注信息..."
                                      maxlength="200">{{ member_club.ApplicationReason or '' }}</textarea>
                            <div class="form-text">最多200个字符，用于记录该成员的相关信息</div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('president.club_members', club_id=club.ClubID) }}" 
                           class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存修改
                        </button>
                    </div>
                </form>
                {% endif %}
            </div>
        </div>
        
        <!-- 操作历史 -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>操作历史
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">提交入会申请</h6>
                            <p class="text-muted mb-1">{{ member_club.ApplyTime.strftime('%Y年%m月%d日 %H:%M:%S') }}</p>
                            {% if member_club.ApplicationReason %}
                            <p class="small mb-0">申请理由：{{ member_club.ApplicationReason }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if member_club.ApprovalTime %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-{{ 'success' if member_club.Status == '已批准' else 'danger' }}"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">
                                {% if member_club.Status == '已批准' %}
                                申请已批准
                                {% elif member_club.Status == '已拒绝' %}
                                申请被拒绝
                                {% else %}
                                状态变更
                                {% endif %}
                            </h6>
                            <p class="text-muted mb-1">{{ member_club.ApprovalTime.strftime('%Y年%m月%d日 %H:%M:%S') }}</p>
                            {% if member_club.ApprovalId %}
                            <p class="small mb-0">操作人：{{ member_club.ApprovalId }}</p>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.user-avatar-large {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 2rem;
    border: 3px solid rgba(255,255,255,0.3);
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #dee2e6;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 字符计数
    $('textarea[maxlength]').each(function() {
        var maxLength = $(this).attr('maxlength');
        var currentLength = $(this).val().length;
        var helpText = $(this).next('.form-text');
        
        if (helpText.length) {
            helpText.append(' <span class="text-muted">(' + currentLength + '/' + maxLength + ')</span>');
        }
        
        $(this).on('input', function() {
            var currentLength = $(this).val().length;
            var counter = helpText.find('span');
            counter.text('(' + currentLength + '/' + maxLength + ')');
            
            if (currentLength > maxLength * 0.9) {
                counter.removeClass('text-muted').addClass('text-warning');
            } else {
                counter.removeClass('text-warning').addClass('text-muted');
            }
        });
    });
});
</script>
{% endblock %}
