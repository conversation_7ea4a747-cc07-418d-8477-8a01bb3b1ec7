#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
辅助函数模块
提供通用的工具函数和业务辅助方法
"""

import uuid
from datetime import datetime
from flask import session
import pytz
from app.models.member import Member

def get_current_user():
    """
    获取当前登录用户
    
    Returns:
        Member: 当前用户对象，未登录返回None
    """
    if 'user_id' in session:
        return Member.query.get(session['user_id'])
    return None

def generate_uuid():
    """
    生成UUID字符串
    
    Returns:
        str: 36位UUID字符串
    """
    return str(uuid.uuid4())

def format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    """
    格式化日期时间
    
    Args:
        dt: datetime对象
        format_str: 格式字符串
    
    Returns:
        str: 格式化后的时间字符串
    """
    if not dt:
        return ''
    
    # 确保时间为中国标准时间
    if dt.tzinfo is None:
        # 假设数据库中的时间为UTC+8
        china_tz = pytz.timezone('Asia/Shanghai')
        dt = china_tz.localize(dt)
    
    return dt.strftime(format_str)

def format_date(dt, format_str='%Y-%m-%d'):
    """
    格式化日期
    
    Args:
        dt: date或datetime对象
        format_str: 格式字符串
    
    Returns:
        str: 格式化后的日期字符串
    """
    if not dt:
        return ''
    return dt.strftime(format_str)

def get_china_time():
    """
    获取中国标准时间
    
    Returns:
        datetime: 当前中国标准时间
    """
    china_tz = pytz.timezone('Asia/Shanghai')
    return datetime.now(china_tz)

def validate_phone(phone):
    """
    验证电话号码格式
    对应database.sql中的约束：Phone REGEXP '^[0-9]{7,15}$'
    
    Args:
        phone: 电话号码字符串
    
    Returns:
        bool: 验证结果
    """
    if not phone:
        return True  # 允许为空
    
    import re
    return bool(re.match(r'^[0-9]{7,15}$', phone))

def validate_age(age):
    """
    验证年龄范围
    对应database.sql中的约束：Age > 0 AND Age < 150
    
    Args:
        age: 年龄数值
    
    Returns:
        bool: 验证结果
    """
    if age is None:
        return True  # 允许为空
    
    try:
        age_int = int(age)
        return 0 < age_int < 150
    except (ValueError, TypeError):
        return False

def validate_website(website):
    """
    验证网站URL格式
    对应database.sql中的约束：Website LIKE 'http%'
    
    Args:
        website: 网站URL字符串
    
    Returns:
        bool: 验证结果
    """
    if not website:
        return True  # 允许为空
    
    return website.startswith('http')

def get_user_role_display(role):
    """
    获取用户角色的显示名称
    
    Args:
        role: 角色枚举值
    
    Returns:
        str: 角色显示名称
    """
    role_map = {
        '管理员': '系统管理员',
        '会长': '社团会长',
        '会员': '普通会员'
    }
    return role_map.get(role, role)

def get_club_status_display(status):
    """
    获取社团状态的显示名称
    
    Args:
        status: 状态枚举值
    
    Returns:
        str: 状态显示名称
    """
    status_map = {
        '活跃': '正常运营',
        '休眠': '暂停活动',
        '解散': '已解散'
    }
    return status_map.get(status, status)

def get_activity_status_display(status):
    """
    获取活动状态的显示名称
    
    Args:
        status: 状态枚举值
    
    Returns:
        str: 状态显示名称
    """
    status_map = {
        '计划中': '筹备中',
        '进行中': '进行中',
        '已完成': '已结束',
        '已取消': '已取消'
    }
    return status_map.get(status, status)

def calculate_days_between(start_date, end_date):
    """
    计算两个日期之间的天数
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        int: 天数差
    """
    if not start_date or not end_date:
        return 0
    
    return (end_date - start_date).days

def is_valid_uuid(uuid_string):
    """
    验证UUID格式
    
    Args:
        uuid_string: UUID字符串
    
    Returns:
        bool: 验证结果
    """
    try:
        uuid.UUID(uuid_string)
        return True
    except (ValueError, TypeError):
        return False

def truncate_text(text, max_length=50, suffix='...'):
    """
    截断文本
    
    Args:
        text: 原始文本
        max_length: 最大长度
        suffix: 后缀
    
    Returns:
        str: 截断后的文本
    """
    if not text:
        return ''
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def get_pagination_info(page, per_page, total):
    """
    获取分页信息
    
    Args:
        page: 当前页码
        per_page: 每页数量
        total: 总数量
    
    Returns:
        dict: 分页信息
    """
    total_pages = (total + per_page - 1) // per_page
    has_prev = page > 1
    has_next = page < total_pages
    
    return {
        'page': page,
        'per_page': per_page,
        'total': total,
        'total_pages': total_pages,
        'has_prev': has_prev,
        'has_next': has_next,
        'prev_num': page - 1 if has_prev else None,
        'next_num': page + 1 if has_next else None
    }
