{% extends "admin_base.html" %}

{% block title %}{{ club.ClubName }} - 成员管理 - 管理后台{% endblock %}

{% block content %}
<!-- 简单导航 -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-2">{{ club.ClubName }} - 成员管理</h5>
                <p class="text-muted mb-0">会长：{{ club.president.Name if club.president else '未知' }} | 成员：{{ club.CurrentMembers or 0 }}人</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ url_for('admin.clubs') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回社团列表
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 成员列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>成员列表
            <span class="badge bg-primary ms-2">{{ pagination.total }}人</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if members %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="25%">成员信息</th>
                        <th width="15%">角色</th>
                        <th width="15%">联系方式</th>
                        <th width="15%">申请时间</th>
                        <th width="15%">状态</th>
                        <th width="15%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for member_club in members %}
                    <tr>
                        <td>
                            <div>
                                <h6 class="mb-1">{{ member_club.member.Name if member_club.member else '未知' }}</h6>
                                <small class="text-muted">{{ member_club.member.Username if member_club.member else '未知' }}</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'danger' if member_club.member.Role == '管理员' else 'warning' if member_club.member.Role == '会长' else 'primary' }}">
                                {{ member_club.member.Role if member_club.member else '未知' }}
                            </span>
                        </td>
                        <td>
                            <div class="small">
                                {% if member_club.member and member_club.member.Phone %}
                                <div><i class="fas fa-phone me-1"></i>{{ member_club.member.Phone }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ member_club.ApplyTime.strftime('%Y-%m-%d') if member_club.ApplyTime else '未知' }}
                            </small>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if member_club.Status == '已批准' else 'warning' if member_club.Status == '待审批' else 'danger' }}">
                                {{ member_club.Status }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                {% if member_club.Status == '待审批' %}
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="approveMember('{{ member_club.RecordID }}')"
                                        data-bs-toggle="tooltip" title="批准">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="rejectMember('{{ member_club.RecordID }}')"
                                        data-bs-toggle="tooltip" title="拒绝">
                                    <i class="fas fa-times"></i>
                                </button>
                                {% endif %}
                                {% if member_club.Status == '已批准' and member_club.member.Role != '管理员' %}
                                <button type="button" class="btn btn-outline-warning" 
                                        onclick="removeMember('{{ member_club.RecordID }}', '{{ member_club.member.Name if member_club.member else "该成员" }}')"
                                        data-bs-toggle="tooltip" title="移除">
                                    <i class="fas fa-user-minus"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users text-muted" style="font-size: 4rem;"></i>
            <h5 class="mt-3 text-muted">暂无成员数据</h5>
            <p class="text-muted">该社团暂时没有成员</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<nav aria-label="成员列表分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.club_members', club_id=club.ClubID, page=pagination.prev_num) }}">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        {% endif %}

        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.club_members', club_id=club.ClubID, page=page_num) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}

        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.club_members', club_id=club.ClubID, page=pagination.next_num) }}">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// 批准成员
function approveMember(recordId) {
    if (confirm('确定要批准该成员的申请吗？')) {
        fetch(`/admin/clubs/{{ club.ClubID }}/members/${recordId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('批准失败：' + data.message);
            }
        })
        .catch(error => {
            alert('批准失败：' + error.message);
        });
    }
}

// 拒绝成员
function rejectMember(recordId) {
    if (confirm('确定要拒绝该成员的申请吗？')) {
        fetch(`/admin/clubs/{{ club.ClubID }}/members/${recordId}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('拒绝失败：' + data.message);
            }
        })
        .catch(error => {
            alert('拒绝失败：' + error.message);
        });
    }
}

// 移除成员
function removeMember(recordId, memberName) {
    if (confirm(`确定要移除成员"${memberName}"吗？`)) {
        // 发送移除成员请求
        fetch(`/admin/clubs/{{ club.ClubID }}/members/${recordId}/remove`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('移除失败：' + data.message);
            }
        })
        .catch(error => {
            alert('移除失败：' + error.message);
        });
    }
}

// 初始化工具提示
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
