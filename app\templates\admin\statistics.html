{% extends "admin_base.html" %}

{% block title %}数据统计 - 管理后台{% endblock %}
{% block page_title %}数据统计{% endblock %}
{% block title_icon %}<i class="fas fa-chart-line"></i>{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-nav">
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.dashboard') }}">
                <i class="fas fa-home"></i>
            </a>
        </li>
        <li class="breadcrumb-item active">数据统计</li>
    </ol>
</nav>
{% endblock %}

{% block page_actions %}
<div class="d-flex justify-content-between align-items-center mt-3">
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-primary" onclick="refreshData()">
            <i class="fas fa-sync me-1"></i>刷新数据
        </button>
        <button type="button" class="btn btn-outline-secondary" onclick="exportReport()">
            <i class="fas fa-download me-1"></i>导出报告
        </button>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 概览统计 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="h3 mb-0 text-primary">{{ overview.total_members }}</div>
                        <div class="text-muted">总用户数</div>
                        <div class="small text-success">
                            <i class="fas fa-arrow-up me-1"></i>{{ overview.member_growth }}% 本月增长
                        </div>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="h3 mb-0 text-success">{{ overview.total_clubs }}</div>
                        <div class="text-muted">活跃社团</div>
                        <div class="small text-success">
                            <i class="fas fa-arrow-up me-1"></i>{{ overview.club_growth }}% 本月增长
                        </div>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-layer-group fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="h3 mb-0 text-warning">{{ overview.total_activities }}</div>
                        <div class="text-muted">总活动数</div>
                        <div class="small text-success">
                            <i class="fas fa-arrow-up me-1"></i>{{ overview.activity_growth }}% 本月增长
                        </div>
                    </div>
                    <div class="text-warning">
                        <i class="fas fa-calendar-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="h3 mb-0 text-info">{{ overview.venue_usage }}%</div>
                        <div class="text-muted">场馆使用率</div>
                        <div class="small text-{{ 'success' if overview.venue_usage_trend > 0 else 'danger' }}">
                            <i class="fas fa-arrow-{{ 'up' if overview.venue_usage_trend > 0 else 'down' }} me-1"></i>
                            {{ overview.venue_usage_trend }}% 较上月
                        </div>
                    </div>
                    <div class="text-info">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 用户增长趋势 -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2 text-primary"></i>用户增长趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="memberGrowthChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 社团类别分布 -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2 text-success"></i>社团类别分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="clubCategoryChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- 活动类型统计 -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2 text-warning"></i>活动类型统计
                </h5>
            </div>
            <div class="card-body">
                <canvas id="activityTypeChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 场馆使用情况 -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2 text-info"></i>场馆使用情况
                </h5>
            </div>
            <div class="card-body">
                <canvas id="venueUsageChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 详细统计表格 -->
<div class="row">
    <!-- 热门社团排行 -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2 text-warning"></i>热门社团排行
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>排名</th>
                                <th>社团名称</th>
                                <th>成员数</th>
                                <th>活动数</th>
                                <th>活跃度</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for club in top_clubs %}
                            <tr>
                                <td>
                                    <span class="badge bg-{{ 'warning' if loop.index == 1 else 'secondary' if loop.index == 2 else 'info' if loop.index == 3 else 'light text-dark' }}">
                                        #{{ loop.index }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="club-avatar-sm me-2">
                                            {{ club.ClubName[0] }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ club.ClubName }}</div>
                                            <div class="text-muted small">{{ club.Description }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ club.CurrentMembers }}</td>
                                <td>{{ club.activity_count }}</td>
                                <td>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-success" style="width: {{ club.activity_score }}%"></div>
                                    </div>
                                    <small class="text-muted">{{ club.activity_score }}%</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 活跃用户排行 -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2 text-primary"></i>活跃用户排行
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>排名</th>
                                <th>用户</th>
                                <th>角色</th>
                                <th>参与活动</th>
                                <th>活跃度</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for member in active_members %}
                            <tr>
                                <td>
                                    <span class="badge bg-{{ 'warning' if loop.index == 1 else 'secondary' if loop.index == 2 else 'info' if loop.index == 3 else 'light text-dark' }}">
                                        #{{ loop.index }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-2">
                                            {{ member.Name[0] if member.Name else member.Username[0] }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ member.Name or member.Username }}</div>
                                            <div class="text-muted small">@{{ member.Username }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if member.Role == '管理员' else 'warning' if member.Role == '会长' else 'primary' }}">
                                        {{ member.Role }}
                                    </span>
                                </td>
                                <td>{{ member.activity_count }}</td>
                                <td>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-primary" style="width: {{ member.activity_score }}%"></div>
                                    </div>
                                    <small class="text-muted">{{ member.activity_score }}%</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 时间范围选择器 -->
<div class="card border-0 shadow-sm mt-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <label for="dateRange" class="form-label">统计时间范围</label>
                <select class="form-select" id="dateRange" onchange="updateCharts()">
                    <option value="7">最近7天</option>
                    <option value="30" selected>最近30天</option>
                    <option value="90">最近90天</option>
                    <option value="365">最近一年</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="chartType" class="form-label">图表类型</label>
                <select class="form-select" id="chartType" onchange="updateCharts()">
                    <option value="line">折线图</option>
                    <option value="bar">柱状图</option>
                    <option value="area">面积图</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-primary" onclick="updateCharts()">
                        <i class="fas fa-sync me-1"></i>更新图表
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-secondary" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>导出报告
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.club-avatar-sm {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.8rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 图表配置
const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
        }
    },
    scales: {
        y: {
            beginAtZero: true
        }
    }
};

// 用户增长趋势图
const memberGrowthCtx = document.getElementById('memberGrowthChart').getContext('2d');
const memberGrowthChart = new Chart(memberGrowthCtx, {
    type: 'line',
    data: {
        labels: {{ member_growth_labels | tojson }},
        datasets: [{
            label: '新增用户',
            data: {{ member_growth_data | tojson }},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: chartOptions
});

// 社团类别分布图
const clubCategoryCtx = document.getElementById('clubCategoryChart').getContext('2d');
const clubCategoryChart = new Chart(clubCategoryCtx, {
    type: 'doughnut',
    data: {
        labels: {{ club_category_labels | tojson }},
        datasets: [{
            data: {{ club_category_data | tojson }},
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'right',
            }
        }
    }
});

// 活动类型统计图
const activityTypeCtx = document.getElementById('activityTypeChart').getContext('2d');
const activityTypeChart = new Chart(activityTypeCtx, {
    type: 'bar',
    data: {
        labels: {{ activity_type_labels | tojson }},
        datasets: [{
            label: '活动数量',
            data: {{ activity_type_data | tojson }},
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)'
            ]
        }]
    },
    options: chartOptions
});

// 场馆使用情况图
const venueUsageCtx = document.getElementById('venueUsageChart').getContext('2d');
const venueUsageChart = new Chart(venueUsageCtx, {
    type: 'line',
    data: {
        labels: {{ venue_usage_labels | tojson }},
        datasets: [{
            label: '使用率 (%)',
            data: {{ venue_usage_data | tojson }},
            borderColor: 'rgb(255, 159, 64)',
            backgroundColor: 'rgba(255, 159, 64, 0.2)',
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});

// 刷新数据
function refreshData() {
    location.reload();
}

// 更新图表
function updateCharts() {
    const dateRange = document.getElementById('dateRange').value;
    const chartType = document.getElementById('chartType').value;
    
    // 发送AJAX请求更新图表数据
    fetch(`/admin/api/statistics?range=${dateRange}&type=${chartType}`)
        .then(response => response.json())
        .then(data => {
            // 更新图表数据
            memberGrowthChart.data.labels = data.member_growth_labels;
            memberGrowthChart.data.datasets[0].data = data.member_growth_data;
            memberGrowthChart.update();
            
            activityTypeChart.data.labels = data.activity_type_labels;
            activityTypeChart.data.datasets[0].data = data.activity_type_data;
            activityTypeChart.update();
            
            venueUsageChart.data.labels = data.venue_usage_labels;
            venueUsageChart.data.datasets[0].data = data.venue_usage_data;
            venueUsageChart.update();
        })
        .catch(error => {
            console.error('更新图表失败:', error);
        });
}

// 导出报告
function exportReport() {
    const dateRange = document.getElementById('dateRange').value;
    window.open(`/admin/statistics/export?range=${dateRange}`, '_blank');
}
</script>
{% endblock %}
