-- 1. 创建数据库
DROP DATABASE IF EXISTS ClubManagementSystem;
CREATE DATABASE ClubManagementSystem CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ClubManagementSystem;

-- 2. 创建成员表
CREATE TABLE Members (
    MemberID CHAR(36) PRIMARY KEY,
    Name VARCHAR(50) NOT NULL,
    Age INT CHECK (Age > 0 AND Age < 150),
    Gender ENUM('男', '女', '其他') NOT NULL,
    College VARCHAR(100),
    Dormitory VARCHAR(50),
    Phone VARCHAR(20) CHECK (Phone REGEXP '^[0-9]{7,15}$'),
    Specialty VARCHAR(200),
    Username VARCHAR(20) NOT NULL UNIQUE,
    Password VARCHAR(30) NOT NULL,
    Role ENUM('管理员', '会长', '会员') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 3. 创建活动场馆表
CREATE TABLE Venues (
    VenueID CHAR(36) PRIMARY KEY,
    VenueName VARCHAR(100) NOT NULL,
    Location VARCHAR(100) NOT NULL,
    Address VARCHAR(200) NOT NULL,
    ContactPhone VARCHAR(20) CHECK (ContactPhone REGEXP '^[0-9]{7,15}$'),
    Capacity INT CHECK (Capacity > 0),
    VenueType ENUM('室内', '室外', '多功能厅', '体育馆', '其他') NOT NULL,
    AvailabTime VARCHAR(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 4. 创建社团表
CREATE TABLE Clubs (
    ClubID CHAR(36) PRIMARY KEY,
    ClubName VARCHAR(100) NOT NULL,
    Description ENUM('学术', '体育', '艺术', '公益', '娱乐', '其他') NOT NULL,
    MaxMembers INT CHECK (MaxMembers > 0),
    CurrentMembers INT CHECK (CurrentMembers >= 0),
    PresidentID CHAR(36) NOT NULL,
    Website VARCHAR(200) CHECK (Website LIKE 'http%' OR Website LIKE 'https%'),
    FoundationDate DATE NOT NULL,
    Category VARCHAR(10) CHECK (Category >= '0'),
    Status ENUM('活跃', '休眠', '解散') NOT NULL,
    FOREIGN KEY (PresidentID) REFERENCES Members(MemberID) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5. 创建社团活动表
CREATE TABLE Activities (
    ActivityID CHAR(36) PRIMARY KEY,
    ClubID CHAR(36) NOT NULL,
    ActivityName VARCHAR(100) NOT NULL,
    ActivityType ENUM('讲座', '比赛', '培训', '展览', '演出', '会议', '其他') NOT NULL,
    StartTime DATETIME NOT NULL,
    EndTime DATETIME NOT NULL,
    Description VARCHAR(500),
    OrganizerID CHAR(36) NOT NULL,
    VenueID CHAR(36) NOT NULL,
    ParticipantLimit INT CHECK (ParticipantLimit >= 0),
    Status ENUM('计划中', '进行中', '已完成', '已取消') NOT NULL,
    ActualParticipant INT NOT NULL CHECK (ActualParticipant >= 0),
    FOREIGN KEY (ClubID) REFERENCES Clubs(ClubID) ON DELETE CASCADE,
    FOREIGN KEY (OrganizerID) REFERENCES Members(MemberID) ON DELETE RESTRICT,
    FOREIGN KEY (VenueID) REFERENCES Venues(VenueID) ON DELETE RESTRICT,
    CHECK (EndTime > StartTime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 6. 创建会员社团关联表
CREATE TABLE ClubMembers (
    RecordID CHAR(36) PRIMARY KEY,
    MemberID CHAR(36) NOT NULL,
    ClubID CHAR(36) NOT NULL,
    ApplyTime DATETIME NOT NULL,
    Status ENUM('待审批', '已批准', '已拒绝', '已退出') NOT NULL,
    ApplicationReason VARCHAR(200),
    ApprovalId VARCHAR(20) NOT NULL,
    ApprovalTime DATETIME,
    Rejoinable ENUM('是', '否') NOT NULL,
    FOREIGN KEY (MemberID) REFERENCES Members(MemberID) ON DELETE CASCADE,
    FOREIGN KEY (ClubID) REFERENCES Clubs(ClubID) ON DELETE CASCADE,
    UNIQUE KEY (MemberID, ClubID)  -- 防止重复加入
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 7. 创建审批申请表
CREATE TABLE ApprovalRequests (
    RequestID CHAR(36) PRIMARY KEY,
    ApplicantID CHAR(36) NOT NULL,
    RequestType ENUM('入会', '退会', '活动申请', '其他') NOT NULL,
    RequestTime DATETIME NOT NULL,
    Status ENUM('待批', '已批', '已拒') NOT NULL,
    RelatedID CHAR(36),
    ApprovalTime DATETIME,
    Comments VARCHAR(200),
    FOREIGN KEY (ApplicantID) REFERENCES Members(MemberID) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 8. 创建触发器

-- 触发器1: 确保社团当前成员不超过最大成员数
DELIMITER //
CREATE TRIGGER check_club_members_insert
BEFORE INSERT ON Clubs
FOR EACH ROW
BEGIN
    IF NEW.CurrentMembers > NEW.MaxMembers THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = '当前成员数不能超过最大成员数';
    END IF;
END//
DELIMITER ;

DELIMITER //
CREATE TRIGGER check_club_members_update
BEFORE UPDATE ON Clubs
FOR EACH ROW
BEGIN
    IF NEW.CurrentMembers > NEW.MaxMembers THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = '当前成员数不能超过最大成员数';
    END IF;
END//
DELIMITER ;

-- 触发器2: 更新社团当前成员数
DELIMITER //
CREATE TRIGGER update_club_members_after_join
AFTER INSERT ON ClubMembers
FOR EACH ROW
BEGIN
    IF NEW.Status = '已批准' THEN
        UPDATE Clubs 
        SET CurrentMembers = CurrentMembers + 1 
        WHERE ClubID = NEW.ClubID;
    END IF;
END//
DELIMITER ;

DELIMITER //
CREATE TRIGGER update_club_members_after_status_change
AFTER UPDATE ON ClubMembers
FOR EACH ROW
BEGIN
    -- 从待审批/已拒绝变为已批准
    IF NEW.Status = '已批准' AND OLD.Status != '已批准' THEN
        UPDATE Clubs 
        SET CurrentMembers = CurrentMembers + 1 
        WHERE ClubID = NEW.ClubID;
    -- 从已批准变为其他状态
    ELSEIF OLD.Status = '已批准' AND NEW.Status != '已批准' THEN
        UPDATE Clubs 
        SET CurrentMembers = CurrentMembers - 1 
        WHERE ClubID = NEW.ClubID;
    END IF;
END//
DELIMITER ;



-- 9. 创建索引

-- 为常用查询字段创建索引
CREATE INDEX idx_members_username ON Members(Username);
CREATE INDEX idx_members_role ON Members(Role);
CREATE INDEX idx_clubs_status ON Clubs(Status);
CREATE INDEX idx_clubmembers_status ON ClubMembers(Status);
CREATE INDEX idx_activities_clubid ON Activities(ClubID);
CREATE INDEX idx_activities_status ON Activities(Status);
CREATE INDEX idx_activities_time ON Activities(StartTime, EndTime);
CREATE INDEX idx_approvalrequests_status ON ApprovalRequests(Status);

-- 10. 插入测试数据

-- 插入成员数据
INSERT INTO Members (MemberID, Name, Age, Gender, College, Dormitory, Phone, Specialty, Username, Password, Role) VALUES
-- 管理员
('admin-001', '张管理', 35, '男', '计算机学院', '教师公寓A101', '13800138001', '系统管理员', 'admin', '123456', '管理员'),

-- 会长
('p001', '李文华', 22, '男', '计算机学院', '学生公寓1号楼201', '13800138002', '软件开发', 'liwenhua', '123456', '会长'),
('p002', '王美丽', 21, '女', '艺术学院', '学生公寓2号楼305', '13800138003', '音乐表演', 'wangmeili', '123456', '会长'),
('p003', '陈强', 23, '男', '体育学院', '学生公寓3号楼102', '13800138004', '篮球运动', 'chenqiang', '123456', '会长'),
('p004', '刘雅静', 20, '女', '文学院', '学生公寓2号楼208', '13800138005', '文学创作', 'liuyajing', '123456', '会长'),
('p005', '赵志明', 22, '男', '理学院', '学生公寓1号楼315', '13800138006', '数学建模', 'zhaozhiming', '123456', '会长'),

-- 普通会员
('m001', '张三', 19, '男', '计算机学院', '学生公寓1号楼101', '13800138007', 'Python编程', 'zhangsan', '123456', '会员'),
('m002', '李四', 20, '女', '计算机学院', '学生公寓2号楼102', '13800138008', 'Java开发', 'lisi', '123456', '会员'),
('m003', '王五', 21, '男', '艺术学院', '学生公寓3号楼103', '13800138009', '绘画', 'wangwu', '123456', '会员'),
('m004', '赵六', 19, '女', '艺术学院', '学生公寓2号楼104', '13800138010', '舞蹈', 'zhaoliu', '123456', '会员'),
('m005', '孙七', 22, '男', '体育学院', '学生公寓1号楼105', '13800138011', '足球', 'sunqi', '123456', '会员'),
('m006', '周八', 20, '女', '体育学院', '学生公寓2号楼106', '13800138012', '羽毛球', 'zhouba', '123456', '会员'),
('m007', '吴九', 21, '男', '文学院', '学生公寓3号楼107', '13800138013', '诗歌创作', 'wujiu', '123456', '会员'),
('m008', '郑十', 19, '女', '文学院', '学生公寓2号楼108', '13800138014', '小说写作', 'zhengshi', '123456', '会员'),
('m009', '钱一', 20, '男', '理学院', '学生公寓1号楼109', '13800138015', '数学', 'qianyi', '123456', '会员'),
('m010', '孙二', 21, '女', '理学院', '学生公寓2号楼110', '13800138016', '物理', 'suner', '123456', '会员'),
('m011', '李明', 19, '男', '工学院', '学生公寓1号楼111', '13800138017', '机械设计', 'liming', '123456', '会员'),
('m012', '张丽', 20, '女', '工学院', '学生公寓2号楼112', '13800138018', '电子工程', 'zhangli', '123456', '会员'),
('m013', '王刚', 22, '男', '经济学院', '学生公寓3号楼113', '13800138019', '金融', 'wanggang', '123456', '会员'),
('m014', '刘芳', 21, '女', '经济学院', '学生公寓2号楼114', '13800138020', '会计', 'liufang', '123456', '会员'),
('m015', '陈浩', 20, '男', '法学院', '学生公寓1号楼115', '13800138021', '法律', 'chenhao', '123456', '会员');

-- 插入场馆数据
INSERT INTO Venues (VenueID, VenueName, Location, Address, ContactPhone, Capacity, VenueType, AvailabTime) VALUES
('v001', '学术报告厅', '图书馆', '图书馆3楼', '02088888001', 200, '多功能厅', '8:00-22:00'),
('v002', '体育馆', '体育中心', '体育中心主馆', '02088888002', 1000, '体育馆', '6:00-22:00'),
('v003', '音乐厅', '艺术楼', '艺术楼1楼', '02088888003', 300, '室内', '9:00-21:00'),
('v004', '舞蹈室', '艺术楼', '艺术楼2楼', '02088888004', 50, '室内', '8:00-22:00'),
('v005', '篮球场', '体育中心', '体育中心室外场地', '02088888005', 100, '室外', '6:00-23:00'),
('v006', '足球场', '体育中心', '体育中心足球场', '02088888006', 500, '室外', '6:00-22:00'),
('v007', '会议室A', '行政楼', '行政楼2楼201', '02088888007', 30, '室内', '8:00-18:00'),
('v008', '会议室B', '行政楼', '行政楼2楼202', '02088888008', 50, '室内', '8:00-18:00'),
('v009', '实验室', '理学楼', '理学楼3楼', '02088888009', 40, '室内', '8:00-20:00'),
('v010', '展览厅', '学生活动中心', '学生活动中心1楼', '02088888010', 150, '多功能厅', '9:00-21:00');

-- 插入社团数据
INSERT INTO Clubs (ClubID, ClubName, Description, MaxMembers, CurrentMembers, PresidentID, Website, FoundationDate, Category, Status) VALUES
('c001', '计算机协会', '学术', 100, 25, 'p001', 'https://cs-club.edu.cn', '2024-09-15', '1', '活跃'),
('c002', '音乐社', '艺术', 80, 18, 'p002', 'https://music-club.edu.cn', '2024-10-01', '2', '活跃'),
('c003', '篮球社', '体育', 60, 22, 'p003', 'https://basketball-club.edu.cn', '2024-09-20', '3', '活跃'),
('c004', '文学社', '学术', 50, 15, 'p004', 'https://literature-club.edu.cn', '2024-10-10', '4', '活跃'),
('c005', '数学建模社', '学术', 40, 12, 'p005', 'https://math-club.edu.cn', '2024-11-01', '5', '活跃'),
('c006', '摄影社', '艺术', 70, 8, 'p002', 'https://photo-club.edu.cn', '2024-11-15', '6', '休眠'),
('c007', '羽毛球社', '体育', 50, 16, 'p003', 'https://badminton-club.edu.cn', '2024-10-25', '7', '活跃');

-- 插入活动数据
INSERT INTO Activities (ActivityID, ClubID, ActivityName, ActivityType, StartTime, EndTime, Description, OrganizerID, VenueID, ParticipantLimit, Status, ActualParticipant) VALUES
-- 已完成的活动
('a001', 'c001', 'Python编程入门讲座', '讲座', '2025-04-15 14:00:00', '2025-04-15 16:00:00', '面向新手的Python编程基础讲座', 'p001', 'v001', 150, '已完成', 120),
('a002', 'c002', '春季音乐会', '演出', '2025-04-20 19:00:00', '2025-04-20 21:00:00', '学生原创音乐作品展演', 'p002', 'v003', 250, '已完成', 280),
('a003', 'c003', '新生篮球友谊赛', '比赛', '2025-04-25 15:00:00', '2025-04-25 17:00:00', '新生篮球技能展示赛', 'p003', 'v005', 80, '已完成', 75),
('a004', 'c004', '诗歌朗诵比赛', '比赛', '2025-05-01 14:00:00', '2025-05-01 16:30:00', '原创诗歌朗诵比赛', 'p004', 'v001', 100, '已完成', 85),
('a005', 'c005', '数学建模培训', '培训', '2025-05-05 09:00:00', '2025-05-05 17:00:00', '数学建模基础技能培训', 'p005', 'v009', 35, '已完成', 32),

-- 进行中的活动
('a006', 'c001', 'Web开发工作坊', '培训', '2025-06-08 09:00:00', '2025-06-08 17:00:00', '前端和后端开发技术培训', 'p001', 'v009', 40, '进行中', 38),

-- 计划中的活动
('a007', 'c002', '夏季音乐节', '演出', '2025-06-15 18:00:00', '2025-06-15 22:00:00', '大型户外音乐节活动', 'p002', 'v006', 500, '计划中', 0),
('a008', 'c003', '篮球联赛决赛', '比赛', '2025-06-20 16:00:00', '2025-06-20 18:00:00', '校内篮球联赛总决赛', 'p003', 'v002', 800, '计划中', 0),
('a009', 'c004', '文学创作研讨会', '会议', '2025-06-12 14:00:00', '2025-06-12 17:00:00', '文学创作技巧分享与讨论', 'p004', 'v007', 25, '计划中', 0),
('a010', 'c005', '数学竞赛', '比赛', '2025-06-18 09:00:00', '2025-06-18 12:00:00', '校内数学建模竞赛', 'p005', 'v009', 30, '计划中', 0),
('a011', 'c007', '羽毛球锦标赛', '比赛', '2025-06-25 14:00:00', '2025-06-25 18:00:00', '校内羽毛球单双打锦标赛', 'p003', 'v002', 60, '计划中', 0),

-- 已取消的活动
('a012', 'c006', '摄影展览', '展览', '2025-05-20 10:00:00', '2025-05-22 18:00:00', '学生摄影作品展览', 'p002', 'v010', 200, '已取消', 0);

-- 插入社团成员关联数据
INSERT INTO ClubMembers (RecordID, MemberID, ClubID, ApplyTime, Status, ApplicationReason, ApprovalId, ApprovalTime, Rejoinable) VALUES
-- 计算机协会成员
('cm-001', 'p001', 'c001', '2024-09-15 10:00:00', '已批准', '创建社团', 'admin-001', '2024-09-15 10:00:00', '是'),
('cm-002', 'm001', 'c001', '2024-09-20 14:30:00', '已批准', '对编程很感兴趣，希望提升技能', 'p001', '2024-09-20 15:00:00', '是'),
('cm-003', 'm002', 'c001', '2024-09-25 16:20:00', '已批准', '想学习更多编程知识', 'p001', '2024-09-25 16:30:00', '是'),
('cm-004', 'm011', 'c001', '2024-10-01 09:15:00', '已批准', '希望参与项目开发', 'p001', '2024-10-01 09:30:00', '是'),
('cm-005', 'm012', 'c001', '2024-10-05 11:45:00', '已批准', '对人工智能感兴趣', 'p001', '2024-10-05 12:00:00', '是'),

-- 音乐社成员
('cm-006', 'p002', 'c002', '2024-10-01 10:00:00', '已批准', '创建社团', 'admin-001', '2024-10-01 10:00:00', '是'),
('cm-007', 'm003', 'c002', '2024-10-08 15:20:00', '已批准', '热爱音乐，会弹吉他', 'p002', '2024-10-08 15:30:00', '是'),
('cm-008', 'm004', 'c002', '2024-10-12 13:40:00', '已批准', '喜欢唱歌，想参加演出', 'p002', '2024-10-12 14:00:00', '是'),

-- 篮球社成员
('cm-009', 'p003', 'c003', '2024-09-20 10:00:00', '已批准', '创建社团', 'admin-001', '2024-09-20 10:00:00', '是'),
('cm-010', 'm005', 'c003', '2024-09-25 17:30:00', '已批准', '篮球爱好者，想参加比赛', 'p003', '2024-09-25 17:45:00', '是'),
('cm-011', 'm006', 'c003', '2024-10-02 14:15:00', '已批准', '希望提高篮球技术', 'p003', '2024-10-02 14:30:00', '是'),

-- 文学社成员
('cm-012', 'p004', 'c004', '2024-10-10 10:00:00', '已批准', '创建社团', 'admin-001', '2024-10-10 10:00:00', '是'),
('cm-013', 'm007', 'c004', '2024-10-15 16:20:00', '已批准', '喜欢写诗，想交流创作心得', 'p004', '2024-10-15 16:30:00', '是'),
('cm-014', 'm008', 'c004', '2024-10-20 11:30:00', '已批准', '热爱文学创作', 'p004', '2024-10-20 11:45:00', '是'),

-- 数学建模社成员
('cm-015', 'p005', 'c005', '2024-11-01 10:00:00', '已批准', '创建社团', 'admin-001', '2024-11-01 10:00:00', '是'),
('cm-016', 'm009', 'c005', '2024-11-05 14:20:00', '已批准', '对数学建模很感兴趣', 'p005', '2024-11-05 14:30:00', '是'),
('cm-017', 'm010', 'c005', '2024-11-10 15:40:00', '已批准', '想参加数学竞赛', 'p005', '2024-11-10 15:50:00', '是'),

-- 羽毛球社成员
('cm-018', 'p003', 'c007', '2024-10-25 10:00:00', '已批准', '创建社团', 'admin-001', '2024-10-25 10:00:00', '是'),
('cm-019', 'm006', 'c007', '2024-11-01 16:30:00', '已批准', '羽毛球爱好者', 'p003', '2024-11-01 16:45:00', '是'),

-- 待审批申请
('cm-020', 'm013', 'c001', '2025-06-08 10:30:00', '待审批', '想学习编程技术', '', NULL, '是'),
('cm-021', 'm014', 'c002', '2025-06-08 14:20:00', '待审批', '喜欢音乐，会弹钢琴', '', NULL, '是'),
('cm-022', 'm015', 'c004', '2025-06-08 16:45:00', '待审批', '对法律文书写作感兴趣', '', NULL, '是');

-- 插入审批申请数据
INSERT INTO ApprovalRequests (RequestID, ApplicantID, RequestType, RequestTime, Status, RelatedID, ApprovalTime, Comments) VALUES
-- 已批准的申请
('req-001', 'm001', '入会', '2024-09-20 14:30:00', '已批', 'c001', '2024-09-20 15:00:00', '申请理由充分，同意加入'),
('req-002', 'm002', '入会', '2024-09-25 16:20:00', '已批', 'c001', '2024-09-25 16:30:00', '技术基础不错，欢迎加入'),
('req-003', 'm003', '入会', '2024-10-08 15:20:00', '已批', 'c002', '2024-10-08 15:30:00', '音乐素养良好，同意加入'),
('req-004', 'm005', '入会', '2024-09-25 17:30:00', '已批', 'c003', '2024-09-25 17:45:00', '篮球技术不错，欢迎加入'),
('req-005', 'p001', '活动申请', '2025-04-10 09:00:00', '已批', 'a001', '2025-04-10 10:00:00', '讲座内容有意义，同意举办'),
('req-006', 'p002', '活动申请', '2025-04-15 14:00:00', '已批', 'a002', '2025-04-15 15:00:00', '音乐会准备充分，同意举办'),
('req-007', 'p003', '活动申请', '2025-04-20 10:00:00', '已批', 'a003', '2025-04-20 11:00:00', '友谊赛有助于新生融入，同意举办'),

-- 已拒绝的申请
('req-008', 'm011', '入会', '2024-11-20 16:30:00', '已拒', 'c002', '2024-11-20 17:00:00', '音乐基础不足，建议先学习基础知识'),
('req-009', 'p002', '活动申请', '2025-05-15 11:00:00', '已拒', 'a012', '2025-05-15 12:00:00', '场地冲突，建议调整时间'),

-- 待审批的申请
('req-010', 'm013', '入会', '2025-06-08 10:30:00', '待批', 'c001', NULL, NULL),
('req-011', 'm014', '入会', '2025-06-08 14:20:00', '待批', 'c002', NULL, NULL),
('req-012', 'm015', '入会', '2025-06-08 16:45:00', '待批', 'c004', NULL, NULL),
('req-013', 'p002', '活动申请', '2025-06-07 09:30:00', '待批', 'a007', NULL, NULL),
('req-014', 'p003', '活动申请', '2025-06-07 11:15:00', '待批', 'a008', NULL, NULL),
('req-015', 'p004', '活动申请', '2025-06-07 13:20:00', '待批', 'a009', NULL, NULL);
