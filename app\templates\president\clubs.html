{% extends "admin_base.html" %}

{% block title %}我的社团 - 会长中心{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-1">
            <i class="fas fa-layer-group me-2"></i>我的社团
        </h4>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('president.dashboard') }}">会长中心</a></li>
                <li class="breadcrumb-item active">我的社团</li>
            </ol>
        </nav>
    </div>
</div>

<!-- 社团列表 -->
<div class="row">
    {% if clubs %}
        {% for club in clubs %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white">
                    <div class="d-flex align-items-center">
                        <div class="club-avatar me-3">
                            {{ club.ClubName[0] }}
                        </div>
                        <div>
                            <h6 class="mb-1">{{ club.ClubName }}</h6>
                            <small class="text-muted">{{ club.Category or '未分类' }}</small>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">{{ club.Description or '暂无描述' }}</p>
                    
                    <!-- 统计信息 -->
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <div class="fw-bold text-primary">{{ club.CurrentMembers or 0 }}</div>
                            <small class="text-muted">成员</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold text-success">{{ club.MaxMembers or '∞' }}</div>
                            <small class="text-muted">容量</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold text-info">
                                {% if club.MaxMembers and club.CurrentMembers %}
                                    {{ "%.0f"|format((club.CurrentMembers / club.MaxMembers * 100)) }}%
                                {% else %}
                                    --
                                {% endif %}
                            </div>
                            <small class="text-muted">满员率</small>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('president.club_detail', club_id=club.ClubID) }}" 
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>查看详情
                        </a>
                        <div class="btn-group" role="group">
                            <a href="{{ url_for('president.club_members', club_id=club.ClubID) }}" 
                               class="btn btn-outline-success btn-sm">
                                <i class="fas fa-users me-1"></i>成员管理
                            </a>
                            <a href="{{ url_for('president.club_activities', club_id=club.ClubID) }}" 
                               class="btn btn-outline-info btn-sm">
                                <i class="fas fa-calendar me-1"></i>活动管理
                            </a>
                        </div>
                        <a href="{{ url_for('president.edit_club', club_id=club.ClubID) }}" 
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-edit me-1"></i>编辑社团
                        </a>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar-alt me-1"></i>
                            成立于 {{ club.EstablishmentDate.strftime('%Y年%m月') if club.EstablishmentDate else '未知' }}
                        </small>
                        {% if club.Website %}
                        <a href="{{ club.Website }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
    <!-- 空状态 -->
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-5">
                <i class="fas fa-layer-group text-muted" style="font-size: 4rem;"></i>
                <h5 class="mt-3 text-muted">暂无管理的社团</h5>
                <p class="text-muted">您目前还没有担任任何社团的会长。</p>
                <a href="{{ url_for('main.clubs') }}" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>浏览所有社团
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 快速操作面板 -->
{% if clubs %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="{{ url_for('president.statistics') }}" class="btn btn-outline-primary">
                                <i class="fas fa-chart-bar me-2"></i>统计报告
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-success" onclick="showPendingApplications()">
                                <i class="fas fa-user-plus me-2"></i>待审批申请
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-info" onclick="showUpcomingActivities()">
                                <i class="fas fa-calendar-check me-2"></i>即将开始的活动
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="{{ url_for('president.dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-tachometer-alt me-2"></i>返回仪表板
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.club-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function showPendingApplications() {
    alert('待审批申请功能将在后续版本中实现，请前往各社团的成员管理页面查看。');
}

function showUpcomingActivities() {
    alert('即将开始的活动功能将在活动管理模块中实现。');
}
</script>
{% endblock %}
