#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
审批控制器
处理各类审批申请的业务逻辑
严格基于ApprovalRequests表的数据结构
"""

from app import db
from app.models.approval import ApprovalRequest
from app.models.member import Member
from app.models.club import Club
from app.models.activity import Activity
from app.utils.helpers import generate_uuid, get_china_time
from datetime import datetime, timedelta

class ApprovalController:
    """审批控制器类"""
    
    @staticmethod
    def get_approval_requests(request_type=None, status=None, page=1, per_page=20):
        """
        获取审批申请列表
        
        Args:
            request_type: 申请类型过滤
            status: 状态过滤
            page: 页码
            per_page: 每页数量
        
        Returns:
            dict: 申请列表和分页信息
        """
        query = ApprovalRequest.query
        
        if request_type:
            query = query.filter_by(RequestType=request_type)
        
        if status:
            query = query.filter_by(Status=status)
        
        pagination = query.order_by(ApprovalRequest.RequestTime.asc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return {
            'approvals': pagination.items,
            'pagination': pagination,
            'total': pagination.total
        }
    
    @staticmethod
    def create_club_application(applicant_id, club_data, application_reason):
        """
        创建社团成立申请
        
        Args:
            applicant_id: 申请人ID
            club_data: 社团数据
            application_reason: 申请理由
        
        Returns:
            tuple: (success: bool, message: str, request_id: str)
        """
        # 验证申请人存在
        applicant = Member.query.get(applicant_id)
        if not applicant:
            return False, '申请人不存在', None
        
        # 验证必填字段
        required_fields = ['club_name', 'description', 'max_members']
        for field in required_fields:
            if not club_data.get(field):
                return False, f'{field}不能为空', None
        
        # 验证社团名称唯一性
        existing_club = Club.query.filter_by(ClubName=club_data['club_name']).first()
        if existing_club:
            return False, '社团名称已存在', None
        
        # 验证社团类别
        valid_descriptions = ['学术', '体育', '艺术', '公益', '娱乐', '其他']
        if club_data['description'] not in valid_descriptions:
            return False, '社团类别无效', None
        
        # 验证最大成员数
        try:
            max_members = int(club_data['max_members'])
            if max_members <= 0:
                return False, '最大成员数必须大于0'
        except ValueError:
            return False, '最大成员数必须为数字'
        
        try:
            # 先创建社团记录（状态为休眠，等待审批）
            club = Club(
                ClubID=generate_uuid(),
                ClubName=club_data['club_name'],
                Description=club_data['description'],
                MaxMembers=max_members,
                CurrentMembers=0,
                PresidentID=applicant_id,
                Website=club_data.get('website') or None,
                FoundationDate=datetime.now().date(),
                Category=club_data.get('category') or '0',
                Status='休眠'  # 等待审批
            )
            
            db.session.add(club)
            db.session.flush()  # 获取ClubID
            
            # 创建审批申请
            approval_request = ApprovalRequest(
                RequestID=generate_uuid(),
                ApplicantID=applicant_id,
                RequestType='其他',  # 社团成立申请归类为其他
                RequestTime=get_china_time(),
                Status='待批',
                RelatedID=club.ClubID,
                Comments=f"社团成立申请：{application_reason}"
            )
            
            db.session.add(approval_request)
            db.session.commit()
            
            return True, f'社团"{club.ClubName}"成立申请已提交，等待管理员审批', approval_request.RequestID
            
        except Exception as e:
            db.session.rollback()
            return False, f'提交申请失败：{str(e)}', None
    
    @staticmethod
    def approve_club_application(request_id, approver_id, comments=None):
        """
        批准社团成立申请
        
        Args:
            request_id: 申请ID
            approver_id: 审批人ID
            comments: 审批意见
        
        Returns:
            tuple: (success: bool, message: str)
        """
        approval_request = ApprovalRequest.query.get(request_id)
        if not approval_request:
            return False, '申请不存在'
        
        if approval_request.Status != '待批':
            return False, '该申请已被处理'
        
        # 获取关联的社团
        club = Club.query.get(approval_request.RelatedID)
        if not club:
            return False, '关联社团不存在'
        
        try:
            # 批准申请
            approval_request.approve(approver_id, comments)
            
            # 激活社团
            club.Status = '活跃'
            
            # 自动将申请人加入社团
            from app.models.member_club import MemberClub
            member_club = MemberClub(
                RecordID=generate_uuid(),
                MemberID=approval_request.ApplicantID,
                ClubID=club.ClubID,
                ApplyTime=get_china_time(),
                Status='已批准',
                ApplicationReason='社团创始人',
                ApprovalId=approver_id,
                ApprovalTime=get_china_time(),
                Rejoinable='是'
            )
            
            db.session.add(member_club)
            
            # 更新社团成员数
            club.update_current_members()
            
            db.session.commit()
            
            return True, f'社团"{club.ClubName}"成立申请已批准'
            
        except Exception as e:
            db.session.rollback()
            return False, f'批准申请失败：{str(e)}'
    
    @staticmethod
    def reject_club_application(request_id, approver_id, comments=None):
        """
        拒绝社团成立申请
        
        Args:
            request_id: 申请ID
            approver_id: 审批人ID
            comments: 拒绝理由
        
        Returns:
            tuple: (success: bool, message: str)
        """
        approval_request = ApprovalRequest.query.get(request_id)
        if not approval_request:
            return False, '申请不存在'
        
        if approval_request.Status != '待批':
            return False, '该申请已被处理'
        
        # 获取关联的社团
        club = Club.query.get(approval_request.RelatedID)
        if not club:
            return False, '关联社团不存在'
        
        try:
            # 拒绝申请
            approval_request.reject(approver_id, comments)
            
            # 删除社团记录
            db.session.delete(club)
            
            db.session.commit()
            
            return True, f'社团"{club.ClubName}"成立申请已拒绝'
            
        except Exception as e:
            db.session.rollback()
            return False, f'拒绝申请失败：{str(e)}'
    
    @staticmethod
    def get_pending_requests_by_type(request_type):
        """
        按类型获取待审批申请
        
        Args:
            request_type: 申请类型
        
        Returns:
            list: 申请列表
        """
        return ApprovalRequest.query.filter_by(
            RequestType=request_type,
            Status='待批'
        ).order_by(ApprovalRequest.RequestTime.asc()).all()
    
    @staticmethod
    def get_overdue_requests(timeout_days=7):
        """
        获取超时的申请
        
        Args:
            timeout_days: 超时天数
        
        Returns:
            list: 超时申请列表
        """
        timeout_date = datetime.now() - timedelta(days=timeout_days)
        
        return ApprovalRequest.query.filter(
            ApprovalRequest.Status == '待批',
            ApprovalRequest.RequestTime < timeout_date
        ).order_by(ApprovalRequest.RequestTime.asc()).all()
    
    @staticmethod
    def get_user_requests(user_id, status=None):
        """
        获取用户的申请列表
        
        Args:
            user_id: 用户ID
            status: 状态过滤
        
        Returns:
            list: 申请列表
        """
        query = ApprovalRequest.query.filter_by(ApplicantID=user_id)
        
        if status:
            query = query.filter_by(Status=status)
        
        return query.order_by(ApprovalRequest.RequestTime.desc()).all()
    
    @staticmethod
    def cancel_request(request_id, user_id):
        """
        取消申请（仅限申请人且状态为待批）
        
        Args:
            request_id: 申请ID
            user_id: 用户ID
        
        Returns:
            tuple: (success: bool, message: str)
        """
        approval_request = ApprovalRequest.query.get(request_id)
        if not approval_request:
            return False, '申请不存在'
        
        if approval_request.ApplicantID != user_id:
            return False, '您没有权限取消该申请'
        
        if approval_request.Status != '待批':
            return False, '该申请已被处理，无法取消'
        
        try:
            # 如果是社团成立申请，需要删除关联的社团记录
            if approval_request.RequestType == '其他' and approval_request.RelatedID:
                club = Club.query.get(approval_request.RelatedID)
                if club and club.Status == '休眠':
                    db.session.delete(club)
            
            # 删除申请记录
            db.session.delete(approval_request)
            db.session.commit()
            
            return True, '申请已取消'
            
        except Exception as e:
            db.session.rollback()
            return False, f'取消申请失败：{str(e)}'
    
    @staticmethod
    def get_approval_statistics():
        """
        获取审批统计数据
        
        Returns:
            dict: 统计数据
        """
        from sqlalchemy import func
        
        # 按类型统计
        type_stats = db.session.query(
            ApprovalRequest.RequestType,
            ApprovalRequest.Status,
            func.count(ApprovalRequest.RequestID)
        ).group_by(
            ApprovalRequest.RequestType,
            ApprovalRequest.Status
        ).all()
        
        # 按月统计
        monthly_stats = db.session.query(
            func.date_format(ApprovalRequest.RequestTime, '%Y-%m').label('month'),
            func.count(ApprovalRequest.RequestID)
        ).group_by('month').order_by('month').all()
        
        # 处理时间统计
        processed_requests = ApprovalRequest.query.filter(
            ApprovalRequest.Status.in_(['已批', '已拒']),
            ApprovalRequest.ApprovalTime.isnot(None)
        ).all()
        
        processing_times = []
        for req in processed_requests:
            if req.RequestTime and req.ApprovalTime:
                duration = (req.ApprovalTime - req.RequestTime).days
                processing_times.append(duration)
        
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        return {
            'type_stats': type_stats,
            'monthly_stats': monthly_stats,
            'avg_processing_time': avg_processing_time,
            'total_requests': ApprovalRequest.query.count(),
            'pending_requests': ApprovalRequest.query.filter_by(Status='待批').count()
        }
