#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型包初始化文件
严格按照database.sql中定义的6张数据表创建对应的SQLAlchemy模型类
"""

# 导入所有数据模型，确保Flask-Migrate能够发现所有表
from .member import Member
from .club import Club
from .venue import Venue
from .activity import Activity
from .member_club import MemberClub
from .approval import ApprovalRequest

# 导出所有模型类
__all__ = [
    'Member',
    'Club',
    'Venue',
    'Activity',
    'MemberClub',
    'ApprovalRequest'
]
