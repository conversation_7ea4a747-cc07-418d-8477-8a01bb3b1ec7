{% extends "base.html" %}

{% block title %}首页 - 学校社团管理系统{% endblock %}

{% block content %}
<!-- 英雄区域 -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">发现你的兴趣社团</h1>
                <p class="lead mb-4">加入我们的校园社团，结识志同道合的朋友，丰富你的大学生活！</p>
                {% if not session.user_id %}
                    <div class="d-flex gap-3">
                        <a href="{{ url_for('auth.register') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-user-plus me-2"></i>立即注册
                        </a>
                        <a href="{{ url_for('common.clubs') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-search me-2"></i>浏览社团
                        </a>
                    </div>
                {% else %}
                    <div class="d-flex gap-3">
                        {% if session.role == '会员' %}
                            <a href="{{ url_for('member.clubs') }}" class="btn btn-light btn-lg">
                                <i class="fas fa-search me-2"></i>申请社团
                            </a>
                            <a href="{{ url_for('member.my_clubs') }}" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-layer-group me-2"></i>我的社团
                            </a>
                        {% else %}
                            <a href="{{ url_for('common.clubs') }}" class="btn btn-light btn-lg">
                                <i class="fas fa-search me-2"></i>浏览社团
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-users" style="font-size: 12rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 统计数据 -->
<section class="stats-section py-5 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <i class="fas fa-layer-group text-primary" style="font-size: 3rem;"></i>
                        <h3 class="mt-3 mb-1">{{ stats.total_clubs }}</h3>
                        <p class="text-muted">活跃社团</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <i class="fas fa-calendar-alt text-success" style="font-size: 3rem;"></i>
                        <h3 class="mt-3 mb-1">{{ stats.total_activities }}</h3>
                        <p class="text-muted">进行中活动</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <i class="fas fa-building text-warning" style="font-size: 3rem;"></i>
                        <h3 class="mt-3 mb-1">{{ stats.total_venues }}</h3>
                        <p class="text-muted">可用场馆</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <i class="fas fa-users text-info" style="font-size: 3rem;"></i>
                        <h3 class="mt-3 mb-1">{{ stats.total_members }}</h3>
                        <p class="text-muted">注册会员</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 热门社团 -->
<section class="clubs-section py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold mb-3">热门社团</h2>
                <p class="lead text-muted">发现最受欢迎的校园社团</p>
            </div>
        </div>
        
        <div class="row">
            {% for club in recent_clubs %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shadow-sm border-0">
                    <div class="card-header bg-{{ loop.cycle('primary', 'success', 'warning', 'info', 'secondary', 'danger') }} text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-layer-group me-2"></i>{{ club.ClubName }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="badge bg-light text-dark">{{ club.Description }}</span>
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>{{ club.CurrentMembers }}/{{ club.MaxMembers or '∞' }}
                            </small>
                        </div>
                        <p class="card-text">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>成立于 {{ club.FoundationDate.strftime('%Y年%m月') }}
                            </small>
                        </p>
                        <p class="card-text">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>会长：{{ club.president.Name if club.president else '未知' }}
                            </small>
                        </p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('common.club_detail', club_id=club.ClubID) }}"
                               class="btn btn-outline-primary btn-sm flex-fill">
                                <i class="fas fa-eye me-1"></i>查看详情
                            </a>
                            {% if session.user_id and session.role == '会员' %}
                            <button type="button" class="btn btn-primary btn-sm flex-fill"
                                    onclick="showQuickApplyModal('{{ club.ClubID }}', '{{ club.ClubName }}')">
                                <i class="fas fa-plus-circle me-1"></i>快速申请
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-4">
            <a href="{{ url_for('common.clubs') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-layer-group me-2"></i>查看所有社团
            </a>
        </div>
    </div>
</section>

<!-- 即将举行的活动 -->
{% if upcoming_activities %}
<section class="activities-section py-5 bg-light">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold mb-3">即将举行的活动</h2>
                <p class="lead text-muted">不要错过精彩的校园活动</p>
            </div>
        </div>
        
        <div class="row">
            {% for activity in upcoming_activities %}
            <div class="col-lg-6 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title">{{ activity.ActivityName }}</h5>
                            <span class="badge bg-{{ loop.cycle('primary', 'success', 'warning', 'info', 'secondary') }}">
                                {{ activity.ActivityType }}
                            </span>
                        </div>
                        <p class="card-text text-muted">
                            <i class="fas fa-layer-group me-2"></i>{{ activity.club.ClubName if activity.club else '未知社团' }}
                        </p>
                        <p class="card-text">
                            <i class="fas fa-calendar me-2"></i>{{ activity.StartTime.strftime('%Y年%m月%d日 %H:%M') }}
                        </p>
                        <p class="card-text">
                            <i class="fas fa-map-marker-alt me-2"></i>{{ activity.venue.VenueName if activity.venue else '待定' }}
                        </p>
                        {% if activity.ParticipantLimit %}
                        <p class="card-text">
                            <i class="fas fa-users me-2"></i>限{{ activity.ParticipantLimit }}人
                        </p>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}"
                               class="btn btn-outline-primary btn-sm flex-fill">
                                <i class="fas fa-eye me-1"></i>查看详情
                            </a>
                            {% if session.user_id and session.role == '会员' and activity.Status == '计划中' %}
                            <button type="button" class="btn btn-primary btn-sm flex-fill"
                                    onclick="quickJoinActivity('{{ activity.ActivityID }}', '{{ activity.ActivityName }}')">
                                <i class="fas fa-plus me-1"></i>快速报名
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-4">
            <a href="{{ url_for('common.activities') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-calendar-alt me-2"></i>查看所有活动
            </a>
        </div>
    </div>
</section>
{% endif %}

<!-- 功能介绍 -->
<section class="features-section py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold mb-3">平台特色</h2>
                <p class="lead text-muted">为校园社团提供全方位的数字化管理服务</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="text-center">
                    <div class="feature-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-search" style="font-size: 2rem;"></i>
                    </div>
                    <h4>便捷搜索</h4>
                    <p class="text-muted">快速找到感兴趣的社团和活动，支持多种筛选条件</p>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="text-center">
                    <div class="feature-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-check-circle" style="font-size: 2rem;"></i>
                    </div>
                    <h4>在线申请</h4>
                    <p class="text-muted">一键申请加入社团，实时跟踪申请状态</p>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="text-center">
                    <div class="feature-icon bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-calendar-alt" style="font-size: 2rem;"></i>
                    </div>
                    <h4>活动管理</h4>
                    <p class="text-muted">参与丰富多彩的社团活动，不错过任何精彩时刻</p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

<!-- 快速申请社团模态框 -->
<div class="modal fade" id="quickApplyModal" tabindex="-1" aria-labelledby="quickApplyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickApplyModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>快速申请加入社团
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <form id="quickApplyForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="mb-3">
                        <label for="clubName" class="form-label">申请社团</label>
                        <input type="text" class="form-control" id="clubName" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="applicationReason" class="form-label">申请理由</label>
                        <textarea class="form-control" id="applicationReason" name="application_reason"
                                  rows="4" placeholder="请简要说明您申请加入该社团的理由..." maxlength="200"></textarea>
                        <div class="form-text">最多200个字符</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i>提交申请
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
// 显示快速申请模态框
function showQuickApplyModal(clubId, clubName) {
    document.getElementById('clubName').value = clubName;
    document.getElementById('quickApplyForm').action = `/member/apply_club/${clubId}`;
    document.getElementById('applicationReason').value = '';

    const modal = new bootstrap.Modal(document.getElementById('quickApplyModal'));
    modal.show();
}

// 快速报名活动
function quickJoinActivity(activityId, activityName) {
    if (confirm(`确定要报名参加"${activityName}"吗？`)) {
        // 显示加载状态
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>报名中...';
        button.disabled = true;

        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/member/join_activity/${activityId}`;

        // 添加CSRF令牌
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}

// 页面加载完成后的初始化
$(document).ready(function() {
    // 添加卡片悬停效果
    $('.card').hover(
        function() {
            $(this).addClass('shadow-lg').removeClass('shadow-sm');
        },
        function() {
            $(this).addClass('shadow-sm').removeClass('shadow-lg');
        }
    );

    // 申请理由字符计数
    $('#applicationReason').on('input', function() {
        const maxLength = 200;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;

        $(this).siblings('.form-text').text(`还可输入${remaining}个字符`);

        if (remaining < 0) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // 表单提交验证
    $('#quickApplyForm').on('submit', function(e) {
        const reason = $('#applicationReason').val().trim();
        if (!reason) {
            e.preventDefault();
            alert('请填写申请理由');
            $('#applicationReason').focus();
            return false;
        }

        if (reason.length > 200) {
            e.preventDefault();
            alert('申请理由不能超过200个字符');
            $('#applicationReason').focus();
            return false;
        }

        // 显示提交状态
        $(this).find('button[type="submit"]').html('<i class="fas fa-spinner fa-spin me-1"></i>提交中...').prop('disabled', true);
    });
});
</script>
{% endblock %}
