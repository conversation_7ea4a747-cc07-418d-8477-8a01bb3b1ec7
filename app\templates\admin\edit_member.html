{% extends "admin_base.html" %}

{% block title %}编辑用户 - 管理后台{% endblock %}
{% block page_title %}编辑用户{% endblock %}
{% block title_icon %}<i class="fas fa-user-edit"></i>{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-nav">
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.dashboard') }}">
                <i class="fas fa-home"></i>
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.members') }}">用户管理</a>
        </li>
        <li class="breadcrumb-item active">编辑用户</li>
    </ol>
</nav>
{% endblock %}

{% block page_actions %}
<div class="d-flex gap-2">
    <a href="{{ url_for('admin.members') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回列表
    </a>
    <button type="button" class="btn btn-outline-danger" onclick="deleteMember()">
        <i class="fas fa-trash me-1"></i>删除用户
    </button>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧：用户信息编辑 -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2 text-primary"></i>编辑用户信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.edit_member', member_id=member.MemberID) }}" id="editMemberForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <!-- 账户信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-key me-1"></i>账户信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username"
                                   value="{{ member.Username }}" required maxlength="20">
                            <div class="form-text">用户名用于登录，可以修改</div>
                        </div>
                        <div class="col-md-6">
                            <label for="role" class="form-label">用户角色 <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="会员" {% if member.Role == '会员' %}selected{% endif %}>会员</option>
                                <option value="会长" {% if member.Role == '会长' %}selected{% endif %}>会长</option>
                                <option value="管理员" {% if member.Role == '管理员' %}selected{% endif %}>管理员</option>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="留空则不修改密码" maxlength="30">
                            <div class="form-text">如需修改密码请输入新密码，否则留空</div>
                        </div>
                    </div>

                    <!-- 个人信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-user me-1"></i>个人信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="name" class="form-label">真实姓名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ member.Name or '' }}" required maxlength="50">
                        </div>
                        <div class="col-md-6">
                            <label for="gender" class="form-label">性别</label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="">请选择</option>
                                <option value="男" {% if member.Gender == '男' %}selected{% endif %}>男</option>
                                <option value="女" {% if member.Gender == '女' %}selected{% endif %}>女</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="age" class="form-label">年龄</label>
                            <input type="number" class="form-control" id="age" name="age" 
                                   value="{{ member.Age or '' }}" min="16" max="100">
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">手机号码</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ member.Phone or '' }}" pattern="[0-9]{11}"
                                   title="请输入11位手机号码">
                        </div>

                    </div>

                    <!-- 学校信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-graduation-cap me-1"></i>学校信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="college" class="form-label">学院</label>
                            <input type="text" class="form-control" id="college" name="college" 
                                   value="{{ member.College or '' }}" maxlength="100">
                        </div>
                        <div class="col-md-6">
                            <label for="specialty" class="form-label">专业</label>
                            <input type="text" class="form-control" id="specialty" name="specialty" 
                                   value="{{ member.Specialty or '' }}" maxlength="100">
                        </div>
                        <div class="col-md-6">
                            <label for="dormitory" class="form-label">宿舍</label>
                            <input type="text" class="form-control" id="dormitory" name="dormitory" 
                                   value="{{ member.Dormitory or '' }}" maxlength="50">
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('admin.members') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="reset" class="btn btn-outline-warning">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>


    </div>

    <!-- 右侧：用户统计和操作 -->
    <div class="col-lg-4">
        <!-- 用户头像和基本信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body text-center">
                <div class="user-avatar-xl mb-3">
                    {{ member.Name[0] if member.Name else member.Username[0] }}
                </div>
                <h5 class="mb-1">{{ member.Name or member.Username }}</h5>
                <p class="text-muted mb-2">@{{ member.Username }}</p>
                <span class="badge bg-{{ 'danger' if member.Role == '管理员' else 'warning' if member.Role == '会长' else 'primary' }} mb-3">
                    {{ member.Role }}
                </span>
                

            </div>
        </div>




    </div>
</div>

<style>
.user-avatar-xl {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 2rem;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {

    
    // 手机号格式验证
    $('#phone').on('input', function() {
        const phone = $(this).val();
        const phoneRegex = /^1[3-9]\d{9}$/;
        
        if (phone && !phoneRegex.test(phone)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">请输入正确的手机号码</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

});

// 切换密码显示
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '-icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}




</script>
{% endblock %}
