{% extends "admin_base.html" %}

{% block title %}{{ club.ClubName }} - 活动管理 - 管理后台{% endblock %}
{% block page_title %}{{ club.ClubName }} - 活动管理{% endblock %}
{% block title_icon %}<i class="fas fa-calendar-alt"></i>{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-nav">
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.dashboard') }}">
                <i class="fas fa-home"></i>
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.clubs') }}">社团管理</a>
        </li>
        <li class="breadcrumb-item active">{{ club.ClubName }} - 活动管理</li>
    </ol>
</nav>
{% endblock %}

{% block page_actions %}
<div class="d-flex gap-2">
    <a href="{{ url_for('admin.clubs') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回社团列表
    </a>
    <a href="{{ url_for('admin.edit_club', club_id=club.ClubID) }}" class="btn btn-outline-primary">
        <i class="fas fa-edit me-1"></i>编辑社团
    </a>
</div>
{% endblock %}

{% block content %}
<!-- 社团信息概览 -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-2">{{ club.ClubName }}</h5>
                <p class="text-muted mb-2">{{ club.Description }} | 会长：{{ club.president.Name if club.president else '未知' }}</p>
                <div class="d-flex gap-3">
                    <span class="badge bg-{{ 'success' if club.Status == '活跃' else 'warning' if club.Status == '暂停' else 'danger' }}">
                        {{ club.Status }}
                    </span>
                    <small class="text-muted">成员：{{ club.CurrentMembers }}/{{ club.MaxMembers or '∞' }}</small>
                    <small class="text-muted">创建时间：{{ club.FoundationDate.strftime('%Y-%m-%d') if club.FoundationDate else '未知' }}</small>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ url_for('common.club_detail', club_id=club.ClubID) }}" 
                   class="btn btn-outline-info" target="_blank">
                    <i class="fas fa-eye me-1"></i>查看详情
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="status" class="form-label">活动状态</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" {% if current_status == 'all' %}selected{% endif %}>全部状态</option>
                    <option value="计划中" {% if current_status == '计划中' %}selected{% endif %}>计划中</option>
                    <option value="进行中" {% if current_status == '进行中' %}selected{% endif %}>进行中</option>
                    <option value="已完成" {% if current_status == '已完成' %}selected{% endif %}>已完成</option>
                    <option value="已取消" {% if current_status == '已取消' %}selected{% endif %}>已取消</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>筛选
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 活动列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="fas fa-calendar-alt me-2"></i>活动列表
            <span class="badge bg-primary ms-2">{{ pagination.total }}个</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if activities %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="25%">活动信息</th>
                        <th width="15%">时间</th>
                        <th width="15%">地点</th>
                        <th width="10%">状态</th>
                        <th width="15%">参与人数</th>
                        <th width="20%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for activity in activities %}
                    <tr>
                        <td>
                            <div>
                                <h6 class="mb-1">{{ activity.ActivityName }}</h6>
                                <small class="text-muted">{{ activity.Description[:50] }}{% if activity.Description|length > 50 %}...{% endif %}</small>
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                {% if activity.StartTime %}
                                <div><i class="fas fa-calendar me-1"></i>{{ activity.StartTime.strftime('%Y-%m-%d') }}</div>
                                <div><i class="fas fa-clock me-1"></i>{{ activity.StartTime.strftime('%H:%M') }}</div>
                                {% else %}
                                <span class="text-muted">时间待定</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <small class="text-muted">
                                {% if activity.Location %}
                                    <i class="fas fa-map-marker-alt me-1"></i>{{ activity.Location }}
                                {% else %}
                                    地点待定
                                {% endif %}
                            </small>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'primary' if activity.Status == '计划中' else 'success' if activity.Status == '进行中' else 'secondary' if activity.Status == '已完成' else 'danger' }}">
                                {{ activity.Status }}
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                {% if activity.MaxParticipants %}
                                    {{ activity.CurrentParticipants or 0 }}/{{ activity.MaxParticipants }}
                                {% else %}
                                    {{ activity.CurrentParticipants or 0 }}/无限制
                                {% endif %}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}" 
                                   class="btn btn-outline-info" target="_blank"
                                   data-bs-toggle="tooltip" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('admin.edit_activity', activity_id=activity.ActivityID) }}" 
                                   class="btn btn-outline-primary" 
                                   data-bs-toggle="tooltip" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if activity.Status in ['计划中', '进行中'] %}
                                <button type="button" class="btn btn-outline-warning" 
                                        onclick="cancelActivity('{{ activity.ActivityID }}', '{{ activity.ActivityName }}')"
                                        data-bs-toggle="tooltip" title="取消活动">
                                    <i class="fas fa-ban"></i>
                                </button>
                                {% endif %}
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteActivity('{{ activity.ActivityID }}', '{{ activity.ActivityName }}')"
                                        data-bs-toggle="tooltip" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-alt text-muted" style="font-size: 4rem;"></i>
            <h5 class="mt-3 text-muted">暂无活动数据</h5>
            <p class="text-muted">该社团暂时没有举办活动</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<nav aria-label="活动列表分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.club_activities', club_id=club.ClubID, page=pagination.prev_num, **request.args) }}">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        {% endif %}
        
        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.club_activities', club_id=club.ClubID, page=page_num, **request.args) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.club_activities', club_id=club.ClubID, page=pagination.next_num, **request.args) }}">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// 取消活动
function cancelActivity(activityId, activityName) {
    if (confirm(`确定要取消活动"${activityName}"吗？`)) {
        // 这里可以添加取消活动的AJAX请求
        alert('取消活动功能开发中...');
    }
}

// 删除活动
function deleteActivity(activityId, activityName) {
    if (confirm(`确定要删除活动"${activityName}"吗？此操作不可恢复。`)) {
        // 这里可以添加删除活动的AJAX请求
        alert('删除活动功能开发中...');
    }
}

// 初始化工具提示
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
