{% extends "base.html" %}

{% block title %}个人资料 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="bi bi-person-circle me-2 text-primary"></i>个人资料
            </h2>
            <p class="text-muted">查看和管理您的个人信息</p>
        </div>
    </div>

    <div class="row">
        <!-- 个人信息卡片 -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">用户名</label>
                            <div class="form-control-plaintext">{{ user.Username }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">姓名</label>
                            <div class="form-control-plaintext">{{ user.Name }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">角色</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-primary">{{ user.Role }}</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">邮箱</label>
                            <div class="form-control-plaintext">{{ user.Email or '未设置' }}</div>
                        </div>
                        {% if user.Role == '会员' %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">学院</label>
                            <div class="form-control-plaintext">{{ user.College or '未设置' }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">专业</label>
                            <div class="form-control-plaintext">{{ user.Specialty or '未设置' }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">年级</label>
                            <div class="form-control-plaintext">{{ user.Grade or '未设置' }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">联系电话</label>
                            <div class="form-control-plaintext">{{ user.ContactInfo or '未设置' }}</div>
                        </div>
                        {% endif %}
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">注册时间</label>
                            <div class="form-control-plaintext">
                                {{ user.RegistrationTime.strftime('%Y年%m月%d日 %H:%M') if user.RegistrationTime else '未知' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作面板 -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="bi bi-gear me-2"></i>账户操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary">
                            <i class="bi bi-key me-2"></i>修改密码
                        </a>
                        {% if user.Role == '管理员' %}
                        <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-success">
                            <i class="bi bi-speedometer2 me-2"></i>管理后台
                        </a>
                        {% elif user.Role == '会长' %}
                        <a href="{{ url_for('president.dashboard') }}" class="btn btn-outline-success">
                            <i class="bi bi-people me-2"></i>社团管理
                        </a>
                        {% else %}
                        <a href="{{ url_for('member.dashboard') }}" class="btn btn-outline-success">
                            <i class="bi bi-person me-2"></i>个人中心
                        </a>
                        {% endif %}
                        <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-danger">
                            <i class="bi bi-box-arrow-right me-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>

            <!-- 账户统计 -->
            {% if user.Role == '会员' %}
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="bi bi-bar-chart me-2"></i>我的统计
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">0</h4>
                                <small class="text-muted">加入社团</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success mb-1">0</h4>
                            <small class="text-muted">参与活动</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 返回按钮 -->
    <div class="row">
        <div class="col-12">
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>返回
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 可以在这里添加一些交互功能
    console.log('个人资料页面加载完成');
});
</script>
{% endblock %}
