{% extends "admin_base.html" %}

{% block title %}审批管理 - 管理后台{% endblock %}
{% block page_title %}审批管理{% endblock %}
{% block title_icon %}<i class="fas fa-check-circle"></i>{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-nav">
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.dashboard') }}">
                <i class="fas fa-home"></i>
            </a>
        </li>
        <li class="breadcrumb-item active">审批管理</li>
    </ol>
</nav>
{% endblock %}

{% block page_actions %}
<div class="d-flex justify-content-between align-items-center mt-3">
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-success" onclick="batchApprove()">
            <i class="fas fa-check me-1"></i>批量通过
        </button>
        <button type="button" class="btn btn-danger" onclick="batchReject()">
            <i class="fas fa-times me-1"></i>批量拒绝
        </button>
        <button type="button" class="btn btn-outline-secondary" onclick="exportApprovals()">
            <i class="fas fa-download me-1"></i>导出数据
        </button>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="h4 mb-0 text-warning">{{ stats.pending }}</div>
                        <div class="text-muted small">待审批</div>
                    </div>
                    <div class="text-warning">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="h4 mb-0 text-success">{{ stats.approved }}</div>
                        <div class="text-muted small">已通过</div>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="h4 mb-0 text-danger">{{ stats.rejected }}</div>
                        <div class="text-muted small">已拒绝</div>
                    </div>
                    <div class="text-danger">
                        <i class="fas fa-times fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="h4 mb-0 text-primary">{{ stats.total }}</div>
                        <div class="text-muted small">总申请数</div>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">搜索申请</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="输入申请人或内容" value="{{ request.args.get('search', '') }}">
                </div>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">状态筛选</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" {% if current_status == 'all' %}selected{% endif %}>全部状态</option>
                    <option value="待批" {% if current_status == '待批' %}selected{% endif %}>待审批</option>
                    <option value="已批" {% if current_status == '已批' %}selected{% endif %}>已通过</option>
                    <option value="已拒" {% if current_status == '已拒' %}selected{% endif %}>已拒绝</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="request_type" class="form-label">类型筛选</label>
                <select class="form-select" id="request_type" name="request_type">
                    <option value="all" {% if current_type == 'all' %}selected{% endif %}>全部类型</option>
                    <option value="入会" {% if current_type == '入会' %}selected{% endif %}>入会申请</option>
                    <option value="退会" {% if current_type == '退会' %}selected{% endif %}>退会申请</option>
                    <option value="活动申请" {% if current_type == '活动申请' %}selected{% endif %}>活动申请</option>
                    <option value="其他" {% if current_type == '其他' %}selected{% endif %}>其他</option>
                </select>
            </div>

            <div class="col-md-2">
                <label for="date_range" class="form-label">时间范围</label>
                <select class="form-select" id="date_range" name="date_range">
                    <option value="all" {% if current_date_range == 'all' %}selected{% endif %}>全部时间</option>
                    <option value="today" {% if current_date_range == 'today' %}selected{% endif %}>今天</option>
                    <option value="week" {% if current_date_range == 'week' %}selected{% endif %}>本周</option>
                    <option value="month" {% if current_date_range == 'month' %}selected{% endif %}>本月</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 审批列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="fas fa-check-circle me-2"></i>审批列表
            <span class="badge bg-primary ms-2">{{ pagination.total }}个</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if approvals %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="5%">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th width="20%">申请人</th>
                        <th width="15%">申请类型</th>
                        <th width="30%">申请内容</th>
                        <th width="15%">申请时间</th>
                        <th width="10%">状态</th>
                        <th width="10%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for approval in approvals %}
                    <tr class="{% if approval.Status == '待批' %}table-warning{% endif %}">
                        <td>
                            <input type="checkbox" class="form-check-input approval-checkbox" 
                                   value="{{ approval.RequestID }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    {{ approval.applicant.Name[0] if approval.applicant and approval.applicant.Name else 'U' }}
                                </div>
                                <div>
                                    <div class="fw-bold">{{ approval.applicant.Name if approval.applicant else '未知用户' }}</div>
                                    <div class="text-muted small">@{{ approval.applicant.Username if approval.applicant else '未知' }}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'primary' if approval.RequestType == '社团成立' else 'success' if approval.RequestType == '活动申请' else 'warning' if approval.RequestType == '会长任命' else 'danger' if approval.RequestType == '社团解散' else 'secondary' }}">
                                {{ approval.RequestType }}
                            </span>
                        </td>
                        <td>
                            <div class="small">
                                {% if approval.Comments %}
                                {{ approval.Comments[:80] }}{% if approval.Comments|length > 80 %}...{% endif %}
                                {% else %}
                                <span class="text-muted">无详细内容</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-warning">
                                普通
                            </span>
                        </td>
                        <td>
                            <span class="text-muted small">
                                {{ approval.RequestTime.strftime('%m-%d %H:%M') if approval.RequestTime else '未知' }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'warning' if approval.Status == '待批' else 'success' if approval.Status == '已批准' else 'danger' }}">
                                {{ approval.Status }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="viewApproval('{{ approval.RequestID }}')"
                                        data-bs-toggle="tooltip" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% if approval.Status == '待批' %}
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="approveRequest('{{ approval.RequestID }}')"
                                        data-bs-toggle="tooltip" title="通过">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="rejectRequest('{{ approval.RequestID }}')"
                                        data-bs-toggle="tooltip" title="拒绝">
                                    <i class="fas fa-times"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-check-circle text-muted" style="font-size: 4rem;"></i>
            <h5 class="mt-3 text-muted">暂无审批申请</h5>
            <p class="text-muted">所有申请都已处理完毕</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<nav aria-label="审批列表分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.approvals', page=pagination.prev_num, search=request.args.get('search', ''), status=request.args.get('status', 'all'), request_type=request.args.get('request_type', 'all'), date_range=request.args.get('date_range', 'all')) }}">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        {% endif %}

        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.approvals', page=page_num, search=request.args.get('search', ''), status=request.args.get('status', 'all'), request_type=request.args.get('request_type', 'all'), date_range=request.args.get('date_range', 'all')) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}

        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.approvals', page=pagination.next_num, search=request.args.get('search', ''), status=request.args.get('status', 'all'), request_type=request.args.get('request_type', 'all'), date_range=request.args.get('date_range', 'all')) }}">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- 批量操作 -->
<div class="card border-0 shadow-sm mt-4" id="batchActions" style="display: none;">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <span class="text-muted">已选择 <span id="selectedCount">0</span> 个申请</span>
            <div class="btn-group">
                <button type="button" class="btn btn-success" onclick="batchApprove()">
                    <i class="fas fa-check me-1"></i>批量通过
                </button>
                <button type="button" class="btn btn-danger" onclick="batchReject()">
                    <i class="fas fa-times me-1"></i>批量拒绝
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="exportSelected()">
                    <i class="fas fa-download me-1"></i>导出选中
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 审批详情模态框 -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">审批详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="approvalModalBody">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" id="modalApproveBtn">通过</button>
                <button type="button" class="btn btn-danger" id="modalRejectBtn">拒绝</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 全选/取消全选
    $('#selectAll').change(function() {
        $('.approval-checkbox').prop('checked', this.checked);
        updateBatchActions();
    });
    
    // 单个选择
    $('.approval-checkbox').change(function() {
        updateBatchActions();
        
        // 更新全选状态
        var total = $('.approval-checkbox').length;
        var checked = $('.approval-checkbox:checked').length;
        $('#selectAll').prop('indeterminate', checked > 0 && checked < total);
        $('#selectAll').prop('checked', checked === total);
    });
    
    // 更新批量操作显示
    function updateBatchActions() {
        var selectedCount = $('.approval-checkbox:checked').length;
        $('#selectedCount').text(selectedCount);
        
        if (selectedCount > 0) {
            $('#batchActions').show();
        } else {
            $('#batchActions').hide();
        }
    }
});

// 查看审批详情
function viewApproval(requestId) {
    fetch(`/admin/approvals/${requestId}/detail`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $('#approvalModalBody').html(data.html);
                $('#modalApproveBtn').attr('onclick', `approveRequest('${requestId}')`);
                $('#modalRejectBtn').attr('onclick', `rejectRequest('${requestId}')`);
                
                // 如果已处理，隐藏操作按钮
                if (data.approval.Status !== '待批') {
                    $('#modalApproveBtn, #modalRejectBtn').hide();
                } else {
                    $('#modalApproveBtn, #modalRejectBtn').show();
                }
                
                $('#approvalModal').modal('show');
            } else {
                alert('获取详情失败：' + data.message);
            }
        })
        .catch(error => {
            alert('获取详情失败：' + error.message);
        });
}

// 通过申请
function approveRequest(requestId) {
    var reason = prompt('请输入通过理由（可选）：');

    // 创建表单数据
    var formData = new FormData();
    formData.append('csrf_token', '{{ csrf_token() }}');
    if (reason) {
        formData.append('comments', reason);
    }

    fetch(`/admin/approvals/${requestId}/approve`, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('操作失败');
        }
    })
    .catch(error => {
        alert('操作失败：' + error.message);
    });
}

// 拒绝申请
function rejectRequest(requestId) {
    var reason = prompt('请输入拒绝理由：');
    if (!reason) {
        alert('拒绝申请必须提供理由');
        return;
    }

    // 创建表单数据
    var formData = new FormData();
    formData.append('csrf_token', '{{ csrf_token() }}');
    formData.append('comments', reason);

    fetch(`/admin/approvals/${requestId}/reject`, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('操作失败');
        }
    })
    .catch(error => {
        alert('操作失败：' + error.message);
    });
}

// 批量通过
function batchApprove() {
    var selectedIds = $('.approval-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要通过的申请');
        return;
    }
    
    var reason = prompt('请输入批量通过理由（可选）：');
    
    if (confirm(`确定要通过选中的 ${selectedIds.length} 个申请吗？`)) {
        fetch('/admin/approvals/batch-approve', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                request_ids: selectedIds,
                reason: reason
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('批量通过失败：' + data.message);
            }
        })
        .catch(error => {
            alert('批量通过失败：' + error.message);
        });
    }
}

// 批量拒绝
function batchReject() {
    var selectedIds = $('.approval-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要拒绝的申请');
        return;
    }
    
    var reason = prompt('请输入批量拒绝理由：');
    if (!reason) {
        alert('批量拒绝必须提供理由');
        return;
    }
    
    if (confirm(`确定要拒绝选中的 ${selectedIds.length} 个申请吗？`)) {
        fetch('/admin/approvals/batch-reject', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                request_ids: selectedIds,
                reason: reason
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('批量拒绝失败：' + data.message);
            }
        })
        .catch(error => {
            alert('批量拒绝失败：' + error.message);
        });
    }
}

// 导出数据
function exportApprovals() {
    window.open('/admin/approvals/export', '_blank');
}

// 导出选中
function exportSelected() {
    var selectedIds = $('.approval-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要导出的申请');
        return;
    }
    
    var form = document.createElement('form');
    form.method = 'POST';
    form.action = '/admin/approvals/export-selected';
    
    var csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '{{ csrf_token() }}';
    form.appendChild(csrfInput);
    
    var idsInput = document.createElement('input');
    idsInput.type = 'hidden';
    idsInput.name = 'request_ids';
    idsInput.value = JSON.stringify(selectedIds);
    form.appendChild(idsInput);
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
</script>
{% endblock %}
