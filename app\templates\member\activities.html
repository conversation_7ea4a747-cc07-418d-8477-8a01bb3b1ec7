{% extends "base.html" %}

{% block title %}我的活动 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="fas fa-calendar-alt me-2 text-primary"></i>我的活动
            </h2>
            <p class="text-muted">管理您参与的所有活动</p>
        </div>
    </div>

    <!-- 活动统计 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.upcoming }}</h4>
                    <p class="text-muted mb-0">即将参加</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-play fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.ongoing }}</h4>
                    <p class="text-muted mb-0">正在进行</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-check fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.completed }}</h4>
                    <p class="text-muted mb-0">已完成</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-star fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.total }}</h4>
                    <p class="text-muted mb-0">总参与数</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">搜索活动</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search" name="search" 
                               placeholder="输入活动名称" value="{{ request.args.get('search', '') }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">状态筛选</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {% if current_status == 'all' %}selected{% endif %}>全部状态</option>
                        <option value="计划中" {% if current_status == '计划中' %}selected{% endif %}>即将参加</option>
                        <option value="进行中" {% if current_status == '进行中' %}selected{% endif %}>正在进行</option>
                        <option value="已完成" {% if current_status == '已完成' %}selected{% endif %}>已完成</option>
                        <option value="已取消" {% if current_status == '已取消' %}selected{% endif %}>已取消</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="club_id" class="form-label">社团筛选</label>
                    <select class="form-select" id="club_id" name="club_id">
                        <option value="all" {% if current_club == 'all' %}selected{% endif %}>全部社团</option>
                        {% for club in my_clubs %}
                        <option value="{{ club.ClubID }}" {% if current_club == club.ClubID %}selected{% endif %}>
                            {{ club.ClubName }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_range" class="form-label">时间范围</label>
                    <select class="form-select" id="date_range" name="date_range">
                        <option value="all" {% if current_date_range == 'all' %}selected{% endif %}>全部时间</option>
                        <option value="week" {% if current_date_range == 'week' %}selected{% endif %}>本周</option>
                        <option value="month" {% if current_date_range == 'month' %}selected{% endif %}>本月</option>
                        <option value="quarter" {% if current_date_range == 'quarter' %}selected{% endif %}>本季度</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 活动列表 -->
    <div class="row">
        {% if activities %}
        {% for activity in activities %}
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-{{ 'warning' if activity.Status == '计划中' else 'primary' if activity.Status == '进行中' else 'success' if activity.Status == '已完成' else 'danger' }} text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">{{ activity.ActivityName }}</h6>
                        <span class="badge bg-light text-dark">{{ activity.Status }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-12">
                            <p class="text-muted mb-2">
                                <i class="fas fa-layer-group me-2"></i>{{ activity.club.ClubName if activity.club else '未知社团' }}
                            </p>
                            <p class="text-muted mb-2">
                                <i class="fas fa-calendar me-2"></i>
                                {% if activity.StartTime %}
                                {{ activity.StartTime.strftime('%Y年%m月%d日 %H:%M') }}
                                {% if activity.EndTime %}
                                - {{ activity.EndTime.strftime('%H:%M') }}
                                {% endif %}
                                {% else %}
                                时间待定
                                {% endif %}
                            </p>
                            <p class="text-muted mb-2">
                                <i class="fas fa-map-marker-alt me-2"></i>{{ activity.venue.VenueName if activity.venue else '地点待定' }}
                            </p>
                            {% if activity.ActivityType %}
                            <p class="text-muted mb-2">
                                <i class="fas fa-tag me-2"></i>{{ activity.ActivityType }}
                            </p>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if activity.Description %}
                    <p class="card-text">
                        {{ activity.Description[:100] }}{% if activity.Description|length > 100 %}...{% endif %}
                    </p>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if activity.ParticipantLimit %}
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>限{{ activity.ParticipantLimit }}人
                            </small>
                            {% endif %}
                            {% if activity.ActualParticipant %}
                            <small class="text-muted ms-2">
                                <i class="fas fa-user-check me-1"></i>实际{{ activity.ActualParticipant }}人
                            </small>
                            {% endif %}
                        </div>
                        <div class="btn-group btn-group-sm">
                            <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}" 
                               class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>详情
                            </a>
                            {% if activity.Status == '计划中' %}
                            <button type="button" class="btn btn-outline-danger" 
                                    onclick="cancelParticipation('{{ activity.ActivityID }}', '{{ activity.ActivityName }}')">
                                <i class="fas fa-times me-1"></i>取消参加
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- 活动进度条（仅对进行中的活动显示） -->
                {% if activity.Status == '进行中' and activity.StartTime and activity.EndTime %}
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">活动进度</small>
                        <small class="text-muted">{{ activity.progress }}%</small>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-primary" style="width: {{ activity.progress }}%"></div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
        {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-calendar-alt text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">暂无活动记录</h4>
                <p class="text-muted">您还没有参加任何活动，去发现一些有趣的活动吧！</p>
                <a href="{{ url_for('common.activities') }}" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>浏览活动
                </a>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 分页 -->
    {% if pagination and pagination.pages > 1 %}
    <nav aria-label="活动列表分页" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('member.activities', page=pagination.prev_num, **request.args) }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            {% endif %}
            
            {% for page_num in pagination.iter_pages() %}
                {% if page_num %}
                    {% if page_num != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('member.activities', page=page_num, **request.args) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('member.activities', page=pagination.next_num, **request.args) }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    <!-- 快速操作 -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-body">
            <h5 class="card-title">
                <i class="fas fa-bolt me-2 text-success"></i>快速操作
            </h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <a href="{{ url_for('common.activities') }}" class="btn btn-outline-primary w-100">
                        <i class="fas fa-search me-2"></i>浏览更多活动
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('member.clubs') }}" class="btn btn-outline-success w-100">
                        <i class="fas fa-layer-group me-2"></i>我的社团
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('member.applications') }}" class="btn btn-outline-warning w-100">
                        <i class="fas fa-file-alt me-2"></i>我的申请
                    </a>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-outline-secondary w-100" onclick="exportActivities()">
                        <i class="fas fa-download me-2"></i>导出记录
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 取消参加活动
function cancelParticipation(activityId, activityName) {
    if (confirm(`确定要取消参加活动"${activityName}"吗？`)) {
        fetch(`/member/activities/${activityId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('已成功取消参加该活动');
                location.reload();
            } else {
                alert('取消失败：' + data.message);
            }
        })
        .catch(error => {
            alert('取消失败：' + error.message);
        });
    }
}

// 导出活动记录
function exportActivities() {
    const params = new URLSearchParams(window.location.search);
    window.open(`/member/activities/export?${params.toString()}`, '_blank');
}

// 自动刷新即将开始的活动状态
setInterval(function() {
    const upcomingCards = document.querySelectorAll('.card-header.bg-warning');
    if (upcomingCards.length > 0) {
        // 检查是否有活动状态需要更新
        fetch('/member/api/activity-status-check')
            .then(response => response.json())
            .then(data => {
                if (data.updated) {
                    // 如果有状态更新，刷新页面
                    location.reload();
                }
            })
            .catch(error => {
                console.error('检查活动状态失败:', error);
            });
    }
}, 60000); // 每分钟检查一次
</script>
{% endblock %}
