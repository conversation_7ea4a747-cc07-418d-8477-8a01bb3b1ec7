{% extends "admin_base.html" %}

{% block title %}创建社团 - 管理后台{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- 简单导航 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">创建社团</h5>
                    <a href="{{ url_for('admin.clubs') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                </div>
            </div>
        </div>

        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.create_club') }}" id="createClubForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                    <!-- 基本信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <label for="club_name" class="form-label">社团名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="club_name" name="club_name"
                                   value="{{ request.form.get('club_name', '') }}" required maxlength="100">
                        </div>
                        <div class="col-md-6">
                            <label for="description" class="form-label">社团类别 <span class="text-danger">*</span></label>
                            <select class="form-select" id="description" name="description" required>
                                <option value="">请选择类别</option>
                                <option value="学术" {% if request.form.get('description') == '学术' %}selected{% endif %}>学术</option>
                                <option value="体育" {% if request.form.get('description') == '体育' %}selected{% endif %}>体育</option>
                                <option value="艺术" {% if request.form.get('description') == '艺术' %}selected{% endif %}>艺术</option>
                                <option value="公益" {% if request.form.get('description') == '公益' %}selected{% endif %}>公益</option>
                                <option value="娱乐" {% if request.form.get('description') == '娱乐' %}selected{% endif %}>娱乐</option>
                                <option value="其他" {% if request.form.get('description') == '其他' %}selected{% endif %}>其他</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="foundation_date" class="form-label">成立时间 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="foundation_date" name="foundation_date"
                                   value="{{ request.form.get('foundation_date', today) }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">社团状态 <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="活跃" {% if request.form.get('status', '活跃') == '活跃' %}selected{% endif %}>活跃</option>
                                <option value="休眠" {% if request.form.get('status') == '休眠' %}selected{% endif %}>休眠</option>
                                <option value="解散" {% if request.form.get('status') == '解散' %}selected{% endif %}>解散</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="website" class="form-label">官方网站</label>
                            <input type="url" class="form-control" id="website" name="website"
                                   value="{{ request.form.get('website', '') }}" placeholder="https://" maxlength="200">
                            <div class="form-text">可选，社团官方网站地址</div>
                        </div>
                    </div>

                    <!-- 会长和成员设置 -->
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <label for="president_id" class="form-label">指定会长 <span class="text-danger">*</span></label>
                            <select class="form-select" id="president_id" name="president_id" required>
                                <option value="">请选择会长</option>
                                {% for member in available_presidents %}
                                <option value="{{ member.MemberID }}" {% if request.form.get('president_id') == member.MemberID %}selected{% endif %}>
                                    {{ member.Name or member.Username }} (@{{ member.Username }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="max_members" class="form-label">最大成员数</label>
                            <input type="number" class="form-control" id="max_members" name="max_members"
                                   value="{{ request.form.get('max_members', '') }}" min="1" max="1000">
                            <div class="form-text">留空表示不限制成员数量</div>
                        </div>
                    </div>



                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('admin.clubs') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="reset" class="btn btn-outline-warning">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>创建社团
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 设置默认成立日期为今天
    const today = new Date().toISOString().split('T')[0];
    if (!$('#foundation_date').val()) {
        $('#foundation_date').val(today);
    }

    // 社团名称唯一性检查
    let nameTimeout;
    $('#club_name').on('input', function() {
        const clubName = $(this).val();
        if (clubName.length >= 2) {
            clearTimeout(nameTimeout);
            nameTimeout = setTimeout(function() {
                checkClubNameAvailability(clubName);
            }, 500);
        }
    });

    // 网站URL验证
    $('#website').on('input', function() {
        const url = $(this).val();
        const urlRegex = /^https?:\/\/.+/;

        if (url && !urlRegex.test(url)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">请输入正确的网站地址（以http://或https://开头）</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // 表单提交验证
    $('#createClubForm').on('submit', function(e) {
        const invalidFields = $(this).find('.is-invalid');
        if (invalidFields.length > 0) {
            e.preventDefault();
            alert('请先修正表单中的错误');
            invalidFields.first().focus();
        }
    });
});

// 检查社团名称可用性
function checkClubNameAvailability(clubName) {
    fetch('/admin/api/check-club-name', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({club_name: clubName})
    })
    .then(response => response.json())
    .then(data => {
        const nameField = $('#club_name');
        if (data.available) {
            nameField.removeClass('is-invalid').addClass('is-valid');
            nameField.next('.invalid-feedback').remove();
            if (!nameField.next('.valid-feedback').length) {
                nameField.after('<div class="valid-feedback">社团名称可用</div>');
            }
        } else {
            nameField.removeClass('is-valid').addClass('is-invalid');
            nameField.next('.valid-feedback').remove();
            if (!nameField.next('.invalid-feedback').length) {
                nameField.after('<div class="invalid-feedback">社团名称已存在</div>');
            }
        }
    })
    .catch(error => {
        console.error('检查社团名称失败:', error);
    });
}
</script>
{% endblock %}
