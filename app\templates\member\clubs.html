{% extends "base.html" %}

{% block title %}浏览社团 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-2">
                        <i class="fas fa-search me-2 text-primary"></i>浏览社团
                    </h2>
                    <p class="text-muted mb-0">发现感兴趣的社团，申请加入开始你的社团生活</p>
                </div>
                <div>
                    <a href="{{ url_for('member.my_clubs') }}" class="btn btn-outline-primary">
                        <i class="fas fa-layer-group me-2"></i>我的社团
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和过滤器 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">搜索社团</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="输入社团名称或关键词" value="{{ search_query }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="category" class="form-label">社团类别</label>
                            <select class="form-select" id="category" name="category">
                                <option value="all" {% if category == 'all' %}selected{% endif %}>全部类别</option>
                                {% for cat in categories %}
                                <option value="{{ cat }}" {% if category == cat %}selected{% endif %}>{{ cat }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 社团列表 -->
    <div class="row">
        {% for club in clubs %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shadow-sm border-0 club-card">
                <div class="card-header bg-{{ loop.cycle('primary', 'success', 'warning', 'info', 'secondary', 'danger') }} text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-layer-group me-2"></i>{{ club.ClubName }}
                        </h5>
                        <span class="badge bg-light text-dark">{{ club.Description }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted d-block">成员数量</small>
                            <strong>{{ club.CurrentMembers }}/{{ club.MaxMembers or '∞' }}</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">成立时间</small>
                            <strong>{{ club.FoundationDate.strftime('%Y年%m月') if club.FoundationDate else '未知' }}</strong>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted d-block">会长</small>
                        <div class="d-flex align-items-center">
                            <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2" 
                                 style="width: 24px; height: 24px; font-size: 0.8rem;">
                                {{ club.president.Name[0] if club.president and club.president.Name else '?' }}
                            </div>
                            <span>{{ club.president.Name if club.president else '未知' }}</span>
                        </div>
                    </div>
                    
                    {% if club.Website %}
                    <div class="mb-3">
                        <small class="text-muted d-block">官方网站</small>
                        <a href="{{ club.Website }}" target="_blank" class="text-decoration-none">
                            <i class="fas fa-external-link-alt me-1"></i>访问网站
                        </a>
                    </div>
                    {% endif %}
                    
                    <!-- 成员数进度条 -->
                    {% if club.MaxMembers %}
                    <div class="mb-3">
                        <small class="text-muted d-block">成员数进度</small>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-{{ loop.cycle('primary', 'success', 'warning', 'info') }}" 
                                 style="width: {{ (club.CurrentMembers / club.MaxMembers * 100) if club.MaxMembers > 0 else 0 }}%"></div>
                        </div>
                        <small class="text-muted">
                            {{ '%.1f'|format((club.CurrentMembers / club.MaxMembers * 100) if club.MaxMembers > 0 else 0) }}% 已满
                        </small>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ url_for('member.club_detail', club_id=club.ClubID) }}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>查看详情
                        </a>
                        {% if club.can_accept_new_member() %}
                            <a href="{{ url_for('member.club_detail', club_id=club.ClubID) }}" 
                               class="btn btn-primary btn-sm">
                                <i class="fas fa-plus-circle me-1"></i>申请加入
                            </a>
                        {% else %}
                            <button class="btn btn-secondary btn-sm" disabled>
                                <i class="fas fa-ban me-1"></i>已满员
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-search text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">没有找到符合条件的社团</h4>
                <p class="text-muted">尝试调整搜索条件或浏览其他类别的社团</p>
                <a href="{{ url_for('member.clubs') }}" class="btn btn-primary">
                    <i class="fas fa-refresh me-2"></i>查看全部社团
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 分页 -->
    {% if pagination.pages > 1 %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="社团列表分页">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('member.clubs', page=pagination.prev_num, search=search_query, category=category) }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('member.clubs', page=page_num, search=search_query, category=category) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('member.clubs', page=pagination.next_num, search=search_query, category=category) }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - 
                    {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page < pagination.total else pagination.total }} 
                    个社团，共 {{ pagination.total }} 个
                </small>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 推荐社团侧边栏（移动端隐藏） -->
    <div class="row mt-5 d-none d-lg-block">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2 text-warning"></i>为您推荐
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="text-primary">热门社团</h6>
                            <p class="small text-muted">基于成员数量和活跃度推荐</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-success">新成立社团</h6>
                            <p class="small text-muted">最近成立的新兴社团</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-info">专业相关</h6>
                            <p class="small text-muted">与您的专长技能相关的社团</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 搜索框自动提交
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            $('#search').closest('form').submit();
        }, 1000);
    });
    
    // 类别选择自动提交
    $('#category').on('change', function() {
        $(this).closest('form').submit();
    });
    
    // 社团卡片悬停效果
    $('.club-card').hover(
        function() {
            $(this).addClass('shadow-lg').removeClass('shadow-sm');
        },
        function() {
            $(this).addClass('shadow-sm').removeClass('shadow-lg');
        }
    );
});
</script>
{% endblock %}
