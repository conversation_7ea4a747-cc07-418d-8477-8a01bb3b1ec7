{% extends "base.html" %}

{% block title %}社团列表 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="fas fa-layer-group me-2 text-primary"></i>社团列表
            </h2>
            <p class="text-muted">发现感兴趣的社团，开启你的校园生活</p>
        </div>
    </div>

    <!-- 社团列表 -->
    <div class="row">
        {% for club in clubs %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-header bg-{{ loop.cycle('primary', 'success', 'warning', 'info', 'secondary', 'danger') }} text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-layer-group me-2"></i>{{ club.ClubName }}
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        <strong>类别：</strong>{{ club.Description }}
                    </p>
                    <p class="card-text">
                        <small class="text-muted">
                            <i class="fas fa-users me-1"></i>{{ club.CurrentMembers }}/{{ club.MaxMembers or '∞' }}
                        </small>
                    </p>
                    <p class="card-text">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>成立于 {{ club.FoundationDate.strftime('%Y年%m月') if club.FoundationDate else '未知' }}
                        </small>
                    </p>
                    <p class="card-text">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>会长：{{ club.president.Name if club.president else '未知' }}
                        </small>
                    </p>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url_for('common.club_detail', club_id=club.ClubID) }}" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>查看详情
                    </a>
                    {% if session.user_id and session.role == '会员' %}
                        <a href="{{ url_for('member.club_detail', club_id=club.ClubID) }}" 
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-plus-circle me-1"></i>申请加入
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-layer-group text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">暂无活跃社团</h4>
                <p class="text-muted">请稍后再来查看</p>
            </div>
        </div>
        {% endfor %}
    </div>


</div>
{% endblock %}
