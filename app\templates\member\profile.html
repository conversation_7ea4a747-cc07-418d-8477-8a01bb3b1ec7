{% extends "base.html" %}

{% block title %}个人资料 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="fas fa-user me-2 text-primary"></i>个人资料
            </h2>
            <p class="text-muted">管理您的个人信息和账户设置</p>
        </div>
    </div>

    <div class="row">
        <!-- 左侧个人信息 -->
        <div class="col-lg-8">
            <!-- 基本信息 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card me-2 text-primary"></i>基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('member.update_profile') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">真实姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ member.Name or '' }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ member.Username }}" readonly>
                                <div class="form-text">用户名不可修改</div>
                            </div>
                            <div class="col-md-6">
                                <label for="gender" class="form-label">性别</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">请选择</option>
                                    <option value="男" {% if member.Gender == '男' %}selected{% endif %}>男</option>
                                    <option value="女" {% if member.Gender == '女' %}selected{% endif %}>女</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="age" class="form-label">年龄</label>
                                <input type="number" class="form-control" id="age" name="age" 
                                       value="{{ member.Age or '' }}" min="16" max="100">
                            </div>
                            <div class="col-md-6">
                                <label for="phone" class="form-label">手机号码</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ member.Phone or '' }}" pattern="[0-9]{11}">
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">邮箱地址</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ member.Email or '' }}">
                            </div>
                            <div class="col-md-6">
                                <label for="college" class="form-label">学院</label>
                                <input type="text" class="form-control" id="college" name="college" 
                                       value="{{ member.College or '' }}">
                            </div>
                            <div class="col-md-6">
                                <label for="specialty" class="form-label">专业</label>
                                <input type="text" class="form-control" id="specialty" name="specialty" 
                                       value="{{ member.Specialty or '' }}">
                            </div>
                            <div class="col-md-6">
                                <label for="dormitory" class="form-label">宿舍</label>
                                <input type="text" class="form-control" id="dormitory" name="dormitory" 
                                       value="{{ member.Dormitory or '' }}">
                            </div>
                            <div class="col-md-6">
                                <label for="role" class="form-label">角色</label>
                                <input type="text" class="form-control" id="role" name="role" 
                                       value="{{ member.Role }}" readonly>
                                <div class="form-text">角色由管理员分配</div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存修改
                            </button>
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 修改密码 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-key me-2 text-warning"></i>修改密码
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('member.change_password') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <div class="row g-3">
                            <div class="col-12">
                                <label for="current_password" class="form-label">当前密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                            <div class="col-md-6">
                                <label for="new_password" class="form-label">新密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="new_password" name="new_password" 
                                       minlength="6" required>
                                <div class="form-text">密码长度至少6位</div>
                            </div>
                            <div class="col-md-6">
                                <label for="confirm_password" class="form-label">确认新密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       minlength="6" required>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key me-1"></i>修改密码
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 右侧信息 -->
        <div class="col-lg-4">
            <!-- 头像和基本信息 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body text-center">
                    <div class="user-avatar-xl mb-3">
                        {{ member.Name[0] if member.Name else member.Username[0] }}
                    </div>
                    <h5 class="mb-1">{{ member.Name or member.Username }}</h5>
                    <p class="text-muted mb-2">@{{ member.Username }}</p>
                    <span class="badge bg-{{ 'danger' if member.Role == '管理员' else 'warning' if member.Role == '会长' else 'primary' }} mb-3">
                        {{ member.Role }}
                    </span>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="fw-bold">{{ member_stats.joined_clubs }}</div>
                            <small class="text-muted">加入社团</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold">{{ member_stats.participated_activities }}</div>
                            <small class="text-muted">参与活动</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold">{{ member_stats.activity_score }}</div>
                            <small class="text-muted">活跃度</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 账户信息 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-info"></i>账户信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">用户ID</small>
                        <div class="fw-bold">{{ member.MemberID[:8] }}...</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">注册时间</small>
                        <div class="fw-bold">{{ member.created_at.strftime('%Y-%m-%d') if member.created_at else '未知' }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">最后登录</small>
                        <div class="fw-bold">{{ member.last_login.strftime('%Y-%m-%d %H:%M') if member.last_login else '未知' }}</div>
                    </div>
                    <div class="mb-0">
                        <small class="text-muted">账户状态</small>
                        <div>
                            <span class="badge bg-success">正常</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的社团 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-layer-group me-2 text-success"></i>我的社团
                    </h6>
                </div>
                <div class="card-body">
                    {% if my_clubs %}
                    {% for membership in my_clubs %}
                    <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                        <div class="club-avatar-sm me-3">
                            {{ membership.club.ClubName[0] }}
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ membership.club.ClubName }}</div>
                            <small class="text-muted">{{ membership.Role or '会员' }}</small>
                        </div>
                    </div>
                    {% endfor %}
                    <div class="text-center">
                        <a href="{{ url_for('member.clubs') }}" class="btn btn-sm btn-outline-primary">
                            查看全部
                        </a>
                    </div>
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-layer-group text-muted" style="font-size: 2rem;"></i>
                        <p class="mt-2 text-muted mb-2">暂未加入社团</p>
                        <a href="{{ url_for('common.clubs') }}" class="btn btn-sm btn-primary">
                            浏览社团
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2 text-warning"></i>快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('member.dashboard') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-tachometer-alt me-1"></i>返回首页
                        </a>
                        <a href="{{ url_for('member.activities') }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-calendar-alt me-1"></i>我的活动
                        </a>
                        <a href="{{ url_for('member.applications') }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-file-alt me-1"></i>我的申请
                        </a>
                        <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-sign-out-alt me-1"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.user-avatar-xl {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 2.5rem;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.club-avatar-sm {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 密码确认验证
    $('#confirm_password').on('input', function() {
        const newPassword = $('#new_password').val();
        const confirmPassword = $(this).val();
        
        if (confirmPassword && newPassword !== confirmPassword) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">两次输入的密码不一致</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
    
    // 手机号格式验证
    $('#phone').on('input', function() {
        const phone = $(this).val();
        const phoneRegex = /^1[3-9]\d{9}$/;
        
        if (phone && !phoneRegex.test(phone)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">请输入正确的手机号码</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
    
    // 邮箱格式验证
    $('#email').on('input', function() {
        const email = $(this).val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">请输入正确的邮箱地址</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
    
    // 表单提交验证
    $('form').on('submit', function(e) {
        const invalidFields = $(this).find('.is-invalid');
        if (invalidFields.length > 0) {
            e.preventDefault();
            alert('请先修正表单中的错误');
            invalidFields.first().focus();
        }
    });
});

// 头像点击上传功能（预留）
$('.user-avatar-xl').on('click', function() {
    // 这里可以添加头像上传功能
    alert('头像上传功能开发中...');
});
</script>
{% endblock %}
