{% extends "admin_base.html" %}

{% block title %}申请审批 - 会长后台{% endblock %}

{% block breadcrumb_title %}申请审批{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">成员申请审批</h2>
            <p class="text-muted mb-0">处理待审批的入会申请</p>
        </div>
        <div>
            <a href="{{ url_for('president.members') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>返回成员管理
            </a>
        </div>
    </div>

    {% if applications %}
    <!-- 申请列表 -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2 text-warning"></i>待审批申请 ({{ applications|length }})
                </h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-success" onclick="batchApprove()">
                        <i class="fas fa-check me-1"></i>批量批准
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="batchReject()">
                        <i class="fas fa-times me-1"></i>批量拒绝
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>申请人信息</th>
                            <th>申请社团</th>
                            <th>申请时间</th>
                            <th>申请理由</th>
                            <th>等待时间</th>
                            <th width="200">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for application in applications %}
                        <tr data-record-id="{{ application.RecordID }}">
                            <td>
                                <input type="checkbox" class="form-check-input application-checkbox" 
                                       value="{{ application.RecordID }}">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-3">
                                        {{ application.member.Name[0] if application.member.Name else application.member.Username[0] }}
                                    </div>
                                    <div>
                                        <div class="fw-semibold">{{ application.member.Name or application.member.Username }}</div>
                                        <small class="text-muted">{{ application.member.Username }}</small>
                                        {% if application.member.College %}
                                        <div><small class="text-muted">{{ application.member.College }}</small></div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ clubs[application.ClubID].ClubName }}</span>
                            </td>
                            <td>
                                <div>{{ application.ApplyTime.strftime('%Y-%m-%d') }}</div>
                                <small class="text-muted">{{ application.ApplyTime.strftime('%H:%M') }}</small>
                            </td>
                            <td>
                                {% if application.ApplicationReason %}
                                <div class="text-truncate" style="max-width: 200px;" 
                                     title="{{ application.ApplicationReason }}">
                                    {{ application.ApplicationReason }}
                                </div>
                                {% else %}
                                <span class="text-muted">无</span>
                                {% endif %}
                            </td>
                            <td>
                                {% set days = (moment().date() - application.ApplyTime.date()).days %}
                                <span class="badge {% if days > 7 %}bg-danger{% elif days > 3 %}bg-warning{% else %}bg-info{% endif %}">
                                    {{ days }}天
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-success" 
                                            onclick="approveApplication('{{ application.RecordID }}', '{{ application.member.Name or application.member.Username }}')"
                                            title="批准申请">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="rejectApplication('{{ application.RecordID }}', '{{ application.member.Name or application.member.Username }}')"
                                            title="拒绝申请">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-info" 
                                            onclick="viewApplicationDetail('{{ application.RecordID }}')"
                                            title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <!-- 空状态 -->
    <div class="text-center py-5">
        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
        <h4 class="mt-3 text-muted">暂无待审批申请</h4>
        <p class="text-muted">当前没有需要处理的入会申请。</p>
        <a href="{{ url_for('president.members') }}" class="btn btn-primary">
            <i class="fas fa-users me-1"></i>返回成员管理
        </a>
    </div>
    {% endif %}
</div>

<!-- 审批模态框 -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalTitle">审批申请</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="approvalModalBody">
                    <!-- 动态内容 -->
                </div>
                <div class="mb-3">
                    <label for="approvalComments" class="form-label">审批意见（可选）</label>
                    <textarea class="form-control" id="approvalComments" rows="3" 
                              placeholder="请输入审批意见..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmApprove" style="display:none;">
                    <i class="fas fa-check me-1"></i>确认批准
                </button>
                <button type="button" class="btn btn-danger" id="confirmReject" style="display:none;">
                    <i class="fas fa-times me-1"></i>确认拒绝
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentAction = '';
let currentRecordId = '';

$(document).ready(function() {
    // 全选功能
    $('#selectAll').change(function() {
        $('.application-checkbox').prop('checked', this.checked);
    });
    
    // 单选框变化时检查全选状态
    $('.application-checkbox').change(function() {
        const total = $('.application-checkbox').length;
        const checked = $('.application-checkbox:checked').length;
        $('#selectAll').prop('checked', total === checked);
    });
});

// 批准申请
function approveApplication(recordId, memberName) {
    currentAction = 'approve';
    currentRecordId = recordId;
    
    $('#approvalModalTitle').text('批准申请');
    $('#approvalModalBody').html(`
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            确定要批准 <strong>${memberName}</strong> 的入会申请吗？
        </div>
    `);
    
    $('#confirmApprove').show();
    $('#confirmReject').hide();
    $('#approvalModal').modal('show');
}

// 拒绝申请
function rejectApplication(recordId, memberName) {
    currentAction = 'reject';
    currentRecordId = recordId;
    
    $('#approvalModalTitle').text('拒绝申请');
    $('#approvalModalBody').html(`
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            确定要拒绝 <strong>${memberName}</strong> 的入会申请吗？
        </div>
    `);
    
    $('#confirmApprove').hide();
    $('#confirmReject').show();
    $('#approvalModal').modal('show');
}

// 确认批准
$('#confirmApprove').click(function() {
    processApplication('approve', currentRecordId);
});

// 确认拒绝
$('#confirmReject').click(function() {
    processApplication('reject', currentRecordId);
});

// 处理申请
function processApplication(action, recordId) {
    const comments = $('#approvalComments').val().trim();
    const url = action === 'approve' ? 
        `/president/approve_application/${recordId}` : 
        `/president/reject_application/${recordId}`;
    
    // 发送请求
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `comments=${encodeURIComponent(comments)}`
    })
    .then(response => {
        if (response.ok) {
            $('#approvalModal').modal('hide');
            location.reload();
        } else {
            alert('操作失败，请重试');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('操作失败，请重试');
    });
}

// 查看申请详情
function viewApplicationDetail(recordId) {
    alert('查看详情功能开发中');
}

// 批量批准
function batchApprove() {
    const selected = $('.application-checkbox:checked');
    if (selected.length === 0) {
        alert('请选择要批准的申请');
        return;
    }
    
    if (confirm(`确定要批准选中的 ${selected.length} 个申请吗？`)) {
        // 批量处理逻辑
        alert('批量批准功能开发中');
    }
}

// 批量拒绝
function batchReject() {
    const selected = $('.application-checkbox:checked');
    if (selected.length === 0) {
        alert('请选择要拒绝的申请');
        return;
    }
    
    if (confirm(`确定要拒绝选中的 ${selected.length} 个申请吗？`)) {
        // 批量处理逻辑
        alert('批量拒绝功能开发中');
    }
}
</script>
{% endblock %}
