#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会长视图模块
处理社团会长的页面路由和请求
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from app import db
from app.controllers.club_controller import ClubController
from app.utils.decorators import login_required, role_required, club_president_required
from app.utils.helpers import get_current_user
from app.models.club import Club
from app.models.member import Member
from app.models.member_club import MemberClub
from app.models.activity import Activity
from app.models.venue import Venue
from datetime import datetime

# 创建会长蓝图
president_bp = Blueprint('president', __name__)

@president_bp.route('/dashboard')
@login_required
@role_required('会长', '管理员')
def dashboard():
    """
    会长仪表板
    显示管理的社团、待审批申请、最近活动等
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    # 获取仪表板数据
    dashboard_data = ClubController.get_president_dashboard_data(user.MemberID)
    
    return render_template('president/dashboard.html', **dashboard_data)

@president_bp.route('/clubs')
@login_required
@role_required('会长', '管理员')
def clubs():
    """
    我管理的社团列表
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    # 获取管理的社团
    clubs = Club.query.filter_by(PresidentID=user.MemberID).all()
    
    return render_template('president/clubs.html', clubs=clubs)

@president_bp.route('/club/<club_id>')
@login_required
@club_president_required()
def club_detail(club_id):
    """
    社团详情管理页面
    """
    club = Club.query.get_or_404(club_id)
    
    # 获取社团成员统计
    members_stats = {
        'approved': ClubController.get_club_members(club_id, '已批准'),
        'pending': ClubController.get_club_members(club_id, '待审批'),
        'rejected': ClubController.get_club_members(club_id, '已拒绝'),
        'withdrawn': ClubController.get_club_members(club_id, '已退出')
    }
    
    # 获取最近活动
    recent_activities = ClubController.get_club_activities(club_id)[:10]
    
    return render_template('president/club_detail.html',
                         club=club,
                         members_stats=members_stats,
                         recent_activities=recent_activities)





@president_bp.route('/club/<club_id>/activities')
@login_required
@club_president_required()
def club_activities(club_id):
    """
    社团活动管理页面
    """
    club = Club.query.get_or_404(club_id)
    
    # 获取活动列表
    status = request.args.get('status', 'all')
    
    if status == 'all':
        activities = ClubController.get_club_activities(club_id)
    else:
        activities = ClubController.get_club_activities(club_id, status)
    
    return render_template('president/club_activities.html',
                         club=club,
                         activities=activities,
                         current_status=status)



@president_bp.route('/club/<club_id>/edit', methods=['GET', 'POST'])
@login_required
@club_president_required()
def edit_club(club_id):
    """
    编辑社团信息页面
    """
    user = get_current_user()
    club = Club.query.get_or_404(club_id)
    
    if request.method == 'POST':
        # 收集表单数据
        club_data = {
            'website': request.form.get('website', '').strip(),
            'max_members': request.form.get('max_members', '').strip(),
            'category': request.form.get('category', '').strip()
        }
        
        # 调用控制器更新社团信息
        success, message = ClubController.update_club_info(
            user.MemberID, club_id, club_data
        )
        
        if success:
            flash(message, 'success')
            return redirect(url_for('president.club_detail', club_id=club_id))
        else:
            flash(message, 'error')
    
    return render_template('president/edit_club.html', club=club)



@president_bp.route('/check_venue_availability')
@login_required
@role_required('会长', '管理员')
def check_venue_availability():
    """
    检查场馆可用性（AJAX接口）
    """
    venue_id = request.args.get('venue_id')
    start_time = request.args.get('start_time')
    end_time = request.args.get('end_time')
    activity_id = request.args.get('activity_id')  # 编辑活动时排除自己
    
    if not all([venue_id, start_time, end_time]):
        return jsonify({'available': False, 'message': '参数不完整'})
    
    try:
        from datetime import datetime
        start_dt = datetime.fromisoformat(start_time)
        end_dt = datetime.fromisoformat(end_time)
        
        venue = Venue.query.get(venue_id)
        if not venue:
            return jsonify({'available': False, 'message': '场馆不存在'})
        
        available = venue.check_availability(start_dt, end_dt, activity_id)
        
        if available:
            return jsonify({'available': True, 'message': '场馆可用'})
        else:
            conflicts = venue.get_conflicting_activities(start_dt, end_dt, activity_id)
            conflict_info = []
            for conflict in conflicts:
                conflict_info.append({
                    'name': conflict.ActivityName,
                    'start': conflict.StartTime.isoformat(),
                    'end': conflict.EndTime.isoformat(),
                    'club': conflict.club.ClubName if conflict.club else ''
                })
            
            return jsonify({
                'available': False,
                'message': '该时间段场馆已被占用',
                'conflicts': conflict_info
            })
    
    except Exception as e:
        return jsonify({'available': False, 'message': f'检查失败：{str(e)}'})

@president_bp.route('/statistics')
@login_required
@role_required('会长', '管理员')
def statistics():
    """
    社团统计页面
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))
    
    # 获取管理的社团
    clubs = Club.query.filter_by(PresidentID=user.MemberID).all()
    
    # 统计数据
    stats_data = []
    for club in clubs:
        club_stats = {
            'club': club,
            'total_members': MemberClub.query.filter_by(ClubID=club.ClubID, Status='已批准').count(),
            'pending_applications': MemberClub.query.filter_by(ClubID=club.ClubID, Status='申请中').count(),
            'total_activities': Activity.query.filter_by(ClubID=club.ClubID).count(),
            'upcoming_activities': Activity.query.filter(
                Activity.ClubID == club.ClubID,
                Activity.Status == '计划中',
                Activity.StartTime > datetime.now()
            ).count()
        }
        stats_data.append(club_stats)
    
    return render_template('president/statistics.html', stats_data=stats_data)

# ==================== 社团成员管理模块 ====================

@president_bp.route('/club/<club_id>/members')
@login_required
@club_president_required()
def club_members(club_id):
    """
    社团成员管理页面
    显示成员列表、申请审批、成员信息管理
    """
    # 获取查询参数
    status = request.args.get('status', 'all')
    search = request.args.get('search', '').strip()
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # 获取社团信息
    club = Club.query.get_or_404(club_id)

    # 获取成员列表
    result = ClubController.get_club_members_with_pagination(
        club_id=club_id,
        page=page,
        per_page=per_page,
        status=status if status != 'all' else None,
        search=search if search else None
    )

    # 获取统计数据
    stats = {
        'total': MemberClub.query.filter_by(ClubID=club_id).count(),
        'approved': MemberClub.query.filter_by(ClubID=club_id, Status='已批准').count(),
        'pending': MemberClub.query.filter_by(ClubID=club_id, Status='待审批').count(),
        'rejected': MemberClub.query.filter_by(ClubID=club_id, Status='已拒绝').count(),
        'withdrawn': MemberClub.query.filter_by(ClubID=club_id, Status='已退出').count()
    }

    return render_template('president/club_members.html',
                         club=club,
                         members=result['members'],
                         pagination=result['pagination'],
                         current_status=status,
                         search_query=search,
                         stats=stats)

@president_bp.route('/club/<club_id>/members/<record_id>/approve', methods=['POST'])
@login_required
@club_president_required()
def approve_member(club_id, record_id):
    """
    批准成员申请
    """
    user = get_current_user()
    if not user:
        return jsonify({'success': False, 'message': '用户信息获取失败'})

    comments = request.form.get('comments', '').strip()

    # 调用控制器处理批准
    success, message = ClubController.approve_member_application(
        user.MemberID, record_id, comments
    )

    if request.is_json or request.headers.get('Content-Type') == 'application/json':
        return jsonify({'success': success, 'message': message})
    else:
        flash(message, 'success' if success else 'error')
        return redirect(url_for('president.club_members', club_id=club_id))

@president_bp.route('/club/<club_id>/members/<record_id>/reject', methods=['POST'])
@login_required
@club_president_required()
def reject_member(club_id, record_id):
    """
    拒绝成员申请
    """
    user = get_current_user()
    if not user:
        return jsonify({'success': False, 'message': '用户信息获取失败'})

    comments = request.form.get('comments', '').strip()

    # 调用控制器处理拒绝
    success, message = ClubController.reject_member_application(
        user.MemberID, record_id, comments
    )

    if request.is_json or request.headers.get('Content-Type') == 'application/json':
        return jsonify({'success': success, 'message': message})
    else:
        flash(message, 'success' if success else 'error')
        return redirect(url_for('president.club_members', club_id=club_id))

@president_bp.route('/club/<club_id>/members/<record_id>/remove', methods=['POST'])
@login_required
@club_president_required()
def remove_member(club_id, record_id):
    """
    移除社团成员
    """
    user = get_current_user()
    if not user:
        return jsonify({'success': False, 'message': '用户信息获取失败'})

    reason = request.form.get('reason', '').strip()

    # 调用控制器处理移除
    success, message = ClubController.remove_member_from_club(
        user.MemberID, record_id, reason
    )

    if request.is_json or request.headers.get('Content-Type') == 'application/json':
        return jsonify({'success': success, 'message': message})
    else:
        flash(message, 'success' if success else 'error')
        return redirect(url_for('president.club_members', club_id=club_id))

@president_bp.route('/club/<club_id>/members/<record_id>/edit', methods=['GET', 'POST'])
@login_required
@club_president_required()
def edit_member(club_id, record_id):
    """
    编辑成员信息
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('president.club_members', club_id=club_id))

    # 获取成员记录
    member_club = ClubController.get_member_application_details(record_id)
    if not member_club:
        flash('成员记录不存在', 'error')
        return redirect(url_for('president.club_members', club_id=club_id))

    club = Club.query.get_or_404(club_id)

    if request.method == 'POST':
        # 收集表单数据
        member_data = {
            'specialty': request.form.get('specialty', '').strip(),
            'application_reason': request.form.get('application_reason', '').strip()
        }

        # 调用控制器更新成员信息
        success, message = ClubController.update_member_info(
            user.MemberID, record_id, member_data
        )

        flash(message, 'success' if success else 'error')
        if success:
            return redirect(url_for('president.club_members', club_id=club_id))

    return render_template('president/edit_member.html',
                         club=club,
                         member_club=member_club)

# ==================== 新的路由结构 ====================

@president_bp.route('/members')
@login_required
@role_required('会长', '管理员')
def members():
    """
    成员管理主页
    直接显示所有管理社团的成员列表
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 获取管理的社团
    clubs = Club.query.filter_by(PresidentID=user.MemberID).all()
    club_ids = [club.ClubID for club in clubs]

    if not club_ids:
        return render_template('president/members.html',
                             clubs=clubs,
                             members=[],
                             stats={})

    # 获取查询参数
    status = request.args.get('status', 'all')
    search = request.args.get('search', '').strip()
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 构建查询
    query = MemberClub.query.join(Member).filter(MemberClub.ClubID.in_(club_ids))

    # 状态筛选
    if status != 'all':
        query = query.filter(MemberClub.Status == status)

    # 搜索筛选（根据数据库结构，Members表没有StudentID字段）
    if search:
        query = query.filter(
            db.or_(
                Member.Name.contains(search),
                Member.Username.contains(search),
                Member.College.contains(search),
                Member.Specialty.contains(search)
            )
        )

    # 分页
    pagination = query.order_by(MemberClub.ApplyTime.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    members = pagination.items

    # 统计数据
    stats = {
        'total': MemberClub.query.filter(MemberClub.ClubID.in_(club_ids)).count(),
        'approved': MemberClub.query.filter(MemberClub.ClubID.in_(club_ids), MemberClub.Status == '已批准').count(),
        'pending': MemberClub.query.filter(MemberClub.ClubID.in_(club_ids), MemberClub.Status == '待审批').count(),
        'rejected': MemberClub.query.filter(MemberClub.ClubID.in_(club_ids), MemberClub.Status == '已拒绝').count(),
        'withdrawn': MemberClub.query.filter(MemberClub.ClubID.in_(club_ids), MemberClub.Status == '已退出').count()
    }

    # 创建社团字典便于模板使用
    clubs_dict = {club.ClubID: club for club in clubs}

    return render_template('president/members.html',
                         clubs=clubs,
                         clubs_dict=clubs_dict,
                         members=members,
                         pagination=pagination,
                         stats=stats,
                         current_status=status,
                         search_query=search)

@president_bp.route('/members/applications')
@login_required
@role_required('会长', '管理员')
def member_applications():
    """
    成员申请审批页面
    显示所有待审批的成员申请
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 获取管理的社团ID
    clubs = Club.query.filter_by(PresidentID=user.MemberID).all()
    club_ids = [club.ClubID for club in clubs]

    if not club_ids:
        return render_template('president/member_applications.html',
                             applications=[], clubs={})

    # 获取待审批申请
    applications = MemberClub.query.join(Member).filter(
        MemberClub.ClubID.in_(club_ids),
        MemberClub.Status == '待审批'
    ).order_by(MemberClub.ApplyTime.asc()).all()

    # 创建社团字典便于模板使用
    clubs_dict = {club.ClubID: club for club in clubs}

    return render_template('president/member_applications.html',
                         applications=applications,
                         clubs=clubs_dict)

@president_bp.route('/activities')
@login_required
@role_required('会长', '管理员')
def activities():
    """
    活动管理主页
    显示所有管理社团的活动概况
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 获取管理的社团
    clubs = Club.query.filter_by(PresidentID=user.MemberID).all()
    club_ids = [club.ClubID for club in clubs]

    # 获取活动数据
    activities_data = {}
    if club_ids:
        # 按状态分类活动
        activities_data = {
            'planned': Activity.query.filter(
                Activity.ClubID.in_(club_ids),
                Activity.Status == '计划中'
            ).order_by(Activity.StartTime.asc()).all(),
            'ongoing': Activity.query.filter(
                Activity.ClubID.in_(club_ids),
                Activity.Status == '进行中'
            ).order_by(Activity.StartTime.asc()).all(),
            'completed': Activity.query.filter(
                Activity.ClubID.in_(club_ids),
                Activity.Status == '已完成'
            ).order_by(Activity.StartTime.desc()).limit(10).all(),
            'cancelled': Activity.query.filter(
                Activity.ClubID.in_(club_ids),
                Activity.Status == '已取消'
            ).order_by(Activity.StartTime.desc()).limit(5).all()
        }

    return render_template('president/activities.html',
                         clubs=clubs,
                         activities_data=activities_data)

@president_bp.route('/registrations')
@login_required
@role_required('会长', '管理员')
def registrations():
    """
    活动报名审批页面
    显示所有活动的报名申请（暂时显示占位内容）
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 获取管理的社团
    clubs = Club.query.filter_by(PresidentID=user.MemberID).all()
    club_ids = [club.ClubID for club in clubs]

    # 获取即将举行的活动
    upcoming_activities = []
    if club_ids:
        upcoming_activities = Activity.query.filter(
            Activity.ClubID.in_(club_ids),
            Activity.Status == '计划中',
            Activity.StartTime > datetime.now()
        ).order_by(Activity.StartTime.asc()).all()

    return render_template('president/activity_registrations.html',
                         clubs=clubs,
                         upcoming_activities=upcoming_activities)

# ==================== 申请审批处理路由 ====================

@president_bp.route('/approve_application/<int:record_id>', methods=['POST'])
@login_required
@role_required('会长', '管理员')
def approve_application(record_id):
    """
    批准成员申请
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 获取申请记录
    application = MemberClub.query.get_or_404(record_id)

    # 验证权限：只能处理自己管理的社团申请
    club = Club.query.get(application.ClubID)
    if club.PresidentID != user.MemberID:
        flash('您没有权限处理此申请', 'error')
        return redirect(url_for('president.member_applications'))

    # 检查申请状态
    if application.Status != '待审批':
        flash('该申请已被处理', 'warning')
        return redirect(url_for('president.member_applications'))

    try:
        # 更新申请状态
        application.Status = '已批准'
        application.ApprovalTime = datetime.now()
        application.ApprovalComments = request.form.get('comments', '').strip()

        db.session.commit()
        flash(f'已批准 {application.member.Name or application.member.Username} 的入会申请', 'success')

    except Exception as e:
        db.session.rollback()
        flash('批准申请失败，请重试', 'error')
        print(f"Error approving application: {e}")

    # 如果是AJAX请求，返回JSON响应
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': True, 'message': f'已批准 {application.member.Name or application.member.Username} 的入会申请'})

    return redirect(url_for('president.member_applications'))

@president_bp.route('/reject_application/<int:record_id>', methods=['POST'])
@login_required
@role_required('会长', '管理员')
def reject_application(record_id):
    """
    拒绝成员申请
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 获取申请记录
    application = MemberClub.query.get_or_404(record_id)

    # 验证权限：只能处理自己管理的社团申请
    club = Club.query.get(application.ClubID)
    if club.PresidentID != user.MemberID:
        flash('您没有权限处理此申请', 'error')
        return redirect(url_for('president.member_applications'))

    # 检查申请状态
    if application.Status != '待审批':
        flash('该申请已被处理', 'warning')
        return redirect(url_for('president.member_applications'))

    try:
        # 更新申请状态
        application.Status = '已拒绝'
        application.ApprovalTime = datetime.now()
        application.ApprovalComments = request.form.get('comments', '').strip()

        db.session.commit()
        flash(f'已拒绝 {application.member.Name or application.member.Username} 的入会申请', 'info')

    except Exception as e:
        db.session.rollback()
        flash('拒绝申请失败，请重试', 'error')
        print(f"Error rejecting application: {e}")

    # 如果是AJAX请求，返回JSON响应
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': True, 'message': f'已拒绝 {application.member.Name or application.member.Username} 的入会申请'})

    return redirect(url_for('president.member_applications'))

# ==================== 活动详情路由 ====================

@president_bp.route('/activity/<activity_id>')
@login_required
@role_required('会长', '管理员')
def activity_detail(activity_id):
    """
    活动详情页面
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 获取活动信息
    activity = Activity.query.get_or_404(activity_id)

    # 验证权限：只能查看自己管理的社团活动
    if activity.club.PresidentID != user.MemberID:
        flash('您没有权限查看此活动', 'error')
        return redirect(url_for('president.activities'))

    return render_template('president/activity_detail.html',
                         activity=activity)

# ==================== 活动创建和编辑路由 ====================

@president_bp.route('/create_activity/<club_id>')
@login_required
@role_required('会长', '管理员')
def create_activity(club_id):
    """
    创建活动页面
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 验证社团权限
    club = Club.query.get_or_404(club_id)
    if club.PresidentID != user.MemberID:
        flash('您没有权限为此社团创建活动', 'error')
        return redirect(url_for('president.activities'))

    # 获取所有场馆（数据库中Venues表没有Status字段）
    venues = Venue.query.all()

    return render_template('president/create_activity.html',
                         club=club,
                         venues=venues)

@president_bp.route('/edit_activity/<activity_id>')
@login_required
@role_required('会长', '管理员')
def edit_activity(activity_id):
    """
    编辑活动页面
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 获取活动信息
    activity = Activity.query.get_or_404(activity_id)

    # 验证权限：只能编辑自己管理的社团活动
    if activity.club.PresidentID != user.MemberID:
        flash('您没有权限编辑此活动', 'error')
        return redirect(url_for('president.activities'))

    # 只能编辑计划中的活动
    if activity.Status != '计划中':
        flash('只能编辑计划中的活动', 'warning')
        return redirect(url_for('president.activity_detail', activity_id=activity_id))

    # 获取所有场馆（数据库中Venues表没有Status字段）
    venues = Venue.query.all()

    return render_template('president/edit_activity.html',
                         activity=activity,
                         venues=venues)

@president_bp.route('/create_activity/<club_id>', methods=['POST'])
@login_required
@role_required('会长', '管理员')
def create_activity_post(club_id):
    """
    处理活动创建表单提交
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 验证社团权限
    club = Club.query.get_or_404(club_id)
    if club.PresidentID != user.MemberID:
        flash('您没有权限为此社团创建活动', 'error')
        return redirect(url_for('president.activities'))

    try:
        # 获取表单数据
        activity_data = {
            'ActivityName': request.form.get('activity_name').strip(),
            'ActivityType': request.form.get('activity_type'),
            'StartTime': datetime.strptime(request.form.get('start_time'), '%Y-%m-%dT%H:%M'),
            'EndTime': datetime.strptime(request.form.get('end_time'), '%Y-%m-%dT%H:%M'),
            'VenueID': request.form.get('venue_id'),
            'ClubID': club_id,
            'OrganizerID': user.MemberID,
            'Status': '计划中',
            'ActualParticipant': 0
        }

        # 可选字段
        participant_limit = request.form.get('participant_limit')
        if participant_limit:
            activity_data['ParticipantLimit'] = int(participant_limit)

        description = request.form.get('description', '').strip()
        if description:
            activity_data['Description'] = description

        requirements = request.form.get('requirements', '').strip()
        if requirements:
            activity_data['Requirements'] = requirements

        # 验证时间
        if activity_data['EndTime'] <= activity_data['StartTime']:
            flash('结束时间必须晚于开始时间', 'error')
            return redirect(url_for('president.create_activity', club_id=club_id))

        # 检查场馆时间冲突
        venue = Venue.query.get(activity_data['VenueID'])
        if not venue.check_availability(activity_data['StartTime'], activity_data['EndTime']):
            flash('选择的时间段场馆不可用，请选择其他时间', 'error')
            return redirect(url_for('president.create_activity', club_id=club_id))

        # 创建活动
        activity = Activity(**activity_data)
        db.session.add(activity)
        db.session.commit()

        flash(f'活动 "{activity.ActivityName}" 创建成功', 'success')
        return redirect(url_for('president.activity_detail', activity_id=activity.ActivityID))

    except ValueError as e:
        flash('时间格式错误，请重新选择', 'error')
        return redirect(url_for('president.create_activity', club_id=club_id))
    except Exception as e:
        db.session.rollback()
        flash('创建活动失败，请重试', 'error')
        print(f"Error creating activity: {e}")
        return redirect(url_for('president.create_activity', club_id=club_id))

@president_bp.route('/edit_activity/<activity_id>', methods=['POST'])
@login_required
@role_required('会长', '管理员')
def edit_activity_post(activity_id):
    """
    处理活动编辑表单提交
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 获取活动信息
    activity = Activity.query.get_or_404(activity_id)

    # 验证权限
    if activity.club.PresidentID != user.MemberID:
        flash('您没有权限编辑此活动', 'error')
        return redirect(url_for('president.activities'))

    # 只能编辑计划中的活动
    if activity.Status != '计划中':
        flash('只能编辑计划中的活动', 'warning')
        return redirect(url_for('president.activity_detail', activity_id=activity_id))

    try:
        # 更新活动数据
        activity.ActivityName = request.form.get('activity_name').strip()
        activity.ActivityType = request.form.get('activity_type')
        activity.StartTime = datetime.strptime(request.form.get('start_time'), '%Y-%m-%dT%H:%M')
        activity.EndTime = datetime.strptime(request.form.get('end_time'), '%Y-%m-%dT%H:%M')
        activity.VenueID = request.form.get('venue_id')

        # 可选字段
        participant_limit = request.form.get('participant_limit')
        if participant_limit:
            new_limit = int(participant_limit)
            if new_limit < activity.ActualParticipant:
                flash(f'人数限制不能少于当前参与人数({activity.ActualParticipant}人)', 'error')
                return redirect(url_for('president.edit_activity', activity_id=activity_id))
            activity.ParticipantLimit = new_limit
        else:
            activity.ParticipantLimit = None

        activity.Description = request.form.get('description', '').strip() or None
        activity.Requirements = request.form.get('requirements', '').strip() or None

        # 验证时间
        if activity.EndTime <= activity.StartTime:
            flash('结束时间必须晚于开始时间', 'error')
            return redirect(url_for('president.edit_activity', activity_id=activity_id))

        # 检查场馆时间冲突（排除当前活动）
        venue = Venue.query.get(activity.VenueID)
        if not venue.check_availability(activity.StartTime, activity.EndTime, exclude_activity_id=activity_id):
            flash('选择的时间段场馆不可用，请选择其他时间', 'error')
            return redirect(url_for('president.edit_activity', activity_id=activity_id))

        db.session.commit()
        flash(f'活动 "{activity.ActivityName}" 修改成功', 'success')
        return redirect(url_for('president.activity_detail', activity_id=activity_id))

    except ValueError as e:
        flash('时间格式错误，请重新选择', 'error')
        return redirect(url_for('president.edit_activity', activity_id=activity_id))
    except Exception as e:
        db.session.rollback()
        flash('修改活动失败，请重试', 'error')
        print(f"Error editing activity: {e}")
        return redirect(url_for('president.edit_activity', activity_id=activity_id))

# ==================== 成员管理操作路由 ====================

@president_bp.route('/members/remove/<record_id>', methods=['POST'])
@login_required
@role_required('会长', '管理员')
def remove_member_from_list(record_id):
    """
    移除社团成员
    """
    user = get_current_user()
    if not user:
        flash('用户信息获取失败', 'error')
        return redirect(url_for('auth.login'))

    # 获取成员记录
    member_club = MemberClub.query.get_or_404(record_id)

    # 验证权限：只能处理自己管理的社团成员
    club = Club.query.get(member_club.ClubID)
    if club.PresidentID != user.MemberID:
        flash('您没有权限移除此成员', 'error')
        return redirect(url_for('president.members'))

    # 只能移除已批准的成员
    if member_club.Status != '已批准':
        flash('只能移除已加入的成员', 'warning')
        return redirect(url_for('president.members'))

    try:
        # 更新成员状态为已退出（数据库中ClubMembers表没有ApprovalComments字段）
        member_club.Status = '已退出'
        member_club.ApprovalTime = datetime.now()
        # 注意：数据库中没有ApprovalComments字段，移除原因可以记录在其他地方

        db.session.commit()

        # 如果是AJAX请求，返回JSON响应
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': True, 'message': f'已移除成员 {member_club.member.Name or member_club.member.Username}'})

        flash(f'已移除成员 {member_club.member.Name or member_club.member.Username}', 'success')

    except Exception as e:
        db.session.rollback()
        error_msg = '移除成员失败，请重试'
        print(f"Error removing member: {e}")

        # 如果是AJAX请求，返回JSON响应
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': error_msg}), 500

        flash(error_msg, 'error')

    return redirect(url_for('president.members'))

@president_bp.route('/members/detail/<record_id>')
@login_required
@role_required('会长', '管理员')
def get_member_detail(record_id):
    """
    获取成员详情信息
    """
    user = get_current_user()
    if not user:
        return jsonify({'success': False, 'message': '用户信息获取失败'}), 401

    # 获取成员记录
    member_club = MemberClub.query.get_or_404(record_id)

    # 验证权限：只能查看自己管理的社团成员
    club = Club.query.get(member_club.ClubID)
    if club.PresidentID != user.MemberID:
        return jsonify({'success': False, 'message': '您没有权限查看此成员信息'}), 403

    try:
        # 获取成员基本信息
        member = member_club.member

        # 获取成员参与的活动记录（该社团的活动）
        # 注意：这里需要根据实际的活动参与表结构来查询
        # 暂时使用占位数据
        activity_records = []

        # 构建返回数据
        member_detail = {
            'record_id': member_club.RecordID,
            'basic_info': {
                'name': getattr(member, 'Name', None) or '未填写',
                'username': getattr(member, 'Username', None) or '未填写',
                'age': getattr(member, 'Age', None) or '未填写',
                'college': getattr(member, 'College', None) or '未填写',
                'dormitory': getattr(member, 'Dormitory', None) or '未填写',
                'specialty': getattr(member, 'Specialty', None) or '未填写',
                'gender': getattr(member, 'Gender', None) or '未填写',
                'role': getattr(member, 'Role', None) or '未填写'
            },
            'contact_info': {
                'phone': getattr(member, 'Phone', None) or '未填写'
            },
            'club_info': {
                'club_name': club.ClubName,
                'apply_time': member_club.ApplyTime.strftime('%Y-%m-%d %H:%M') if getattr(member_club, 'ApplyTime', None) else '未知',
                'application_reason': getattr(member_club, 'ApplicationReason', None) or '无',
                'status': getattr(member_club, 'Status', None) or '未知',
                'approval_time': member_club.ApprovalTime.strftime('%Y-%m-%d %H:%M') if getattr(member_club, 'ApprovalTime', None) else '未审批',
                'approval_id': getattr(member_club, 'ApprovalId', None) or '无',
                'rejoinable': getattr(member_club, 'Rejoinable', None) or '未知'
            },
            'activity_stats': {
                'total_activities': len(activity_records),
                'completed_activities': 0,  # 需要根据实际数据计算
                'recent_activities': activity_records[:5]  # 最近5个活动
            },
            'member_since_days': (datetime.now() - member_club.ApplyTime).days if getattr(member_club, 'ApplyTime', None) else 0
        }

        return jsonify({
            'success': True,
            'data': member_detail
        })

    except Exception as e:
        print(f"Error getting member detail: {e}")
        return jsonify({
            'success': False,
            'message': '获取成员详情失败'
        }), 500
