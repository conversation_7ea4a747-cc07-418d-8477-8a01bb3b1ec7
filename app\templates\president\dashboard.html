{% extends "admin_base.html" %}

{% block title %}会长中心 - 学校社团管理系统{% endblock %}

{% block content %}
    <!-- 欢迎横幅 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-crown me-2"></i>会长工作台
                            </h2>
                            <p class="mb-0 opacity-75">
                                欢迎，{{ session.username }}！管理您的社团，组织精彩活动。
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="user-avatar-large">
                                {{ session.username[0].upper() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速统计 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.total_members or 0 }}</h4>
                    <p class="text-muted mb-0">社团成员</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-calendar-check fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.total_activities or 0 }}</h4>
                    <p class="text-muted mb-0">组织活动</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.pending_requests or 0 }}</h4>
                    <p class="text-muted mb-0">待处理申请</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-layer-group fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.total_clubs or 0 }}</h4>
                    <p class="text-muted mb-0">管理社团</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 左侧内容 -->
        <div class="col-lg-8">
            <!-- 即将举行的活动 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2 text-warning"></i>即将举行的活动
                    </h5>
                </div>
                <div class="card-body">
                    {% if upcoming_activities %}
                    <div class="row">
                        {% for activity in upcoming_activities %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-start border-warning border-3">
                                <div class="card-body">
                                    <h6 class="card-title">{{ activity.ActivityName }}</h6>
                                    <p class="card-text">
                                        <i class="fas fa-calendar me-1"></i>{{ activity.StartTime.strftime('%m月%d日 %H:%M') if activity.StartTime else '时间待定' }}
                                    </p>
                                    <p class="card-text">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ activity.venue.VenueName if activity.venue else '地点待定' }}
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-warning">{{ activity.Status }}</span>
                                        <a href="{{ url_for('president.activity_detail', activity_id=activity.ActivityID) }}"
                                           class="btn btn-sm btn-outline-primary">查看详情</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-alt text-muted" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 text-muted">暂无即将举行的活动</h6>
                        <p class="text-muted">创建新活动，丰富社团生活！</p>
                        <a href="{{ url_for('president.activities') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>管理活动
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 社团成员管理 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2 text-primary"></i>社团成员
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_members %}
                    <!-- 成员列表表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>成员信息</th>
                                    <th>学号/用户名</th>
                                    <th>角色</th>
                                    <th>加入时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for member_club in recent_members %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-3">
                                                {{ member_club.member.Name[0] if member_club.member.Name else member_club.member.Username[0] }}
                                            </div>
                                            <div>
                                                <div class="fw-semibold">{{ member_club.member.Name or member_club.member.Username }}</div>
                                                {% if member_club.member.College %}
                                                <small class="text-muted">{{ member_club.member.College }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ member_club.member.Username }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ member_club.member.Role or '会员' }}</span>
                                    </td>
                                    <td>
                                        {% if member_club.ApprovalTime %}
                                        <div>{{ member_club.ApprovalTime.strftime('%Y-%m-%d') }}</div>
                                        <small class="text-muted">{{ member_club.ApprovalTime.strftime('%H:%M') }}</small>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ member_club.get_status_display() }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary"
                                                    onclick="viewMemberDetail('{{ member_club.member.MemberID }}')"
                                                    title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary"
                                                    onclick="manageMember('{{ member_club.RecordID }}')"
                                                    title="管理">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 待审批成员提醒 -->
                    {% if pending_members %}
                    <div class="alert alert-warning d-flex align-items-center mt-3" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            还有 <strong>{{ pending_members|length }}</strong> 个成员申请待审批
                            <a href="{{ url_for('president.member_applications') }}" class="alert-link ms-2">立即处理</a>
                        </div>
                    </div>
                    {% endif %}

                    <div class="text-center mt-3">
                        <a href="{{ url_for('president.members') }}" class="btn btn-outline-primary">
                            <i class="fas fa-users me-1"></i>查看全部成员
                        </a>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 text-muted">暂无成员数据</h6>
                        <p class="text-muted">当前社团还没有成员，快去招募新成员吧！</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 右侧边栏 -->
        <div class="col-lg-4">


            <!-- 待处理申请 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2 text-warning"></i>待处理申请
                    </h5>
                </div>
                <div class="card-body">
                    {% if pending_applications %}
                    {% for application in pending_applications %}
                    <div class="d-flex align-items-start mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                        <div class="notification-icon me-3">
                            <i class="fas fa-user-plus text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ application.member.Name or application.member.Username }}</h6>
                            <p class="mb-1 small text-muted">申请加入社团</p>
                            <small class="text-muted">{{ application.ApplyTime.strftime('%m-%d %H:%M') if application.ApplyTime else '刚刚' }}</small>
                        </div>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-success"
                                    onclick="approveApplication('{{ application.RecordID }}')">
                                <i class="fas fa-check"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger"
                                    onclick="rejectApplication('{{ application.RecordID }}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle text-muted" style="font-size: 2rem;"></i>
                        <p class="mt-2 text-muted mb-0">暂无待处理申请</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 社团信息 -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-layer-group me-2 text-info"></i>社团信息
                    </h5>
                </div>
                <div class="card-body">
                    {% if my_club %}
                    <div class="text-center mb-3">
                        <div class="club-avatar-lg mb-3">
                            {{ my_club.ClubName[0] }}
                        </div>
                        <h6 class="mb-1">{{ my_club.ClubName }}</h6>
                        <p class="text-muted small">{{ my_club.Description }}</p>
                    </div>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="fw-bold">{{ my_club.CurrentMembers }}</div>
                            <small class="text-muted">当前成员</small>
                        </div>
                        <div class="col-6">
                            <div class="fw-bold">{{ my_club.MaxMembers or '∞' }}</div>
                            <small class="text-muted">最大容量</small>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('president.members') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-users me-1"></i>成员管理
                        </a>
                    </div>
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-layer-group text-muted" style="font-size: 2rem;"></i>
                        <p class="mt-2 text-muted mb-0">暂无社团信息</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 快速导航面板 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-warning"></i>快速导航
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('president.members') }}"
                               class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-users d-block mb-2" style="font-size: 1.5rem;"></i>
                                <div class="fw-semibold">成员管理</div>
                                <small class="text-muted">管理社团成员</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('president.member_applications') }}"
                               class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-clock d-block mb-2" style="font-size: 1.5rem;"></i>
                                <div class="fw-semibold">申请审批</div>
                                <small class="text-muted">处理入会申请</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('president.activities') }}"
                               class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-calendar-alt d-block mb-2" style="font-size: 1.5rem;"></i>
                                <div class="fw-semibold">活动管理</div>
                                <small class="text-muted">管理社团活动</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('president.registrations') }}"
                               class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-clipboard-check d-block mb-2" style="font-size: 1.5rem;"></i>
                                <div class="fw-semibold">报名审批</div>
                                <small class="text-muted">活动报名管理</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
.user-avatar-large {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 2rem;
    border: 3px solid rgba(255,255,255,0.3);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

.club-avatar-lg {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
    margin: 0 auto;
}

.notification-icon {
    width: 30px;
    text-align: center;
}

/* 快速导航按钮样式 */
.btn-outline-primary:hover,
.btn-outline-warning:hover,
.btn-outline-success:hover,
.btn-outline-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 查看成员详情
function viewMemberDetail(memberId) {
    // 创建模态框显示成员详细信息
    fetch(`/api/member/${memberId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMemberDetailModal(data.member);
            } else {
                alert('获取成员信息失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('查看成员详情功能开发中，请前往成员管理页面查看详细信息。');
            window.location.href = '{{ url_for("president.members") }}';
        });
}

// 管理成员
function manageMember(recordId) {
    // 跳转到成员管理页面
    window.location.href = `{{ url_for("president.members") }}`;
}

// 显示成员详情模态框
function showMemberDetailModal(member) {
    // 这里可以创建一个模态框显示成员详细信息
    // 暂时使用alert显示基本信息
    let info = `成员信息：\n`;
    info += `姓名：${member.Name}\n`;
    info += `用户名：${member.Username}\n`;
    info += `角色：${member.Role}\n`;
    if (member.College) info += `学院：${member.College}\n`;
    if (member.Phone) info += `电话：${member.Phone}\n`;
    alert(info);
}

// 批准申请
function approveApplication(requestId) {
    if (confirm('确定要批准这个申请吗？')) {
        // 发送批准请求
        fetch('/president/approve-application', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({
                record_id: requestId,
                action: 'approve'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('申请已批准！');
                location.reload();
            } else {
                alert('批准失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('申请审批功能将在成员管理模块中实现，请前往申请审批页面进行操作。');
            window.location.href = '{{ url_for("president.member_applications") }}';
        });
    }
}

// 拒绝申请
function rejectApplication(requestId) {
    let reason = prompt('请输入拒绝理由（可选）：');
    if (reason !== null) {
        // 发送拒绝请求
        fetch('/president/approve-application', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify({
                record_id: requestId,
                action: 'reject',
                reason: reason
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('申请已拒绝！');
                location.reload();
            } else {
                alert('拒绝失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('申请审批功能将在成员管理模块中实现，请前往申请审批页面进行操作。');
            window.location.href = '{{ url_for("president.member_applications") }}';
        });
    }
}
</script>
{% endblock %}
