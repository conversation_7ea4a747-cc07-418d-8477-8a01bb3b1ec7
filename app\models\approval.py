#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ApprovalRequests表数据模型
严格按照database.sql中的ApprovalRequests表结构定义
审批申请流程管理
"""

from app import db
from datetime import datetime
import uuid

class ApprovalRequest(db.Model):
    """
    审批申请模型类 - 对应database.sql中的ApprovalRequests表
    管理各类审批申请的提交、处理、跟踪流程
    """
    
    __tablename__ = 'ApprovalRequests'
    
    # 主键：RequestID CHAR(36) PRIMARY KEY
    RequestID = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # ApplicantID CHAR(36) NOT NULL, FOREIGN KEY (ApplicantID) REFERENCES Members(MemberID)
    ApplicantID = db.Column(db.String(36), db.ForeignKey('Members.MemberID'), 
                           nullable=False, comment='申请人ID')
    
    # RequestType ENUM('入会', '退会', '活动申请', '其他') NOT NULL
    RequestType = db.Column(db.Enum('入会', '退会', '活动申请', '其他'), 
                           nullable=False, comment='申请类型')
    
    # RequestTime DATETIME NOT NULL
    RequestTime = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='申请时间')
    
    # Status ENUM('待批', '已批', '已拒') NOT NULL
    Status = db.Column(db.Enum('待批', '已批', '已拒'), 
                      nullable=False, default='待批', comment='申请状态')
    
    # RelatedID CHAR(36)
    RelatedID = db.Column(db.String(36), nullable=True, comment='关联ID（如社团ID、活动ID）')
    
    # ApprovalTime DATETIME
    ApprovalTime = db.Column(db.DateTime, nullable=True, comment='审批时间')
    
    # Comments VARCHAR(200)
    Comments = db.Column(db.String(200), nullable=True, comment='审批意见或附件')

    # 注意：applicant关系已在Member模型中通过backref='applicant'自动创建

    def __init__(self, **kwargs):
        """初始化审批申请对象"""
        super(ApprovalRequest, self).__init__(**kwargs)
        if not self.RequestID:
            self.RequestID = str(uuid.uuid4())
        if not self.RequestTime:
            self.RequestTime = datetime.now()
    
    def approve(self, approver_id, comments=None):
        """
        批准申请
        
        Args:
            approver_id: 审批人ID
            comments: 审批意见
        """
        self.Status = '已批'
        self.ApprovalTime = datetime.now()
        if comments:
            self.Comments = comments
        
        # 根据申请类型执行相应的业务逻辑
        self._execute_approval_logic()
    
    def reject(self, approver_id, comments=None):
        """
        拒绝申请
        
        Args:
            approver_id: 审批人ID
            comments: 拒绝理由
        """
        self.Status = '已拒'
        self.ApprovalTime = datetime.now()
        if comments:
            self.Comments = comments
    
    def _execute_approval_logic(self):
        """执行批准后的业务逻辑"""
        if self.RequestType == '活动申请' and self.RelatedID:
            # 活动申请批准后，更新活动状态从'待审批'改为'计划中'
            from app.models.activity import Activity
            activity = Activity.query.get(self.RelatedID)
            if activity and activity.Status == '待审批':
                # 活动申请批准，状态从'待审批'更新为'计划中'
                activity.Status = '计划中'
        
        elif self.RequestType == '入会' and self.RelatedID:
            # 入会申请批准后，更新会员社团关系
            from app.models.member_club import MemberClub
            member_club = MemberClub.query.filter_by(
                MemberID=self.ApplicantID,
                ClubID=self.RelatedID,
                Status='申请中'
            ).first()
            if member_club:
                member_club.approve(self.ApplicantID)
        
        elif self.RequestType == '退会' and self.RelatedID:
            # 退会申请批准后，更新会员社团关系
            from app.models.member_club import MemberClub
            member_club = MemberClub.query.filter_by(
                MemberID=self.ApplicantID,
                ClubID=self.RelatedID,
                Status='已批准'
            ).first()
            if member_club:
                member_club.withdraw(self.Comments)
    
    def can_be_approved(self):
        """判断是否可以批准"""
        return self.Status == '待批'
    
    def can_be_rejected(self):
        """判断是否可以拒绝"""
        return self.Status == '待批'
    
    def is_pending(self):
        """判断是否为待审批状态"""
        return self.Status == '待批'
    
    def is_approved(self):
        """判断是否已批准"""
        return self.Status == '已批'
    
    def is_rejected(self):
        """判断是否已拒绝"""
        return self.Status == '已拒'
    
    def get_status_display(self):
        """获取状态的中文显示"""
        status_map = {
            '待批': '待审批',
            '已批': '已批准',
            '已拒': '已拒绝'
        }
        return status_map.get(self.Status, self.Status)
    
    def get_processing_duration(self):
        """获取处理耗时（天数）"""
        if self.ApprovalTime and self.RequestTime:
            duration = self.ApprovalTime - self.RequestTime
            return duration.days
        return None
    
    def is_overdue(self, timeout_days=7):
        """
        判断申请是否超时
        
        Args:
            timeout_days: 超时天数，默认7天
        
        Returns:
            bool: True表示超时
        """
        if self.Status == '待批':
            from datetime import timedelta
            timeout_date = self.RequestTime + timedelta(days=timeout_days)
            return datetime.now() > timeout_date
        return False
    
    def get_related_object(self):
        """获取关联对象"""
        if not self.RelatedID:
            return None
        
        if self.RequestType == '活动申请':
            from app.models.activity import Activity
            return Activity.query.get(self.RelatedID)
        elif self.RequestType in ['入会', '退会']:
            from app.models.club import Club
            return Club.query.get(self.RelatedID)
        
        return None
    
    def get_related_name(self):
        """获取关联对象名称"""
        related_obj = self.get_related_object()
        if related_obj:
            if hasattr(related_obj, 'ActivityName'):
                return related_obj.ActivityName
            elif hasattr(related_obj, 'ClubName'):
                return related_obj.ClubName
        return None
    
    @staticmethod
    def get_pending_requests(request_type=None, limit=None):
        """
        获取待审批的申请列表
        
        Args:
            request_type: 申请类型过滤
            limit: 限制数量
        
        Returns:
            list: 待审批申请列表
        """
        query = ApprovalRequest.query.filter_by(Status='待批')
        if request_type:
            query = query.filter_by(RequestType=request_type)
        
        query = query.order_by(ApprovalRequest.RequestTime.asc())
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    @staticmethod
    def get_user_requests(user_id, status=None):
        """
        获取用户的申请列表
        
        Args:
            user_id: 用户ID
            status: 状态过滤
        
        Returns:
            list: 用户申请列表
        """
        query = ApprovalRequest.query.filter_by(ApplicantID=user_id)
        if status:
            query = query.filter_by(Status=status)
        
        return query.order_by(ApprovalRequest.RequestTime.desc()).all()
    
    @staticmethod
    def create_activity_request(applicant_id, activity_id, comments=None):
        """
        创建活动申请
        
        Args:
            applicant_id: 申请人ID
            activity_id: 活动ID
            comments: 申请说明
        
        Returns:
            ApprovalRequest: 创建的申请对象
        """
        request = ApprovalRequest(
            ApplicantID=applicant_id,
            RequestType='活动申请',
            RelatedID=activity_id,
            Comments=comments
        )
        return request
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'RequestID': self.RequestID,
            'ApplicantID': self.ApplicantID,
            'RequestType': self.RequestType,
            'RequestTime': self.RequestTime.isoformat() if self.RequestTime else None,
            'Status': self.Status,
            'StatusDisplay': self.get_status_display(),
            'RelatedID': self.RelatedID,
            'ApprovalTime': self.ApprovalTime.isoformat() if self.ApprovalTime else None,
            'Comments': self.Comments,
            'ApplicantName': self.applicant.Name if self.applicant else None,
            'RelatedName': self.get_related_name(),
            'ProcessingDuration': self.get_processing_duration(),
            'IsOverdue': self.is_overdue()
        }
    
    def __repr__(self):
        applicant_name = self.applicant.Name if self.applicant else 'Unknown'
        return f'<ApprovalRequest {self.RequestType}-{applicant_name}({self.Status})>'
