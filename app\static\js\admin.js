// 管理后台JavaScript

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 侧边栏菜单激活状态
    $('.sidebar .nav-link').each(function() {
        if ($(this).attr('href') === window.location.pathname) {
            $(this).addClass('active');
        }
    });

    // 确认删除对话框
    $('.btn-delete').click(function(e) {
        e.preventDefault();
        var $this = $(this);
        var message = $this.data('message') || '确定要删除这个项目吗？删除后无法恢复！';
        
        if (confirm(message)) {
            if ($this.attr('href')) {
                window.location.href = $this.attr('href');
            } else if ($this.closest('form').length) {
                $this.closest('form').submit();
            }
        }
    });

    // 表单提交加载状态
    $('form').submit(function() {
        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        
        if ($submitBtn.length && !$submitBtn.prop('disabled')) {
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true)
                     .html('<i class="fas fa-spinner fa-spin me-2"></i>处理中...');
            
            // 10秒后恢复按钮状态（防止卡死）
            setTimeout(function() {
                $submitBtn.prop('disabled', false).html(originalText);
            }, 10000);
        }
    });

    // 批量选择功能
    $('.select-all').change(function() {
        var checked = $(this).prop('checked');
        $(this).closest('table').find('.select-item').prop('checked', checked);
        updateBatchActions();
    });

    $('.select-item').change(function() {
        var $table = $(this).closest('table');
        var $selectAll = $table.find('.select-all');
        var totalItems = $table.find('.select-item').length;
        var checkedItems = $table.find('.select-item:checked').length;
        
        $selectAll.prop('checked', totalItems === checkedItems);
        $selectAll.prop('indeterminate', checkedItems > 0 && checkedItems < totalItems);
        
        updateBatchActions();
    });

    function updateBatchActions() {
        var selectedCount = $('.select-item:checked').length;
        var $batchActions = $('.batch-actions');
        
        if (selectedCount > 0) {
            $batchActions.show().find('.selected-count').text(selectedCount);
        } else {
            $batchActions.hide();
        }
    }

    // 表格排序
    $('.sortable-table th[data-sort]').click(function() {
        var $th = $(this);
        var $table = $th.closest('table');
        var column = $th.data('sort');
        var order = $th.hasClass('sort-asc') ? 'desc' : 'asc';
        
        // 移除其他列的排序标识
        $table.find('th').removeClass('sort-asc sort-desc');
        $th.addClass('sort-' + order);
        
        // 执行排序
        sortTable($table, column, order);
    });

    function sortTable($table, column, order) {
        var $tbody = $table.find('tbody');
        var $rows = $tbody.find('tr').toArray();
        
        $rows.sort(function(a, b) {
            var aVal = $(a).find('td').eq(column).text().trim();
            var bVal = $(b).find('td').eq(column).text().trim();
            
            // 尝试数字比较
            if (!isNaN(aVal) && !isNaN(bVal)) {
                aVal = parseFloat(aVal);
                bVal = parseFloat(bVal);
            }
            
            if (order === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });
        
        $tbody.empty().append($rows);
    }

    // 搜索框实时搜索
    $('.search-input').on('input', function() {
        var $input = $(this);
        var query = $input.val().trim();
        var minLength = $input.data('min-length') || 2;
        
        if (query.length >= minLength) {
            clearTimeout($input.data('timeout'));
            $input.data('timeout', setTimeout(function() {
                performSearch(query, $input);
            }, 500));
        } else {
            hideSearchResults($input);
        }
    });

    function performSearch(query, $input) {
        var searchUrl = $input.data('search-url');
        if (!searchUrl) return;

        $.ajax({
            url: searchUrl,
            data: { q: query },
            method: 'GET',
            beforeSend: function() {
                $input.addClass('loading');
            },
            success: function(data) {
                displaySearchResults(data, $input);
            },
            error: function() {
                console.error('搜索请求失败');
            },
            complete: function() {
                $input.removeClass('loading');
            }
        });
    }

    function displaySearchResults(data, $input) {
        var $results = $input.siblings('.search-results');
        if (!$results.length) {
            $results = $('<div class="search-results"></div>').insertAfter($input);
        }

        $results.empty();

        if (data.results && data.results.length > 0) {
            data.results.forEach(function(item) {
                var $item = $('<div class="search-result-item"></div>')
                    .html('<strong>' + item.title + '</strong><br><small>' + item.description + '</small>')
                    .click(function() {
                        window.location.href = item.url;
                    });
                $results.append($item);
            });
            $results.show();
        } else {
            $results.hide();
        }
    }

    function hideSearchResults($input) {
        $input.siblings('.search-results').hide();
    }

    // 点击外部隐藏搜索结果
    $(document).click(function(e) {
        if (!$(e.target).closest('.search-input, .search-results').length) {
            $('.search-results').hide();
        }
    });

    // 数据导出功能
    $('.btn-export').click(function() {
        var format = $(this).data('format') || 'excel';
        var url = window.location.href;
        
        // 添加导出参数
        var separator = url.indexOf('?') > -1 ? '&' : '?';
        window.open(url + separator + 'export=' + format);
    });

    // 自动刷新功能
    if ($('.auto-refresh').length) {
        setInterval(function() {
            $('.auto-refresh').each(function() {
                var $element = $(this);
                var url = $element.data('refresh-url');
                
                if (url) {
                    $.get(url).done(function(data) {
                        $element.html(data);
                    });
                }
            });
        }, 30000); // 30秒刷新一次
    }

    // 图表初始化
    initCharts();

    // 表单验证增强
    $('form[data-validate]').each(function() {
        var $form = $(this);
        
        $form.find('input, select, textarea').on('blur', function() {
            validateField($(this));
        });
        
        $form.submit(function(e) {
            var isValid = true;
            
            $form.find('input, select, textarea').each(function() {
                if (!validateField($(this))) {
                    isValid = false;
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showToast('请检查表单中的错误', 'error');
            }
        });
    });

    function validateField($field) {
        var value = $field.val().trim();
        var rules = $field.data('rules');
        var isValid = true;
        var message = '';
        
        if (rules) {
            if (rules.required && !value) {
                isValid = false;
                message = '此字段为必填项';
            } else if (rules.minLength && value.length < rules.minLength) {
                isValid = false;
                message = '最少需要' + rules.minLength + '个字符';
            } else if (rules.maxLength && value.length > rules.maxLength) {
                isValid = false;
                message = '最多允许' + rules.maxLength + '个字符';
            } else if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
                isValid = false;
                message = rules.message || '格式不正确';
            } else if (rules.min && parseFloat(value) < rules.min) {
                isValid = false;
                message = '最小值为' + rules.min;
            } else if (rules.max && parseFloat(value) > rules.max) {
                isValid = false;
                message = '最大值为' + rules.max;
            }
        }
        
        if (isValid) {
            $field.removeClass('is-invalid').addClass('is-valid');
            $field.siblings('.invalid-feedback').hide();
        } else {
            $field.removeClass('is-valid').addClass('is-invalid');
            var $feedback = $field.siblings('.invalid-feedback');
            if (!$feedback.length) {
                $feedback = $('<div class="invalid-feedback"></div>').insertAfter($field);
            }
            $feedback.text(message).show();
        }
        
        return isValid;
    }

    // 显示提示消息
    function showToast(message, type = 'success') {
        var $toast = $('<div class="toast-message toast-' + type + '">' + message + '</div>');
        $('body').append($toast);
        
        setTimeout(function() {
            $toast.addClass('show');
        }, 100);
        
        setTimeout(function() {
            $toast.removeClass('show');
            setTimeout(function() {
                $toast.remove();
            }, 300);
        }, 3000);
    }

    // 复制到剪贴板
    $('.copy-to-clipboard').click(function() {
        var text = $(this).data('text') || $(this).text();
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('已复制到剪贴板');
            });
        } else {
            var $temp = $('<textarea>').val(text).appendTo('body').select();
            document.execCommand('copy');
            $temp.remove();
            showToast('已复制到剪贴板');
        }
    });
});

// 图表初始化函数
function initCharts() {
    // 统计图表
    if ($('#statsChart').length && typeof Chart !== 'undefined') {
        var ctx = document.getElementById('statsChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['活跃社团', '休眠社团', '解散社团'],
                datasets: [{
                    data: [12, 3, 1],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
}

// CSS样式
var css = `
.loading {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24'%3E%3Cpath fill='%23999' d='M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z' opacity='.25'/%3E%3Cpath fill='%23999' d='M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z'%3E%3CanimateTransform attributeName='transform' dur='0.75s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.search-result-item {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast-message.show {
    transform: translateX(0);
}

.toast-success {
    background-color: #28a745;
}

.toast-error {
    background-color: #dc3545;
}

.toast-warning {
    background-color: #ffc107;
    color: #212529;
}

.toast-info {
    background-color: #17a2b8;
}
`;

// 添加CSS到页面
$('<style>').text(css).appendTo('head');
