{% extends "admin_base.html" %}

{% block title %}编辑场馆{% endblock %}

{% block page_title %}编辑场馆{% endblock %}

{% block page_actions %}
<a href="{{ url_for('admin.venues') }}" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-1"></i>返回列表
</a>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="bi bi-building me-2"></i>编辑场馆信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.edit_venue', venue_id=venue.VenueID) }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <!-- 基本信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="bi bi-info-circle me-1"></i>基本信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="venue_name" class="form-label">场馆名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="venue_name" name="venue_name" 
                                   value="{{ venue.VenueName }}" required maxlength="100">
                        </div>
                        <div class="col-md-6">
                            <label for="venue_type" class="form-label">场馆类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="venue_type" name="venue_type" required>
                                <option value="">请选择类型</option>
                                <option value="室内" {% if venue.VenueType == '室内' %}selected{% endif %}>室内</option>
                                <option value="室外" {% if venue.VenueType == '室外' %}selected{% endif %}>室外</option>
                                <option value="多功能厅" {% if venue.VenueType == '多功能厅' %}selected{% endif %}>多功能厅</option>
                                <option value="体育馆" {% if venue.VenueType == '体育馆' %}selected{% endif %}>体育馆</option>
                                <option value="其他" {% if venue.VenueType == '其他' %}selected{% endif %}>其他</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">位置 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   value="{{ venue.Location }}" required maxlength="100">
                        </div>
                        <div class="col-md-6">
                            <label for="capacity" class="form-label">容量</label>
                            <input type="number" class="form-control" id="capacity" name="capacity" 
                                   value="{{ venue.Capacity }}" min="1" max="10000">
                        </div>
                        <div class="col-12">
                            <label for="address" class="form-label">详细地址 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="address" name="address"
                                   value="{{ venue.Address }}" required maxlength="200">
                        </div>
                    </div>

                    <!-- 联系信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="bi bi-telephone me-1"></i>联系信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="contact_phone" class="form-label">联系电话</label>
                            <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                   value="{{ venue.ContactPhone }}" pattern="[0-9]{7,15}" maxlength="20">
                            <div class="form-text">7-15位数字</div>
                        </div>
                        <div class="col-md-6">
                            <label for="availab_time" class="form-label">可用时间 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="availab_time" name="availab_time"
                                   value="{{ venue.AvailabTime }}" required maxlength="20"
                                   placeholder="如：8:00-22:00">
                            <div class="form-text">场馆的开放时间</div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('admin.venues') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 电话号码验证
    $('#contact_phone').on('input', function() {
        const phone = $(this).val();
        if (phone && !/^[0-9]{7,15}$/.test(phone)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // 表单提交验证
    $('form').on('submit', function(e) {
        const invalidFields = $(this).find('.is-invalid');
        if (invalidFields.length > 0) {
            e.preventDefault();
            alert('请先修正表单中的错误');
            invalidFields.first().focus();
        }
    });
});
</script>
{% endblock %}
