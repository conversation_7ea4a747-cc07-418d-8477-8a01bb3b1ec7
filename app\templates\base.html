<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}学校社团管理系统{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS (本地) -->
    <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- FontAwesome Icons (本地) -->
    <link href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('common.index') }}">
                <i class="fas fa-users me-2"></i>社团管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('common.index') }}">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('common.clubs') }}">社团</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('common.activities') }}">活动</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('common.venues') }}">场馆</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if session.user_id %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i>{{ session.username }}
                            </a>
                            <ul class="dropdown-menu">
                                {% if session.role == '管理员' %}
                                    <li><a class="dropdown-item" href="{{ url_for('admin.dashboard') }}">
                                        <i class="fas fa-tachometer-alt me-2"></i>管理后台
                                    </a></li>
                                {% elif session.role == '会长' %}
                                    <li><a class="dropdown-item" href="{{ url_for('president.dashboard') }}">
                                        <i class="fas fa-tachometer-alt me-2"></i>会长后台
                                    </a></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="{{ url_for('member.dashboard') }}">
                                    <i class="fas fa-home me-2"></i>个人中心
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>退出登录
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.login') }}">
                                <i class="fas fa-sign-in-alt me-1"></i>登录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.register') }}">
                                <i class="fas fa-user-plus me-1"></i>注册
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 消息提示 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- 主要内容 -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="text-center">
                <h5 class="mb-3">学校社团管理系统</h5>
                <p class="text-muted mb-3">为校园社团提供全方位的数字化管理服务</p>
                <hr>
                <small class="text-muted">&copy; 2024 学校社团管理系统. 保留所有权利.</small>
            </div>
        </div>
    </footer>

    <!-- jQuery (本地) -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <!-- Bootstrap 5 JS (本地) -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
