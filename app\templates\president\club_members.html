{% extends "admin_base.html" %}

{% block title %}{{ club.ClubName }} - 成员管理 - 会长中心{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-1">
            <i class="fas fa-users me-2"></i>{{ club.ClubName }} - 成员管理
        </h4>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('president.dashboard') }}">会长中心</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('president.clubs') }}">我的社团</a></li>
                <li class="breadcrumb-item active">成员管理</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('president.club_detail', club_id=club.ClubID) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回社团详情
        </a>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="text-primary mb-2">
                    <i class="fas fa-users" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-1">{{ stats.total }}</h5>
                <small class="text-muted">总成员</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-1">{{ stats.approved }}</h5>
                <small class="text-muted">已批准</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="text-warning mb-2">
                    <i class="fas fa-clock" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-1">{{ stats.pending }}</h5>
                <small class="text-muted">待审批</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="text-danger mb-2">
                    <i class="fas fa-times-circle" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-1">{{ stats.rejected }}</h5>
                <small class="text-muted">已拒绝</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="text-secondary mb-2">
                    <i class="fas fa-sign-out-alt" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-1">{{ stats.withdrawn }}</h5>
                <small class="text-muted">已退出</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="text-info mb-2">
                    <i class="fas fa-percentage" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-1">
                    {% if club.MaxMembers %}
                        {{ "%.0f"|format((stats.approved / club.MaxMembers * 100)) }}%
                    {% else %}
                        --
                    {% endif %}
                </h5>
                <small class="text-muted">满员率</small>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">搜索成员</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search" name="search"
                           placeholder="输入姓名、用户名、手机号等" value="{{ search_query or '' }}">
                </div>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">状态筛选</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" {% if current_status == 'all' %}selected{% endif %}>全部状态</option>
                    <option value="已批准" {% if current_status == '已批准' %}selected{% endif %}>已批准</option>
                    <option value="待审批" {% if current_status == '待审批' %}selected{% endif %}>待审批</option>
                    <option value="已拒绝" {% if current_status == '已拒绝' %}selected{% endif %}>已拒绝</option>
                    <option value="已退出" {% if current_status == '已退出' %}selected{% endif %}>已退出</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{{ url_for('president.club_members', club_id=club.ClubID) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>重置
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 成员列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>成员列表
            <span class="badge bg-primary ms-2">{{ pagination.total }}人</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if members %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="20%">成员信息</th>
                        <th width="10%">状态</th>
                        <th width="15%">联系方式</th>
                        <th width="20%">学院/专业</th>
                        <th width="15%">申请时间</th>
                        <th width="20%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for member_club in members %}
                    {% set member = member_club.member %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    {{ member.Name[0] if member.Name else member.Username[0] }}
                                </div>
                                <div>
                                    <div class="fw-bold">{{ member.Name or '未设置' }}</div>
                                    <div class="text-muted small">@{{ member.Username }}</div>
                                    {% if member.Specialty %}
                                    <div class="text-muted small">
                                        <i class="fas fa-star me-1"></i>{{ member.Specialty }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if member_club.Status == '已批准' else 'warning' if member_club.Status == '待审批' else 'danger' if member_club.Status == '已拒绝' else 'secondary' }}">
                                {{ member_club.Status }}
                            </span>
                            {% if member_club.Status == '待审批' %}
                            <div class="small text-muted mt-1">
                                <i class="fas fa-clock me-1"></i>{{ member_club.ApplyTime.strftime('%m-%d %H:%M') }}
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <div class="small">
                                {% if member.Phone %}
                                <div><i class="fas fa-phone me-1"></i>{{ member.Phone }}</div>
                                {% endif %}
                                {% if member.Gender %}
                                <div><i class="fas fa-user me-1"></i>{{ member.Gender }}</div>
                                {% endif %}
                                {% if member.Age %}
                                <div><i class="fas fa-birthday-cake me-1"></i>{{ member.Age }}岁</div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                {% if member.College %}
                                <div><i class="fas fa-university me-1"></i>{{ member.College }}</div>
                                {% endif %}
                                {% if member.Dormitory %}
                                <div><i class="fas fa-home me-1"></i>{{ member.Dormitory }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                <div>{{ member_club.ApplyTime.strftime('%Y-%m-%d') }}</div>
                                <div class="text-muted">{{ member_club.ApplyTime.strftime('%H:%M:%S') }}</div>
                                {% if member_club.ApprovalTime %}
                                <div class="text-success mt-1">
                                    <i class="fas fa-check me-1"></i>{{ member_club.ApprovalTime.strftime('%m-%d %H:%M') }}
                                </div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                {% if member_club.Status == '待审批' %}
                                <button type="button" class="btn btn-outline-success"
                                        onclick="approveMember('{{ member_club.RecordID }}', '{{ member.Name or member.Username }}')"
                                        data-bs-toggle="tooltip" title="批准申请">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="rejectMember('{{ member_club.RecordID }}', '{{ member.Name or member.Username }}')"
                                        data-bs-toggle="tooltip" title="拒绝申请">
                                    <i class="fas fa-times"></i>
                                </button>
                                {% elif member_club.Status == '已批准' %}
                                <a href="{{ url_for('president.edit_member', club_id=club.ClubID, record_id=member_club.RecordID) }}"
                                   class="btn btn-outline-primary" data-bs-toggle="tooltip" title="编辑信息">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if member.MemberID != session.user_id %}
                                <button type="button" class="btn btn-outline-warning"
                                        onclick="removeMember('{{ member_club.RecordID }}', '{{ member.Name or member.Username }}')"
                                        data-bs-toggle="tooltip" title="移除成员">
                                    <i class="fas fa-user-minus"></i>
                                </button>
                                {% endif %}
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users text-muted" style="font-size: 4rem;"></i>
            <h5 class="mt-3 text-muted">暂无成员数据</h5>
            <p class="text-muted">
                {% if current_status != 'all' or search_query %}
                    没有找到符合条件的成员，请尝试调整搜索条件
                {% else %}
                    该社团暂无成员申请
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<nav aria-label="成员列表分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('president.club_members', club_id=club.ClubID, page=pagination.prev_num, search=search_query, status=current_status) }}">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        {% endif %}

        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('president.club_members', club_id=club.ClubID, page=page_num, search=search_query, status=current_status) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}

        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('president.club_members', club_id=club.ClubID, page=pagination.next_num, search=search_query, status=current_status) }}">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- 批准申请模态框 -->
<div class="modal fade" id="approveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle text-success me-2"></i>批准申请
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="approveForm" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <p>确定要批准 <strong id="approveMemberName"></strong> 的入会申请吗？</p>
                    <div class="mb-3">
                        <label for="approveComments" class="form-label">审批意见（可选）</label>
                        <textarea class="form-control" id="approveComments" name="comments" rows="3"
                                  placeholder="请输入审批意见..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-1"></i>批准申请
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 拒绝申请模态框 -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times-circle text-danger me-2"></i>拒绝申请
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <p>确定要拒绝 <strong id="rejectMemberName"></strong> 的入会申请吗？</p>
                    <div class="mb-3">
                        <label for="rejectComments" class="form-label">拒绝理由 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejectComments" name="comments" rows="3"
                                  placeholder="请输入拒绝理由..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-1"></i>拒绝申请
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 移除成员模态框 -->
<div class="modal fade" id="removeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-minus text-warning me-2"></i>移除成员
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="removeForm" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <p>确定要将 <strong id="removeMemberName"></strong> 从社团中移除吗？</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        移除后该成员将无法参与社团活动，此操作不可撤销。
                    </div>
                    <div class="mb-3">
                        <label for="removeReason" class="form-label">移除理由（可选）</label>
                        <textarea class="form-control" id="removeReason" name="reason" rows="3"
                                  placeholder="请输入移除理由..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-user-minus me-1"></i>确认移除
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 批准申请
function approveMember(recordId, memberName) {
    document.getElementById('approveMemberName').textContent = memberName;
    document.getElementById('approveForm').action =
        `{{ url_for('president.approve_member', club_id=club.ClubID, record_id='RECORD_ID') }}`.replace('RECORD_ID', recordId);

    var modal = new bootstrap.Modal(document.getElementById('approveModal'));
    modal.show();
}

// 拒绝申请
function rejectMember(recordId, memberName) {
    document.getElementById('rejectMemberName').textContent = memberName;
    document.getElementById('rejectForm').action =
        `{{ url_for('president.reject_member', club_id=club.ClubID, record_id='RECORD_ID') }}`.replace('RECORD_ID', recordId);

    var modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();
}

// 移除成员
function removeMember(recordId, memberName) {
    document.getElementById('removeMemberName').textContent = memberName;
    document.getElementById('removeForm').action =
        `{{ url_for('president.remove_member', club_id=club.ClubID, record_id='RECORD_ID') }}`.replace('RECORD_ID', recordId);

    var modal = new bootstrap.Modal(document.getElementById('removeModal'));
    modal.show();
}

// 搜索功能
$(document).ready(function() {
    // 搜索框自动提交
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            $('#search').closest('form').submit();
        }, 1000);
    });

    // 状态筛选自动提交
    $('#status').on('change', function() {
        $(this).closest('form').submit();
    });

    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<style>
.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}
</style>
{% endblock %}
