{% extends "base.html" %}

{% block title %}个人中心 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 欢迎区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">欢迎回来，{{ member.Name }}！</h2>
                            <p class="mb-0 opacity-75">
                                <i class="bi bi-person-badge me-2"></i>{{ member.Role }}
                                {% if member.College %}
                                <i class="bi bi-building ms-3 me-2"></i>{{ member.College }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex justify-content-end gap-2 flex-wrap">
                                <a href="{{ url_for('member.apply_create_club') }}" class="btn btn-warning">
                                    <i class="bi bi-plus-circle me-1"></i>申请创建社团
                                </a>
                                <a href="{{ url_for('common.clubs') }}" class="btn btn-light">
                                    <i class="bi bi-search me-1"></i>浏览社团
                                </a>
                                <a href="{{ url_for('common.index') }}" class="btn btn-outline-light">
                                    <i class="bi bi-house me-1"></i>返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-3">
                        <i class="bi bi-collection" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="mb-1">{{ stats.joined_count }}</h3>
                    <p class="text-muted mb-0">已加入社团</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-3">
                        <i class="bi bi-clock-history" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="mb-1">{{ stats.pending_count }}</h3>
                    <p class="text-muted mb-0">待审批申请</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-info mb-3">
                        <i class="bi bi-calendar-event" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="mb-1">{{ stats.activity_count }}</h3>
                    <p class="text-muted mb-0">即将参与活动</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 我的社团 -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-collection me-2 text-success"></i>我的社团
                        </h5>
                        <a href="{{ url_for('member.my_clubs') }}" class="btn btn-sm btn-outline-primary">
                            查看全部
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if joined_clubs %}
                        {% for mc in joined_clubs[:3] %}
                        <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                            <div class="flex-shrink-0">
                                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <i class="bi bi-collection"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ mc.club.ClubName }}</h6>
                                <p class="text-muted mb-1 small">{{ mc.club.Description }}</p>
                                <small class="text-success">
                                    <i class="bi bi-calendar me-1"></i>
                                    {{ mc.ApprovalTime.strftime('%Y年%m月%d日') if mc.ApprovalTime else '未知' }} 加入
                                </small>
                            </div>
                            <div class="flex-shrink-0">
                                <a href="{{ url_for('member.club_detail', club_id=mc.ClubID) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    查看
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-collection text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2 mb-3">您还没有加入任何社团</p>
                            <a href="{{ url_for('member.clubs') }}" class="btn btn-primary">
                                <i class="bi bi-search me-2"></i>浏览社团
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 待审批申请 -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2 text-warning"></i>待审批申请
                        </h5>
                        <a href="{{ url_for('member.my_clubs') }}" class="btn btn-sm btn-outline-primary">
                            查看全部
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if pending_applications %}
                        {% for mc in pending_applications[:3] %}
                        <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                            <div class="flex-shrink-0">
                                <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <i class="bi bi-clock-history"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ mc.club.ClubName }}</h6>
                                <p class="text-muted mb-1 small">{{ mc.club.Description }}</p>
                                <small class="text-warning">
                                    <i class="bi bi-calendar me-1"></i>
                                    {{ mc.ApplyTime.strftime('%Y年%m月%d日') }} 申请
                                </small>
                            </div>
                            <div class="flex-shrink-0">
                                <form method="POST" action="{{ url_for('member.cancel_application', club_id=mc.ClubID) }}" 
                                      class="d-inline" onsubmit="return confirm('确定要取消申请吗？')">
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        取消
                                    </button>
                                </form>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-check-circle text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">暂无待审批申请</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 即将参与的活动 -->
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-event me-2 text-info"></i>即将参与的活动
                        </h5>
                        <a href="{{ url_for('member.my_activities') }}" class="btn btn-sm btn-outline-primary">
                            查看全部
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if recent_activities %}
                        {% for activity in recent_activities %}
                        <div class="d-flex align-items-start mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                            <div class="flex-shrink-0">
                                <div class="bg-info text-white rounded d-flex align-items-center justify-content-center" 
                                     style="width: 60px; height: 60px;">
                                    <i class="bi bi-calendar-event"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ activity.ActivityName }}</h6>
                                <p class="text-muted mb-1 small">{{ activity.club.ClubName if activity.club else '未知社团' }}</p>
                                <div class="d-flex flex-wrap gap-3 small text-muted">
                                    <span>
                                        <i class="bi bi-calendar me-1"></i>
                                        {{ activity.StartTime.strftime('%m月%d日 %H:%M') }}
                                    </span>
                                    <span>
                                        <i class="bi bi-geo-alt me-1"></i>
                                        {{ activity.venue.VenueName if activity.venue else '待定' }}
                                    </span>
                                    <span class="badge bg-{{ loop.cycle('primary', 'success', 'warning', 'info') }}">
                                        {{ activity.ActivityType }}
                                    </span>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}"
                                       class="btn btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>详情
                                    </a>
                                    {% if activity.Status == '计划中' %}
                                    <button type="button" class="btn btn-primary"
                                            onclick="quickJoinActivity('{{ activity.ActivityID }}', '{{ activity.ActivityName }}')">
                                        <i class="fas fa-plus me-1"></i>快速报名
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">暂无即将参与的活动</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 推荐活动 -->
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2 text-warning"></i>推荐活动
                        </h5>
                        <a href="{{ url_for('common.activities') }}" class="btn btn-sm btn-outline-primary">
                            查看全部
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if recommended_activities %}
                        <div class="row">
                            {% for activity in recommended_activities %}
                            <div class="col-md-6 mb-3">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">{{ activity.ActivityName }}</h6>
                                            <span class="badge bg-{{ 'success' if activity.ActivityType == '讲座' else 'primary' if activity.ActivityType == '比赛' else 'info' if activity.ActivityType == '培训' else 'warning' }}">
                                                {{ activity.ActivityType }}
                                            </span>
                                        </div>
                                        <p class="text-muted small mb-2">{{ activity.club.ClubName if activity.club else '未知社团' }}</p>
                                        <div class="d-flex justify-content-between align-items-center small text-muted mb-3">
                                            <span>
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ activity.StartTime.strftime('%m月%d日 %H:%M') }}
                                            </span>
                                            <span>
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                {{ activity.venue.VenueName if activity.venue else '待定' }}
                                            </span>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}"
                                               class="btn btn-sm btn-outline-primary flex-fill">
                                                <i class="fas fa-eye me-1"></i>详情
                                            </a>
                                            {% if activity.Status == '计划中' %}
                                            <button type="button" class="btn btn-sm btn-primary flex-fill"
                                                    onclick="quickJoinActivity('{{ activity.ActivityID }}', '{{ activity.ActivityName }}')">
                                                <i class="fas fa-plus me-1"></i>报名
                                            </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-plus text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">暂无推荐活动</p>
                            <a href="{{ url_for('common.activities') }}" class="btn btn-outline-primary">
                                浏览所有活动
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

    </div>

    <!-- 个人资料管理 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="bi bi-person-gear me-2 text-primary"></i>个人资料管理
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('member.update_profile') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="name" class="form-label">真实姓名</label>
                                <input type="text" class="form-control" id="name" name="name"
                                       value="{{ member.Name or '' }}">
                            </div>
                            <div class="col-md-3">
                                <label for="phone" class="form-label">手机号码</label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       value="{{ member.Phone or '' }}">
                            </div>
                            <div class="col-md-3">
                                <label for="email" class="form-label">邮箱地址</label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="{{ member.Email or '' }}">
                            </div>
                            <div class="col-md-3">
                                <label for="college" class="form-label">学院</label>
                                <input type="text" class="form-control" id="college" name="college"
                                       value="{{ member.College or '' }}">
                            </div>
                            <div class="col-md-3">
                                <label for="specialty" class="form-label">专业</label>
                                <input type="text" class="form-control" id="specialty" name="specialty"
                                       value="{{ member.Specialty or '' }}">
                            </div>
                            <div class="col-md-3">
                                <label for="dormitory" class="form-label">宿舍</label>
                                <input type="text" class="form-control" id="dormitory" name="dormitory"
                                       value="{{ member.Dormitory or '' }}">
                            </div>
                            <div class="col-md-3">
                                <label for="gender" class="form-label">性别</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">请选择</option>
                                    <option value="男" {% if member.Gender == '男' %}selected{% endif %}>男</option>
                                    <option value="女" {% if member.Gender == '女' %}selected{% endif %}>女</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="age" class="form-label">年龄</label>
                                <input type="number" class="form-control" id="age" name="age"
                                       value="{{ member.Age or '' }}" min="16" max="100">
                            </div>
                        </div>

                        <div class="mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-1"></i>保存修改
                            </button>
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise me-1"></i>重置
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 快速报名活动
function quickJoinActivity(activityId, activityName) {
    if (confirm(`确定要报名参加"${activityName}"吗？`)) {
        // 显示加载状态
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>报名中...';
        button.disabled = true;

        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/member/join_activity/${activityId}`;

        // 添加CSRF令牌
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}

// 页面加载完成后的初始化
$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 添加卡片悬停效果
    $('.card').hover(
        function() {
            $(this).addClass('shadow-lg').removeClass('shadow-sm');
        },
        function() {
            $(this).addClass('shadow-sm').removeClass('shadow-lg');
        }
    );
});
</script>
{% endblock %}
