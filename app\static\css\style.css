/* 学校社团管理系统 - 前端样式 */

/* 全局样式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* 英雄区域样式 */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.hero-section h1 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* 卡片样式 */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 12px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    font-weight: 600;
}

/* 统计卡片样式 */
.stats-section .card {
    border: none;
    background: white;
}

.stats-section .card-body {
    padding: 2rem;
}

.stats-section h3 {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--dark-color);
}

/* 功能图标样式 */
.feature-icon {
    transition: transform 0.3s ease;
}

.feature-icon:hover {
    transform: scale(1.1);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

/* 标签样式 */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.8rem;
    border-radius: 6px;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 警告框样式 */
.alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 1rem;
}

/* 页脚样式 */
footer {
    margin-top: auto;
}

footer h5, footer h6 {
    color: var(--dark-color);
    font-weight: 600;
}

footer a {
    color: var(--secondary-color);
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-section {
        min-height: 50vh;
        text-align: center;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .stats-section .card-body {
        padding: 1.5rem;
    }
    
    .stats-section h3 {
        font-size: 2rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 社团卡片特殊样式 */
.club-card {
    overflow: hidden;
}

.club-card .card-header {
    position: relative;
    overflow: hidden;
}

.club-card .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.club-card:hover .card-header::before {
    left: 100%;
}

/* 活动卡片样式 */
.activity-card {
    border-left: 4px solid var(--primary-color);
}

.activity-card.type-讲座 {
    border-left-color: var(--info-color);
}

.activity-card.type-比赛 {
    border-left-color: var(--warning-color);
}

.activity-card.type-演出 {
    border-left-color: var(--danger-color);
}

.activity-card.type-展览 {
    border-left-color: var(--success-color);
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-left: 3rem;
}

.search-box .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    z-index: 10;
}

/* 过滤器样式 */
.filter-section {
    background: var(--light-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-section .form-select {
    border: 1px solid #dee2e6;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.active {
    background-color: var(--success-color);
}

.status-indicator.pending {
    background-color: var(--warning-color);
}

.status-indicator.inactive {
    background-color: var(--secondary-color);
}

.status-indicator.rejected {
    background-color: var(--danger-color);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 6px;
}

/* 模态框样式 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 12px 12px;
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    border-radius: 4px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
