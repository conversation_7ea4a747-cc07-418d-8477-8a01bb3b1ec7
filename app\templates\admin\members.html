{% extends "admin_base.html" %}

{% block title %}用户管理 - 管理后台{% endblock %}

{% block content %}
<!-- 搜索功能 -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">搜索用户</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search" name="search"
                           placeholder="输入姓名或用户名" value="{{ search_query or '' }}">
                </div>
            </div>
            <div class="col-md-3">
                <label for="role" class="form-label">角色筛选</label>
                <select class="form-select" id="role" name="role">
                    <option value="all" {% if current_role == 'all' %}selected{% endif %}>全部角色</option>
                    <option value="管理员" {% if current_role == '管理员' %}selected{% endif %}>管理员</option>
                    <option value="会长" {% if current_role == '会长' %}selected{% endif %}>会长</option>
                    <option value="会员" {% if current_role == '会员' %}selected{% endif %}>会员</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{{ url_for('admin.members') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>重置
                    </a>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('admin.create_member') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>添加用户
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 用户列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>用户列表
            <span class="badge bg-primary ms-2">{{ pagination.total }}人</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if members %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="5%">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th width="20%">用户信息</th>
                        <th width="10%">角色</th>
                        <th width="15%">联系方式</th>
                        <th width="20%">学院/专业</th>
                        <th width="15%">宿舍信息</th>
                        <th width="15%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for member in members %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input member-checkbox" 
                                   value="{{ member.MemberID }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    {{ member.Name[0] if member.Name else member.Username[0] }}
                                </div>
                                <div>
                                    <div class="fw-bold">{{ member.Name or '未设置' }}</div>
                                    <div class="text-muted small">@{{ member.Username }}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'danger' if member.Role == '管理员' else 'warning' if member.Role == '会长' else 'primary' }}">
                                {{ member.Role }}
                            </span>
                        </td>
                        <td>
                            <div class="small">
                                {% if member.Phone %}
                                <div><i class="fas fa-phone me-1"></i>{{ member.Phone }}</div>
                                {% endif %}
                                {% if member.Gender %}
                                <div><i class="fas fa-user me-1"></i>{{ member.Gender }}</div>
                                {% endif %}
                                {% if member.Age %}
                                <div><i class="fas fa-birthday-cake me-1"></i>{{ member.Age }}岁</div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                {% if member.College %}
                                <div><i class="fas fa-university me-1"></i>{{ member.College }}</div>
                                {% endif %}
                                {% if member.Specialty %}
                                <div><i class="fas fa-graduation-cap me-1"></i>{{ member.Specialty }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                {% if member.Dormitory %}
                                <div><i class="fas fa-home me-1"></i>{{ member.Dormitory }}</div>
                                {% else %}
                                <span class="text-muted">未设置</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('admin.edit_member', member_id=member.MemberID) }}"
                                   class="btn btn-outline-primary" data-bs-toggle="tooltip" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if member.Role != '管理员' %}
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="deleteMember('{{ member.MemberID }}', '{{ member.Name or member.Username }}')"
                                        data-bs-toggle="tooltip" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users text-muted" style="font-size: 4rem;"></i>
            <h5 class="mt-3 text-muted">暂无用户数据</h5>
            <p class="text-muted">点击上方"添加用户"按钮创建第一个用户</p>
            <a href="{{ url_for('admin.create_member') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>添加用户
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if pagination.pages > 1 %}
<nav aria-label="用户列表分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.members', page=pagination.prev_num, search=search_query, role=current_role) }}">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        {% endif %}

        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.members', page=page_num, search=search_query, role=current_role) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}

        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('admin.members', page=pagination.next_num, search=search_query, role=current_role) }}">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- 批量操作 -->
<div class="card border-0 shadow-sm mt-4" id="batchActions" style="display: none;">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <span class="text-muted">已选择 <span id="selectedCount">0</span> 个用户</span>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-warning" onclick="batchChangeRole()">
                    <i class="fas fa-user-tag me-1"></i>批量修改角色
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="batchDelete()">
                    <i class="fas fa-trash me-1"></i>批量删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 全选/取消全选
    $('#selectAll').change(function() {
        $('.member-checkbox').prop('checked', this.checked);
        updateBatchActions();
    });
    
    // 单个选择
    $('.member-checkbox').change(function() {
        updateBatchActions();
        
        // 更新全选状态
        var total = $('.member-checkbox').length;
        var checked = $('.member-checkbox:checked').length;
        $('#selectAll').prop('indeterminate', checked > 0 && checked < total);
        $('#selectAll').prop('checked', checked === total);
    });
    
    // 更新批量操作显示
    function updateBatchActions() {
        var selectedCount = $('.member-checkbox:checked').length;
        $('#selectedCount').text(selectedCount);
        
        if (selectedCount > 0) {
            $('#batchActions').show();
        } else {
            $('#batchActions').hide();
        }
    }
});

// 删除用户
function deleteMember(memberId, memberName) {
    if (confirm(`确定要删除用户"${memberName}"吗？此操作不可恢复。`)) {
        // 发送删除请求
        fetch(`/admin/members/${memberId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败：' + error.message);
        });
    }
}

// 批量删除
function batchDelete() {
    var selectedIds = $('.member-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要删除的用户');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${selectedIds.length} 个用户吗？此操作不可恢复。`)) {
        // 发送批量删除请求
        fetch('/admin/members/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({member_ids: selectedIds})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('批量删除失败：' + data.message);
            }
        })
        .catch(error => {
            alert('批量删除失败：' + error.message);
        });
    }
}

// 批量修改角色
function batchChangeRole() {
    var selectedIds = $('.member-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        alert('请先选择要修改的用户');
        return;
    }
    
    var newRole = prompt('请输入新角色（管理员/会长/会员）：');
    if (newRole && ['管理员', '会长', '会员'].includes(newRole)) {
        // 发送批量修改请求
        fetch('/admin/members/batch-change-role', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                member_ids: selectedIds,
                new_role: newRole
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('批量修改失败：' + data.message);
            }
        })
        .catch(error => {
            alert('批量修改失败：' + error.message);
        });
    }
}

// 导出Excel功能
function exportToExcel() {
    window.open('{{ url_for("admin.members") }}?export=excel');
}

// 搜索功能
$(document).ready(function() {
    // 搜索框自动提交
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            $('#search').closest('form').submit();
        }, 1000);
    });

    // 角色筛选自动提交
    $('#role').on('change', function() {
        $(this).closest('form').submit();
    });
});


</script>
{% endblock %}
