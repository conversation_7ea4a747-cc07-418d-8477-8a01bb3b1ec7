#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学校社团管理系统启动脚本
基于Flask 2.2.3 + MySQL数据库
"""

import os
from app import create_app

# 创建Flask应用实例
app = create_app()

if __name__ == '__main__':
    # 开发环境配置
    debug_mode = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    host = os.environ.get('FLASK_HOST', '127.0.0.1')
    port = int(os.environ.get('FLASK_PORT', 5000))

    app.run(
        host=host,
        port=port,
        debug=debug_mode,
        use_reloader=debug_mode
    )
