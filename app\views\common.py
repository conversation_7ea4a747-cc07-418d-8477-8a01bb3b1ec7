#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公共视图模块
处理首页、关于页面等公共路由
"""

from flask import Blueprint, render_template, request, session
from app.utils.helpers import get_current_user
from app.models.club import Club
from app.models.activity import Activity
from app.models.venue import Venue
from app.models.member import Member

# 创建公共蓝图
common_bp = Blueprint('common', __name__)

@common_bp.route('/')
def index():
    """
    系统首页
    根据用户角色显示不同内容或重定向到对应仪表板
    """
    user = get_current_user()
    
    # 所有用户都显示公共首页（包括已登录用户）
    # 管理员和会长可以通过导航栏访问各自的后台

    # 获取一些公开的统计信息
    stats = {
            'total_clubs': Club.query.filter_by(Status='活跃').count(),
            'total_activities': Activity.query.filter(
                Activity.Status.in_(['计划中', '进行中'])
            ).count(),
            'total_venues': Venue.query.count(),
            'total_members': Member.query.filter_by(Role='会员').count()
    }

    # 获取最新的活跃社团（前6个）
    recent_clubs = Club.query.filter_by(Status='活跃').order_by(
        Club.FoundationDate.desc()
    ).limit(6).all()

    # 获取即将举行的活动（前5个）
    from datetime import datetime
    upcoming_activities = Activity.query.filter(
        Activity.Status == '计划中',
        Activity.StartTime > datetime.now()
    ).order_by(Activity.StartTime.asc()).limit(5).all()

    return render_template('common/index.html',
                         stats=stats,
                         recent_clubs=recent_clubs,
                         upcoming_activities=upcoming_activities)

# 移除了about和contact路由，因为不需要这些页面

@common_bp.route('/clubs')
def clubs():
    """
    公开的社团列表页面
    显示所有活跃社团的基本信息
    """
    # 获取所有活跃社团
    clubs = Club.query.filter_by(Status='活跃').order_by(
        Club.FoundationDate.desc()
    ).all()
    
    # 按类别分组
    clubs_by_category = {}
    for club in clubs:
        category = club.Description
        if category not in clubs_by_category:
            clubs_by_category[category] = []
        clubs_by_category[category].append(club)
    
    return render_template('common/clubs.html', 
                         clubs=clubs,
                         clubs_by_category=clubs_by_category)

@common_bp.route('/club/<club_id>')
def club_detail(club_id):
    """
    社团详情页面
    显示社团的详细信息和最近活动
    """
    club = Club.query.get_or_404(club_id)

    # 获取社团最近的活动
    recent_activities = club.activities.filter(
        Activity.Status.in_(['计划中', '进行中', '已完成'])
    ).order_by(Activity.StartTime.desc()).limit(5).all()

    # 获取社团成员数统计
    member_count = club.member_relationships.filter_by(Status='已批准').count()

    # 获取社团成员列表（只显示已批准的成员）
    from app.models.member_club import MemberClub
    club_members = MemberClub.query.filter_by(
        ClubID=club_id,
        Status='已批准'
    ).order_by(MemberClub.ApprovalTime.desc()).limit(20).all()

    # 检查当前用户与该社团的关系状态
    user_club_status = None
    if session.get('user_id'):
        user_club_relation = MemberClub.query.filter_by(
            MemberID=session.get('user_id'),
            ClubID=club_id
        ).first()
        if user_club_relation:
            user_club_status = user_club_relation.Status

    return render_template('common/club_detail.html',
                         club=club,
                         recent_activities=recent_activities,
                         member_count=member_count,
                         club_members=club_members,
                         user_club_status=user_club_status)

@common_bp.route('/activities')
def activities():
    """
    公开的活动列表页面
    显示即将举行和最近完成的活动
    """
    from datetime import datetime
    
    # 即将举行的活动
    upcoming_activities = Activity.query.filter(
        Activity.Status == '计划中',
        Activity.StartTime > datetime.now()
    ).order_by(Activity.StartTime.asc()).limit(10).all()
    
    # 最近完成的活动
    recent_activities = Activity.query.filter(
        Activity.Status == '已完成'
    ).order_by(Activity.EndTime.desc()).limit(10).all()
    
    return render_template('common/activities.html',
                         upcoming_activities=upcoming_activities,
                         recent_activities=recent_activities)

@common_bp.route('/activity/<activity_id>')
def activity_detail(activity_id):
    """
    活动详情页面
    显示活动的详细信息和参与会员列表
    """
    activity = Activity.query.get_or_404(activity_id)

    # 获取该社团的已批准成员列表（作为活动的潜在参与者）
    from app.models.member_club import MemberClub
    club_members = MemberClub.query.filter_by(
        ClubID=activity.ClubID,
        Status='已批准'
    ).order_by(MemberClub.ApprovalTime.desc()).all()

    return render_template('common/activity_detail.html',
                         activity=activity,
                         club_members=club_members)

@common_bp.route('/venues')
def venues():
    """
    公开的场馆列表页面
    显示所有可用场馆信息
    """
    venues = Venue.query.order_by(Venue.VenueName.asc()).all()
    
    # 按类型分组
    venues_by_type = {}
    for venue in venues:
        venue_type = venue.VenueType
        if venue_type not in venues_by_type:
            venues_by_type[venue_type] = []
        venues_by_type[venue_type].append(venue)
    
    return render_template('common/venues.html',
                         venues=venues,
                         venues_by_type=venues_by_type)

@common_bp.route('/venue/<venue_id>')
def venue_detail(venue_id):
    """
    场馆详情页面
    显示场馆信息和最近的活动安排
    """
    venue = Venue.query.get_or_404(venue_id)
    
    # 获取场馆最近的活动
    recent_activities = venue.activities.filter(
        Activity.Status.in_(['计划中', '进行中', '已完成'])
    ).order_by(Activity.StartTime.desc()).limit(10).all()
    
    return render_template('common/venue_detail.html',
                         venue=venue,
                         recent_activities=recent_activities)

@common_bp.route('/search')
def search():
    """
    搜索页面
    支持搜索社团、活动、场馆
    """
    query = request.args.get('q', '').strip()
    search_type = request.args.get('type', 'all')  # all, clubs, activities, venues
    
    results = {
        'clubs': [],
        'activities': [],
        'venues': []
    }
    
    if query:
        if search_type in ['all', 'clubs']:
            # 搜索社团
            results['clubs'] = Club.query.filter(
                Club.Status == '活跃',
                Club.ClubName.contains(query)
            ).limit(20).all()
        
        if search_type in ['all', 'activities']:
            # 搜索活动
            results['activities'] = Activity.query.filter(
                Activity.ActivityName.contains(query)
            ).order_by(Activity.StartTime.desc()).limit(20).all()
        
        if search_type in ['all', 'venues']:
            # 搜索场馆
            results['venues'] = Venue.query.filter(
                Venue.VenueName.contains(query)
            ).limit(20).all()
    
    return render_template('common/search.html',
                         query=query,
                         search_type=search_type,
                         results=results)

# 移除了help、privacy、terms路由，因为不需要这些页面
