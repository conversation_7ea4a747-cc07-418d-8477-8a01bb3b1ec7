{% extends "base.html" %}

{% block title %}{{ venue.VenueName }} - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 返回按钮 -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ url_for('common.venues') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回场馆列表
            </a>
        </div>
    </div>

    <!-- 场馆详情 -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-building me-2"></i>{{ venue.VenueName }}
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">基本信息</h5>
                            <p><strong>场馆类型：</strong>{{ venue.VenueType }}</p>
                            <p><strong>场馆位置：</strong>{{ venue.Location }}</p>
                            {% if venue.Address %}
                            <p><strong>详细地址：</strong>{{ venue.Address }}</p>
                            {% endif %}
                            {% if venue.Capacity %}
                            <p><strong>容纳人数：</strong>{{ venue.Capacity }}人</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">联系信息</h5>
                            <p><strong>开放时间：</strong>{{ venue.AvailabTime }}</p>
                            {% if venue.ContactPhone %}
                            <p><strong>联系电话：</strong>
                                <a href="tel:{{ venue.ContactPhone }}" class="text-decoration-none">
                                    {{ venue.ContactPhone }}
                                </a>
                            </p>
                            {% endif %}
                        </div>
                    </div>

                    {% if session.user_id and session.role in ['会长', '管理员'] %}
                    <div class="text-center">
                        <a href="{{ url_for('member.book_venue', venue_id=venue.VenueID) }}" 
                           class="btn btn-primary btn-lg">
                            <i class="fas fa-calendar-plus me-2"></i>预订场馆
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>最近活动
                    </h5>
                </div>
                <div class="card-body">
                    {% for activity in recent_activities %}
                    <div class="mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                        <h6 class="mb-1">{{ activity.ActivityName }}</h6>
                        <p class="small text-muted mb-1">
                            <i class="fas fa-layer-group me-1"></i>{{ activity.club.ClubName if activity.club else '未知社团' }}
                        </p>
                        <p class="small text-muted mb-1">
                            <i class="fas fa-calendar me-1"></i>
                            {{ activity.StartTime.strftime('%Y-%m-%d %H:%M') if activity.StartTime else '时间待定' }}
                        </p>
                        <span class="badge bg-{{ 'warning' if activity.Status == '计划中' else 'primary' if activity.Status == '进行中' else 'success' if activity.Status == '已完成' else 'danger' }}">
                            {{ activity.Status }}
                        </span>
                    </div>
                    {% else %}
                    <div class="text-center py-3 text-muted">
                        <i class="fas fa-calendar-alt" style="font-size: 2rem;"></i>
                        <div class="mt-2">暂无活动记录</div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- 场馆使用统计 -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>使用统计
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">总活动数</span>
                            <strong>{{ recent_activities|length }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">场馆类型</span>
                            <strong>{{ venue.VenueType }}</strong>
                        </div>
                    </div>
                    {% if venue.Capacity %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">最大容量</span>
                            <strong>{{ venue.Capacity }}人</strong>
                        </div>
                    </div>
                    {% endif %}
                    <div class="mb-0">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">开放状态</span>
                            <span class="badge bg-success">正常开放</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
