{% extends "base.html" %}

{% block title %}{{ activity.ActivityName }} - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 返回按钮 -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ url_for('common.activities') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回活动列表
            </a>
        </div>
    </div>

    <!-- 活动详情 -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-{{ 'warning' if activity.Status == '计划中' else 'primary' if activity.Status == '进行中' else 'success' }} text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>{{ activity.ActivityName }}
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">基本信息</h5>
                            <p><strong>主办社团：</strong>{{ activity.club.ClubName if activity.club else '未知社团' }}</p>
                            <p><strong>活动类型：</strong>{{ activity.ActivityType }}</p>
                            <p><strong>活动状态：</strong>
                                <span class="badge bg-{{ 'warning' if activity.Status == '计划中' else 'primary' if activity.Status == '进行中' else 'success' if activity.Status == '已完成' else 'danger' }}">
                                    {{ activity.Status }}
                                </span>
                            </p>
                            {% if activity.ParticipantLimit %}
                            <p><strong>参与限制：</strong>{{ activity.ParticipantLimit }}人</p>
                            {% endif %}
                            {% if activity.ActualParticipant %}
                            <p><strong>实际参与：</strong>{{ activity.ActualParticipant }}人</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">时间地点</h5>
                            <p><strong>开始时间：</strong>{{ activity.StartTime.strftime('%Y年%m月%d日 %H:%M') if activity.StartTime else '时间待定' }}</p>
                            {% if activity.EndTime %}
                            <p><strong>结束时间：</strong>{{ activity.EndTime.strftime('%Y年%m月%d日 %H:%M') }}</p>
                            {% endif %}
                            <p><strong>活动场馆：</strong>{{ activity.venue.VenueName if activity.venue else '地点待定' }}</p>
                            {% if activity.venue and activity.venue.Address %}
                            <p><strong>详细地址：</strong>{{ activity.venue.Address }}</p>
                            {% endif %}
                        </div>
                    </div>

                    {% if activity.Description %}
                    <div class="mb-4">
                        <h5 class="text-primary mb-3">活动描述</h5>
                        <div class="bg-light p-3 rounded">
                            {{ activity.Description }}
                        </div>
                    </div>
                    {% endif %}

                    {% if session.user_id and activity.Status == '计划中' %}
                    <div class="text-center">
                        <form method="POST" action="{{ url_for('member.join_activity', activity_id=activity.ActivityID) }}" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus-circle me-2"></i>报名参加活动
                            </button>
                        </form>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>所有用户都可以报名参加活动
                            </small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 场馆信息 -->
            {% if activity.venue %}
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>场馆信息
                    </h5>
                </div>
                <div class="card-body">
                    <h6>{{ activity.venue.VenueName }}</h6>
                    <p class="text-muted mb-2">
                        <i class="fas fa-tag me-1"></i>{{ activity.venue.VenueType }}
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ activity.venue.Location }}
                    </p>
                    {% if activity.venue.Capacity %}
                    <p class="text-muted mb-2">
                        <i class="fas fa-users me-1"></i>容量：{{ activity.venue.Capacity }}人
                    </p>
                    {% endif %}
                    {% if activity.venue.ContactPhone %}
                    <p class="text-muted mb-0">
                        <i class="fas fa-phone me-1"></i>{{ activity.venue.ContactPhone }}
                    </p>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- 社团信息 -->
            {% if activity.club %}
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>主办社团
                    </h5>
                </div>
                <div class="card-body">
                    <h6>{{ activity.club.ClubName }}</h6>
                    <p class="text-muted mb-2">{{ activity.club.Description }}</p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-user me-1"></i>会长：{{ activity.club.president.Name if activity.club.president else '未知' }}
                    </p>
                    <p class="text-muted mb-3">
                        <i class="fas fa-users me-1"></i>成员：{{ activity.club.CurrentMembers }}/{{ activity.club.MaxMembers or '∞' }}
                    </p>
                    <a href="{{ url_for('common.club_detail', club_id=activity.club.ClubID) }}"
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>查看社团
                    </a>
                </div>
            </div>
            {% endif %}

            <!-- 社团成员列表 -->
            {% if club_members %}
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>社团成员
                        <span class="badge bg-primary ms-2">{{ club_members|length }}人</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if club_members|length > 0 %}
                        <div class="row">
                            {% for member_club in club_members %}
                            <div class="col-12 mb-2">
                                <div class="d-flex align-items-center p-2 border rounded">
                                    <div class="flex-shrink-0">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 36px; height: 36px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-0">{{ member_club.member.Name if member_club.member else '未知' }}</h6>
                                        <small class="text-muted">
                                            {{ member_club.member.College if member_club.member else '' }}
                                            {% if member_club.member and member_club.member.Specialty %}
                                            · {{ member_club.member.Specialty }}
                                            {% endif %}
                                        </small>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <small class="text-muted">
                                            {{ member_club.ApprovalTime.strftime('%Y-%m-%d') if member_club.ApprovalTime else '未知' }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-users" style="font-size: 2rem;"></i>
                            <div class="mt-2">暂无社团成员</div>
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
