#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Activities表数据模型
严格按照database.sql中的Activities表结构定义
社团活动全生命周期管理
"""

from app import db
from datetime import datetime
import uuid

class Activity(db.Model):
    """
    活动模型类 - 对应database.sql中的Activities表
    管理社团活动的申请、审批、执行、总结全流程
    """
    
    __tablename__ = 'Activities'
    
    # 主键：ActivityID CHAR(36) PRIMARY KEY
    ActivityID = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # ClubID CHAR(36) NOT NULL, FOREIGN KEY (ClubID) REFERENCES Clubs(ClubID)
    ClubID = db.Column(db.String(36), db.ForeignKey('Clubs.ClubID'), 
                      nullable=False, comment='所属社团ID')
    
    # ActivityName VARCHAR(100) NOT NULL
    ActivityName = db.Column(db.String(100), nullable=False, comment='活动名称')
    
    # ActivityType ENUM('讲座', '比赛', '培训', '展览', '演出', '会议', '其他') NOT NULL
    ActivityType = db.Column(db.Enum('讲座', '比赛', '培训', '展览', '演出', '会议', '其他'),
                            nullable=False, comment='活动类型')
    
    # StartTime DATETIME NOT NULL
    StartTime = db.Column(db.DateTime, nullable=False, comment='活动开始时间')
    
    # EndTime DATETIME NOT NULL
    EndTime = db.Column(db.DateTime, nullable=False, comment='活动结束时间')
    
    # Description VARCHAR(500)
    Description = db.Column(db.String(500), nullable=True, comment='活动描述')
    
    # OrganizerID CHAR(36) NOT NULL, FOREIGN KEY (OrganizerID) REFERENCES Members(MemberID)
    OrganizerID = db.Column(db.String(36), db.ForeignKey('Members.MemberID'), 
                           nullable=False, comment='活动组织者ID')
    
    # VenueID CHAR(36) NOT NULL, FOREIGN KEY (VenueID) REFERENCES Venues(VenueID)
    VenueID = db.Column(db.String(36), db.ForeignKey('Venues.VenueID'), 
                       nullable=False, comment='活动场馆ID')
    
    # ParticipantLimit INT CHECK (ParticipantLimit >= 0)
    ParticipantLimit = db.Column(db.Integer, nullable=True, comment='参与人数限制')
    
    # Status ENUM('待审批', '计划中', '进行中', '已完成', '已取消') NOT NULL
    Status = db.Column(db.Enum('待审批', '计划中', '进行中', '已完成', '已取消'),
                      nullable=False, default='待审批', comment='活动状态')
    
    # ActualParticipant INT NOT NULL
    ActualParticipant = db.Column(db.Integer, nullable=False, default=0, comment='实际参与人数')

    # 关系定义
    # 活动组织者（多对一关系）
    organizer = db.relationship('Member', foreign_keys=[OrganizerID],
                               overlaps="activities_as_organizer", lazy='select')

    # 注意：venue关系通过Venue模型的backref自动创建
    # 注意：club关系通过Club模型的backref自动创建

    def __init__(self, **kwargs):
        """初始化活动对象"""
        super(Activity, self).__init__(**kwargs)
        if not self.ActivityID:
            self.ActivityID = str(uuid.uuid4())
    
    def validate_time_constraint(self):
        """验证时间约束：EndTime > StartTime"""
        if self.StartTime and self.EndTime:
            return self.EndTime > self.StartTime
        return True
    
    def validate_participant_limit(self):
        """验证参与人数限制约束：ParticipantLimit >= 0"""
        if self.ParticipantLimit is not None:
            return self.ParticipantLimit >= 0
        return True
    
    def validate_actual_participant(self):
        """验证实际参与人数的合理性"""
        if self.ActualParticipant < 0:
            return False
        if self.ParticipantLimit is not None and self.ActualParticipant > self.ParticipantLimit:
            return False
        return True
    
    def check_venue_availability(self):
        """检查场馆可用性（排除当前活动）"""
        if self.venue:
            return self.venue.check_availability(
                self.StartTime, 
                self.EndTime, 
                exclude_activity_id=self.ActivityID
            )
        return False
    
    def get_conflicting_activities(self):
        """获取与当前活动时间冲突的其他活动"""
        if self.venue:
            return self.venue.get_conflicting_activities(
                self.StartTime, 
                self.EndTime, 
                exclude_activity_id=self.ActivityID
            )
        return []
    
    def can_be_cancelled(self):
        """判断活动是否可以取消"""
        # 只有计划中的活动可以取消
        return self.Status == '计划中'
    
    def can_be_started(self):
        """判断活动是否可以开始"""
        # 计划中的活动且开始时间已到
        return self.Status == '计划中' and datetime.now() >= self.StartTime
    
    def can_be_completed(self):
        """判断活动是否可以完成"""
        # 进行中的活动且结束时间已到
        return self.Status == '进行中' and datetime.now() >= self.EndTime
    
    def get_duration_hours(self):
        """获取活动持续时间（小时）"""
        if self.StartTime and self.EndTime:
            duration = self.EndTime - self.StartTime
            return duration.total_seconds() / 3600
        return 0
    
    def is_upcoming(self):
        """判断是否为即将举行的活动"""
        return self.Status == '计划中' and self.StartTime > datetime.now()
    
    def is_ongoing(self):
        """判断是否为正在进行的活动"""
        now = datetime.now()
        return (self.Status == '进行中' or 
                (self.Status == '计划中' and self.StartTime <= now <= self.EndTime))
    
    def is_completed(self):
        """判断是否为已完成的活动"""
        return self.Status == '已完成'
    
    def is_cancelled(self):
        """判断是否为已取消的活动"""
        return self.Status == '已取消'
    
    def get_participation_rate(self):
        """获取参与率"""
        if self.ParticipantLimit and self.ParticipantLimit > 0:
            return (self.ActualParticipant / self.ParticipantLimit) * 100
        return 0
    
    def update_status_by_time(self):
        """根据当前时间自动更新活动状态"""
        now = datetime.now()
        
        if self.Status == '计划中':
            if now >= self.StartTime:
                if now <= self.EndTime:
                    self.Status = '进行中'
                else:
                    # 活动已过期但未手动完成，自动标记为已完成
                    self.Status = '已完成'
        elif self.Status == '进行中':
            if now > self.EndTime:
                self.Status = '已完成'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'ActivityID': self.ActivityID,
            'ClubID': self.ClubID,
            'ActivityName': self.ActivityName,
            'ActivityType': self.ActivityType,
            'StartTime': self.StartTime.isoformat() if self.StartTime else None,
            'EndTime': self.EndTime.isoformat() if self.EndTime else None,
            'Description': self.Description,
            'OrganizerID': self.OrganizerID,
            'VenueID': self.VenueID,
            'ParticipantLimit': self.ParticipantLimit,
            'Status': self.Status,
            'ActualParticipant': self.ActualParticipant,
            'ClubName': self.club.ClubName if self.club else None,
            'OrganizerName': self.organizer.Name if self.organizer else None,
            'VenueName': self.venue.VenueName if self.venue else None,
            'Duration': self.get_duration_hours()
        }
    
    def __repr__(self):
        return f'<Activity {self.ActivityName}({self.Status})>'
