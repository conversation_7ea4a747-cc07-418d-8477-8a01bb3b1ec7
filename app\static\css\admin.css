/* 管理后台样式 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
    padding-top: 56px; /* 为固定导航栏留出空间 */
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
}

.sidebar .nav-link {
    color: #333;
    font-weight: 500;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: #0d6efd;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* 主内容区域 */
main {
    margin-left: 0;
    padding: 20px;
}

@media (min-width: 768px) {
    main {
        margin-left: 240px;
    }
}

/* 页面标题 */
.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-card .stats-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stats-card .stats-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* 数据表格 */
.data-table {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.data-table .table {
    margin-bottom: 0;
}

.data-table .table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.data-table .table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.data-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 操作按钮 */
.action-buttons .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

/* 状态标签 */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.approved {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 搜索和过滤器 */
.search-filter-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-filter-section .form-control,
.search-filter-section .form-select {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
}

/* 分页 */
.pagination-wrapper {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.pagination .page-link {
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    border: 1px solid #dee2e6;
    color: #6c757d;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 图表容器 */
.chart-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chart-container h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* 表单样式 */
.form-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-container .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-container .form-control,
.form-container .form-select,
.form-container .form-check-input {
    border-radius: 0.5rem;
}

.form-container .form-control:focus,
.form-container .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 警告框 */
.alert {
    border-radius: 0.75rem;
    border: none;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
}

.alert .alert-heading {
    font-weight: 600;
}

/* 模态框 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
    background-color: #f8f9fa;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.3rem solid rgba(255, 255, 255, 0.3);
    border-top: 0.3rem solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 1rem 0;
    }
    
    main {
        margin-left: 0;
        padding: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .data-table {
        font-size: 0.875rem;
    }
    
    .data-table .table thead th,
    .data-table .table tbody td {
        padding: 0.75rem 0.5rem;
    }
    
    .action-buttons .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination-wrapper {
        display: none !important;
    }
    
    main {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .data-table,
    .chart-container,
    .form-container {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .sidebar {
        background-color: #2d3748;
    }
    
    .sidebar .nav-link {
        color: #e9ecef;
    }
    
    .sidebar .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .data-table,
    .search-filter-section,
    .pagination-wrapper,
    .chart-container,
    .form-container {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .data-table .table thead th {
        background-color: #4a5568;
        color: #e9ecef;
    }
}
