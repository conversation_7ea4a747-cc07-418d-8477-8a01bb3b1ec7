/* 管理后台样式 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
    padding-top: 56px; /* 为固定导航栏留出空间 */
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
}

.sidebar .nav-link {
    color: #333;
    font-weight: 500;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: #0d6efd;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* 主内容区域 */
main {
    margin-left: 0;
    padding: 20px;
}

@media (min-width: 768px) {
    main {
        margin-left: 240px;
    }
}

/* 页面标题 */
.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-card .stats-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stats-card .stats-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* 数据表格 */
.data-table {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.data-table .table {
    margin-bottom: 0;
}

.data-table .table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.data-table .table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.data-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 操作按钮 */
.action-buttons .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

/* 状态标签 */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.approved {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 搜索和过滤器 */
.search-filter-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-filter-section .form-control,
.search-filter-section .form-select {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
}

/* 分页 */
.pagination-wrapper {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.pagination .page-link {
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    border: 1px solid #dee2e6;
    color: #6c757d;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 图表容器 */
.chart-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chart-container h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* 表单样式 */
.form-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-container .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-container .form-control,
.form-container .form-select,
.form-container .form-check-input {
    border-radius: 0.5rem;
}

.form-container .form-control:focus,
.form-container .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 警告框 */
.alert {
    border-radius: 0.75rem;
    border: none;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
}

.alert .alert-heading {
    font-weight: 600;
}

/* 模态框 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
    background-color: #f8f9fa;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.3rem solid rgba(255, 255, 255, 0.3);
    border-top: 0.3rem solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 1rem 0;
    }
    
    main {
        margin-left: 0;
        padding: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .data-table {
        font-size: 0.875rem;
    }
    
    .data-table .table thead th,
    .data-table .table tbody td {
        padding: 0.75rem 0.5rem;
    }
    
    .action-buttons .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination-wrapper {
        display: none !important;
    }
    
    main {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .data-table,
    .chart-container,
    .form-container {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .sidebar {
        background-color: #2d3748;
    }
    
    .sidebar .nav-link {
        color: #e9ecef;
    }
    
    .sidebar .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .data-table,
    .search-filter-section,
    .pagination-wrapper,
    .chart-container,
    .form-container {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .data-table .table thead th {
        background-color: #4a5568;
        color: #e9ecef;
    }
}

/* ========== 审批页面专用样式 ========== */

/* 审批统计卡片增强 */
.approval-stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.approval-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.approval-stats-card:hover::before {
    opacity: 1;
}

.approval-stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.approval-stats-card.pending {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.approval-stats-card.approved {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.approval-stats-card.rejected {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.approval-stats-card.total {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 审批表格增强 */
.approval-table {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    border: none;
}

.approval-table .table {
    margin-bottom: 0;
}

.approval-table .table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 1.25rem 1rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.approval-table .table thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.approval-table .table tbody tr {
    transition: all 0.3s ease;
    border: none;
}

.approval-table .table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.approval-table .table tbody td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
    position: relative;
}

/* 用户头像美化 */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.user-avatar:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

/* 状态徽章增强 */
.status-badge-enhanced {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.status-badge-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.status-badge-enhanced:hover::before {
    left: 100%;
}

.status-badge-enhanced.pending {
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
    color: #d63031;
}

.status-badge-enhanced.approved {
    background: linear-gradient(135deg, #55efc4 0%, #00b894 100%);
    color: #00695c;
}

.status-badge-enhanced.rejected {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: #ad1457;
}

/* 操作按钮增强 */
.action-btn-enhanced {
    border: none;
    border-radius: 8px;
    padding: 0.5rem;
    margin: 0 0.125rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.action-btn-enhanced::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.action-btn-enhanced:hover::before {
    width: 100px;
    height: 100px;
}

.action-btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.action-btn-enhanced.view {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
}

.action-btn-enhanced.approve {
    background: linear-gradient(135deg, #55efc4 0%, #00b894 100%);
    color: white;
}

.action-btn-enhanced.reject {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
}

/* 搜索筛选区域增强 */
.approval-search-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: none;
}

.approval-search-section .form-control,
.approval-search-section .form-select {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.approval-search-section .form-control:focus,
.approval-search-section .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
    transform: translateY(-1px);
}

.approval-search-section .input-group-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    border-radius: 12px 0 0 12px;
}

/* 批量操作区域 */
.batch-actions-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 1.5rem;
    margin-top: 1.5rem;
    color: white;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
    transform: translateY(10px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.batch-actions-enhanced.show {
    transform: translateY(0);
    opacity: 1;
}

.batch-actions-enhanced .btn {
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.batch-actions-enhanced .btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-2px);
}

/* 分页增强 */
.pagination-enhanced {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    margin-top: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.pagination-enhanced .pagination {
    margin-bottom: 0;
    justify-content: center;
}

.pagination-enhanced .page-link {
    border: none;
    border-radius: 12px;
    margin: 0 0.25rem;
    padding: 0.75rem 1rem;
    color: #6c757d;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.pagination-enhanced .page-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination-enhanced .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 加载状态增强 */
.loading-enhanced {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner-enhanced {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin-enhanced 1s linear infinite;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

@keyframes spin-enhanced {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态增强 */
.empty-state-enhanced {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state-enhanced i {
    font-size: 5rem;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.empty-state-enhanced h5 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #495057;
}

.empty-state-enhanced p {
    font-size: 1.1rem;
    opacity: 0.8;
}

/* 审批类型徽章 */
.request-type-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.request-type-badge.club-creation {
    background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
    color: white;
}

.request-type-badge.activity-application {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
}

.request-type-badge.president-appointment {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: white;
}

.request-type-badge.club-dissolution {
    background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
    color: white;
}

.request-type-badge.membership {
    background: linear-gradient(135deg, #00cec9 0%, #00b894 100%);
    color: white;
}

/* 优先级指示器 */
.priority-indicator {
    width: 4px;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 0 4px 4px 0;
}

.priority-indicator.high {
    background: linear-gradient(180deg, #e17055 0%, #d63031 100%);
}

.priority-indicator.normal {
    background: linear-gradient(180deg, #fdcb6e 0%, #e17055 100%);
}

.priority-indicator.low {
    background: linear-gradient(180deg, #00b894 0%, #00cec9 100%);
}

/* 审批页面响应式设计 */
@media (max-width: 991.98px) {
    .approval-stats-card {
        margin-bottom: 1rem;
    }

    .approval-search-section {
        padding: 1.5rem;
    }

    .approval-table .table thead th,
    .approval-table .table tbody td {
        padding: 1rem 0.75rem;
        font-size: 0.875rem;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }

    .action-btn-enhanced {
        padding: 0.375rem;
        margin: 0 0.0625rem;
    }
}

@media (max-width: 767.98px) {
    .approval-search-section {
        padding: 1rem;
    }

    .approval-search-section .row > div {
        margin-bottom: 1rem;
    }

    .approval-table {
        font-size: 0.8rem;
    }

    .approval-table .table thead th,
    .approval-table .table tbody td {
        padding: 0.75rem 0.5rem;
    }

    .batch-actions-enhanced {
        padding: 1rem;
    }

    .batch-actions-enhanced .btn {
        padding: 0.5rem 1rem;
        margin: 0.25rem;
        font-size: 0.875rem;
    }

    .pagination-enhanced {
        padding: 1rem;
    }

    .pagination-enhanced .page-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* 表格行动画延迟 */
.approval-table .table tbody tr:nth-child(1) { animation-delay: 0.1s; }
.approval-table .table tbody tr:nth-child(2) { animation-delay: 0.2s; }
.approval-table .table tbody tr:nth-child(3) { animation-delay: 0.3s; }
.approval-table .table tbody tr:nth-child(4) { animation-delay: 0.4s; }
.approval-table .table tbody tr:nth-child(5) { animation-delay: 0.5s; }
