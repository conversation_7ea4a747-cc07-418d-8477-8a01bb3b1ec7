{% extends "admin_base.html" %}

{% block title %}管理员仪表板{% endblock %}

{% block page_title %}管理仪表板{% endblock %}

{% block breadcrumb_title %}管理仪表板{% endblock %}

{% block page_actions %}{% endblock %}

{% block content %}


<div class="row">
    <!-- 待审批申请 -->
    <div class="col-12 mb-4">
        <div class="data-table">
            <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle me-2 text-warning"></i>待审批申请
                </h5>
                <a href="{{ url_for('admin.approvals') }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>申请类型</th>
                            <th>申请人</th>
                            <th>申请时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for approval in pending_approvals %}
                        <tr>
                            <td>
                                <span class="badge bg-{{ loop.cycle('primary', 'success', 'warning', 'info') }}">
                                    {{ approval.RequestType }}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2" 
                                         style="width: 32px; height: 32px; font-size: 0.8rem;">
                                        {{ approval.applicant.Name[0] if approval.applicant else '?' }}
                                    </div>
                                    {{ approval.applicant.Name if approval.applicant else '未知用户' }}
                                </div>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ approval.RequestTime.strftime('%Y-%m-%d %H:%M') if approval.RequestTime else '未知' }}
                                </small>
                            </td>
                            <td>
                                <span class="status-badge pending">{{ approval.get_status_display() }}</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <form method="POST" action="{{ url_for('admin.approve_request', request_id=approval.RequestID) }}"
                                          class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                        <button type="submit" class="btn btn-success btn-sm"
                                                onclick="return confirm('确定要批准这个申请吗？')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                    <form method="POST" action="{{ url_for('admin.reject_request', request_id=approval.RequestID) }}"
                                          class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                        <button type="submit" class="btn btn-danger btn-sm"
                                                onclick="return confirm('确定要拒绝这个申请吗？')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center py-4 text-muted">
                                <i class="fas fa-check-circle" style="font-size: 2rem;"></i>
                                <div class="mt-2">暂无待审批申请</div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>


</div>

<div class="row">
    <!-- 最近注册用户 -->
    <div class="col-lg-6 mb-4">
        <div class="data-table">
            <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2 text-success"></i>最近注册用户
                </h5>
                <a href="{{ url_for('admin.members') }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="p-3">
                {% for member in recent_members %}
                <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 40px; height: 40px;">
                        {{ member.Name[0] if member.Name else '?' }}
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ member.Name }}</h6>
                        <small class="text-muted">{{ member.Username }} - {{ member.Role }}</small>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">刚刚注册</small>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-3 text-muted">
                    <i class="fas fa-user" style="font-size: 2rem;"></i>
                    <div class="mt-2">暂无新用户</div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 最近创建社团 -->
    <div class="col-lg-6 mb-4">
        <div class="data-table">
            <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-layer-group me-2 text-primary"></i>最近创建社团
                </h5>
                <a href="{{ url_for('admin.clubs') }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="p-3">
                {% for club in recent_clubs %}
                <div class="d-flex align-items-center mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                    <div class="bg-success text-white rounded d-flex align-items-center justify-content-center me-3"
                         style="width: 40px; height: 40px;">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ club.ClubName }}</h6>
                        <small class="text-muted">
                            {{ club.Description }} - 
                            <span class="status-badge {{ 'active' if club.Status == '活跃' else 'inactive' }}">
                                {{ club.Status }}
                            </span>
                        </small>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">
                            {{ club.FoundationDate.strftime('%m-%d') if club.FoundationDate else '未知' }}
                        </small>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-3 text-muted">
                    <i class="fas fa-layer-group" style="font-size: 2rem;"></i>
                    <div class="mt-2">暂无新社团</div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- 即将举行的活动 -->
{% if upcoming_activities %}
<div class="row">
    <div class="col-12">
        <div class="data-table">
            <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2 text-warning"></i>即将举行的活动
                </h5>
                <a href="{{ url_for('admin.activities') }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>活动名称</th>
                            <th>社团</th>
                            <th>类型</th>
                            <th>开始时间</th>
                            <th>场馆</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for activity in upcoming_activities %}
                        <tr>
                            <td>
                                <strong>{{ activity.ActivityName }}</strong>
                            </td>
                            <td>{{ activity.club.ClubName if activity.club else '未知社团' }}</td>
                            <td>
                                <span class="badge bg-{{ loop.cycle('primary', 'success', 'warning', 'info') }}">
                                    {{ activity.ActivityType }}
                                </span>
                            </td>
                            <td>
                                <small>{{ activity.StartTime.strftime('%m-%d %H:%M') if activity.StartTime else '未知' }}</small>
                            </td>
                            <td>{{ activity.venue.VenueName if activity.venue else '待定' }}</td>
                            <td>
                                <span class="status-badge {{ 'active' if activity.Status == '计划中' else 'pending' }}">
                                    {{ activity.Status }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 自动刷新待审批数量
    setInterval(function() {
        $.get('{{ url_for("admin.api_stats") }}')
        .done(function(data) {
            if (data.approval_stats && data.approval_stats.pending_requests !== undefined) {
                $('.stats-card.info .stats-number').text(data.approval_stats.pending_requests);
            }
        });
    }, 30000); // 30秒刷新一次
});
</script>
{% endblock %}
