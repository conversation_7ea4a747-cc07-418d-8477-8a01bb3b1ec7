{% extends "admin_base.html" %}

{% block title %}创建活动 - 管理后台{% endblock %}
{% block page_title %}创建活动{% endblock %}
{% block title_icon %}<i class="fas fa-calendar-plus"></i>{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-nav">
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.dashboard') }}">
                <i class="fas fa-home"></i>
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.activities') }}">活动管理</a>
        </li>
        <li class="breadcrumb-item active">创建活动</li>
    </ol>
</nav>
{% endblock %}

{% block page_actions %}
<div class="d-flex gap-2">
    <a href="{{ url_for('admin.activities') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回列表
    </a>
</div>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>活动基本信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.create_activity') }}" id="createActivityForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <!-- 基本信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-info-circle me-1"></i>基本信息
                            </h6>
                        </div>
                        <div class="col-md-8">
                            <label for="activity_name" class="form-label">活动名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="activity_name" name="activity_name" 
                                   value="{{ request.form.get('activity_name', '') }}" required maxlength="100">
                        </div>
                        <div class="col-md-4">
                            <label for="activity_type" class="form-label">活动类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="activity_type" name="activity_type" required>
                                <option value="">请选择类型</option>
                                <option value="讲座" {% if request.form.get('activity_type') == '讲座' %}selected{% endif %}>讲座</option>
                                <option value="比赛" {% if request.form.get('activity_type') == '比赛' %}selected{% endif %}>比赛</option>
                                <option value="培训" {% if request.form.get('activity_type') == '培训' %}selected{% endif %}>培训</option>
                                <option value="展览" {% if request.form.get('activity_type') == '展览' %}selected{% endif %}>展览</option>
                                <option value="演出" {% if request.form.get('activity_type') == '演出' %}selected{% endif %}>演出</option>
                                <option value="会议" {% if request.form.get('activity_type') == '会议' %}selected{% endif %}>会议</option>
                                <option value="其他" {% if request.form.get('activity_type') == '其他' %}selected{% endif %}>其他</option>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label for="description" class="form-label">活动描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      maxlength="500">{{ request.form.get('description', '') }}</textarea>
                            <div class="form-text">
                                <span id="desc-count">0</span>/500 字符
                            </div>
                        </div>
                    </div>

                    <!-- 主办方信息 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-users me-1"></i>主办方信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="club_id" class="form-label">主办社团 <span class="text-danger">*</span></label>
                            <select class="form-select" id="club_id" name="club_id" required>
                                <option value="">请选择主办社团</option>
                                {% for club in clubs %}
                                <option value="{{ club.ClubID }}" {% if request.form.get('club_id') == club.ClubID %}selected{% endif %}>
                                    {{ club.ClubName }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="organizer_display" class="form-label">活动组织者 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="organizer_display" name="organizer_display"
                                   value="" readonly placeholder="请先选择主办社团">
                            <input type="hidden" id="organizer_id" name="organizer_id" value="">
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>组织者将自动设置为所选社团的会长
                            </div>
                        </div>
                    </div>

                    <!-- 时间地点 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>时间地点
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="start_time" class="form-label">开始时间 <span class="text-danger">*</span></label>
                            <input type="datetime-local" class="form-control" id="start_time" name="start_time"
                                   value="{{ request.form.get('start_time', '') }}" required>
                            <div class="form-text">
                                <i class="fas fa-clock me-1"></i>默认为当前时间
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="end_time" class="form-label">结束时间 <span class="text-danger">*</span></label>
                            <input type="datetime-local" class="form-control" id="end_time" name="end_time"
                                   value="{{ request.form.get('end_time', '') }}" required>
                            <div class="form-text">
                                <i class="fas fa-clock me-1"></i>默认为开始时间+3小时
                            </div>
                        </div>
                        <div class="col-md-12">
                            <label for="venue_id" class="form-label">活动场馆 <span class="text-danger">*</span></label>
                            <select class="form-select" id="venue_id" name="venue_id" required>
                                <option value="">请选择场馆</option>
                                {% for venue in venues %}
                                <option value="{{ venue.VenueID }}" {% if request.form.get('venue_id') == venue.VenueID %}selected{% endif %}>
                                    {{ venue.VenueName }} (容量: {{ venue.Capacity or '未知' }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- 参与设置 -->
                    <div class="row g-3 mb-4">
                        <div class="col-12">
                            <h6 class="text-muted border-bottom pb-2">
                                <i class="fas fa-cog me-1"></i>参与设置
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <label for="participant_limit" class="form-label">参与人数限制</label>
                            <input type="number" class="form-control" id="participant_limit" name="participant_limit"
                                   value="{{ request.form.get('participant_limit', '') }}" min="0" max="10000">
                            <div class="form-text">留空或0表示不限制人数</div>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">活动状态 <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="计划中" {% if request.form.get('status') == '计划中' %}selected{% endif %}>计划中</option>
                                <option value="进行中" {% if request.form.get('status') == '进行中' %}selected{% endif %}>进行中</option>
                                <option value="已完成" {% if request.form.get('status') == '已完成' %}selected{% endif %}>已完成</option>
                                <option value="已取消" {% if request.form.get('status') == '已取消' %}selected{% endif %}>已取消</option>
                            </select>
                        </div>
                    </div>



                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('admin.activities') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="reset" class="btn btn-outline-warning">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>创建活动
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 字符计数
    function setupCharCounter(fieldId, counterId) {
        $(`#${fieldId}`).on('input', function() {
            const count = $(this).val().length;
            $(`#${counterId}`).text(count);
        });
        $(`#${fieldId}`).trigger('input');
    }
    
    setupCharCounter('description', 'desc-count');

    // 设置默认时间值
    function setDefaultTimes() {
        const now = new Date();
        const startTime = new Date(now.getTime());
        const endTime = new Date(now.getTime() + 3 * 60 * 60 * 1000); // +3小时

        // 格式化为 YYYY-MM-DDTHH:MM
        const formatDateTime = (date) => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        };

        // 只在字段为空时设置默认值
        if (!$('#start_time').val()) {
            $('#start_time').val(formatDateTime(startTime));
        }
        if (!$('#end_time').val()) {
            $('#end_time').val(formatDateTime(endTime));
        }
    }

    // 页面加载时设置默认时间
    setDefaultTimes();

    // 主办社团选择变化时获取会长信息
    $('#club_id').on('change', function() {
        const clubId = $(this).val();
        const organizerDisplay = $('#organizer_display');
        const organizerId = $('#organizer_id');

        if (!clubId) {
            organizerDisplay.val('').attr('placeholder', '请先选择主办社团');
            organizerId.val('');
            return;
        }

        // 显示加载状态
        organizerDisplay.val('正在获取会长信息...').attr('placeholder', '');

        // 发送AJAX请求获取会长信息
        $.ajax({
            url: `/admin/api/club/${clubId}/president`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    organizerDisplay.val(`${response.president.name} (${response.president.role})`);
                    organizerId.val(response.president.id);
                } else {
                    organizerDisplay.val('').attr('placeholder', '获取会长信息失败');
                    organizerId.val('');
                    alert('获取会长信息失败：' + response.message);
                }
            },
            error: function(xhr, status, error) {
                organizerDisplay.val('').attr('placeholder', '获取会长信息失败');
                organizerId.val('');
                alert('获取会长信息失败，请重试');
            }
        });
    });

    // 开始时间变化时自动调整结束时间
    $('#start_time').on('change', function() {
        const startTimeValue = $(this).val();
        if (startTimeValue) {
            const startTime = new Date(startTimeValue);
            const endTime = new Date(startTime.getTime() + 3 * 60 * 60 * 1000); // +3小时

            const formatDateTime = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                return `${year}-${month}-${day}T${hours}:${minutes}`;
            };

            $('#end_time').val(formatDateTime(endTime));
        }
    });

    // 时间验证
    $('#end_time').on('change', function() {
        const startTime = new Date($('#start_time').val());
        const endTime = new Date($(this).val());

        if (startTime && endTime && endTime <= startTime) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">结束时间必须晚于开始时间</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // 表单提交验证
    $('#createActivityForm').on('submit', function(e) {
        // 检查是否选择了组织者
        if (!$('#organizer_id').val()) {
            e.preventDefault();
            alert('请先选择主办社团以自动设置活动组织者');
            $('#club_id').focus();
            return;
        }

        const invalidFields = $(this).find('.is-invalid');
        if (invalidFields.length > 0) {
            e.preventDefault();
            alert('请先修正表单中的错误');
            invalidFields.first().focus();
        }
    });
});
</script>
{% endblock %}
