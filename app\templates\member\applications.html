{% extends "base.html" %}

{% block title %}我的申请 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="fas fa-file-alt me-2 text-primary"></i>我的申请
            </h2>
            <p class="text-muted">查看和管理您提交的所有申请</p>
        </div>
    </div>

    <!-- 申请统计 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.pending }}</h4>
                    <p class="text-muted mb-0">待审批</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-check fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.approved }}</h4>
                    <p class="text-muted mb-0">已通过</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-danger mb-2">
                        <i class="fas fa-times fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.rejected }}</h4>
                    <p class="text-muted mb-0">已拒绝</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ stats.total }}</h4>
                    <p class="text-muted mb-0">总申请数</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速申请 -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0">
                <i class="fas fa-plus me-2 text-success"></i>快速申请
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <a href="{{ url_for('member.apply_club') }}" class="btn btn-outline-primary w-100">
                        <i class="fas fa-layer-group me-2"></i>申请加入社团
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('member.apply_activity') }}" class="btn btn-outline-success w-100">
                        <i class="fas fa-calendar-plus me-2"></i>申请参加活动
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('member.apply_venue') }}" class="btn btn-outline-warning w-100">
                        <i class="fas fa-building me-2"></i>申请场馆使用
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('member.apply_other') }}" class="btn btn-outline-info w-100">
                        <i class="fas fa-file-plus me-2"></i>其他申请
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">搜索申请</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search" name="search" 
                               placeholder="输入申请内容关键词" value="{{ request.args.get('search', '') }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">状态筛选</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {% if current_status == 'all' %}selected{% endif %}>全部状态</option>
                        <option value="待批" {% if current_status == '待批' %}selected{% endif %}>待审批</option>
                        <option value="已批准" {% if current_status == '已批准' %}selected{% endif %}>已通过</option>
                        <option value="已拒绝" {% if current_status == '已拒绝' %}selected{% endif %}>已拒绝</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="request_type" class="form-label">类型筛选</label>
                    <select class="form-select" id="request_type" name="request_type">
                        <option value="all" {% if current_type == 'all' %}selected{% endif %}>全部类型</option>
                        <option value="社团申请" {% if current_type == '社团申请' %}selected{% endif %}>社团申请</option>
                        <option value="活动申请" {% if current_type == '活动申请' %}selected{% endif %}>活动申请</option>
                        <option value="场馆申请" {% if current_type == '场馆申请' %}selected{% endif %}>场馆申请</option>
                        <option value="其他申请" {% if current_type == '其他申请' %}selected{% endif %}>其他申请</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_range" class="form-label">时间范围</label>
                    <select class="form-select" id="date_range" name="date_range">
                        <option value="all" {% if current_date_range == 'all' %}selected{% endif %}>全部时间</option>
                        <option value="week" {% if current_date_range == 'week' %}selected{% endif %}>本周</option>
                        <option value="month" {% if current_date_range == 'month' %}selected{% endif %}>本月</option>
                        <option value="quarter" {% if current_date_range == 'quarter' %}selected{% endif %}>本季度</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 申请列表 -->
    <div class="row">
        {% if applications %}
        {% for application in applications %}
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-{{ 'warning' if application.Status == '待批' else 'success' if application.Status == '已批准' else 'danger' }} text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">{{ application.RequestType }}</h6>
                        <span class="badge bg-light text-dark">{{ application.Status }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="card-title">申请内容</h6>
                        <p class="card-text">
                            {% if application.RequestContent %}
                            {{ application.RequestContent[:150] }}{% if application.RequestContent|length > 150 %}...{% endif %}
                            {% else %}
                            <span class="text-muted">无详细内容</span>
                            {% endif %}
                        </p>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">申请时间</small>
                            <div class="fw-bold">
                                {{ application.RequestTime.strftime('%Y-%m-%d %H:%M') if application.RequestTime else '未知' }}
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">处理时间</small>
                            <div class="fw-bold">
                                {% if application.ProcessTime %}
                                {{ application.ProcessTime.strftime('%Y-%m-%d %H:%M') }}
                                {% else %}
                                <span class="text-muted">未处理</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if application.ProcessorID %}
                    <div class="mb-3">
                        <small class="text-muted">处理人</small>
                        <div class="fw-bold">{{ application.processor.Name if application.processor else '未知' }}</div>
                    </div>
                    {% endif %}
                    
                    {% if application.ProcessReason %}
                    <div class="mb-3">
                        <small class="text-muted">处理意见</small>
                        <div class="text-{{ 'success' if application.Status == '已批准' else 'danger' }}">
                            {{ application.ProcessReason }}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if application.Priority %}
                            <span class="badge bg-{{ 'danger' if application.Priority == '高' else 'warning' if application.Priority == '中' else 'secondary' }}">
                                {{ application.Priority }}优先级
                            </span>
                            {% endif %}
                        </div>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-info" 
                                    onclick="viewApplication('{{ application.RequestID }}')"
                                    data-bs-toggle="tooltip" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            {% if application.Status == '待批' %}
                            <button type="button" class="btn btn-outline-warning" 
                                    onclick="editApplication('{{ application.RequestID }}')"
                                    data-bs-toggle="tooltip" title="编辑申请">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" 
                                    onclick="cancelApplication('{{ application.RequestID }}')"
                                    data-bs-toggle="tooltip" title="撤销申请">
                                <i class="fas fa-times"></i>
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- 申请进度 -->
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">申请进度</small>
                        <small class="text-muted">
                            {% if application.Status == '待批' %}
                            等待审批中...
                            {% elif application.Status == '已批准' %}
                            申请已通过
                            {% elif application.Status == '已拒绝' %}
                            申请已拒绝
                            {% endif %}
                        </small>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-{{ 'warning' if application.Status == '待批' else 'success' if application.Status == '已批准' else 'danger' }}" 
                             style="width: {{ '50' if application.Status == '待批' else '100' }}%"></div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-file-alt text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">暂无申请记录</h4>
                <p class="text-muted">您还没有提交任何申请，点击上方按钮开始申请吧！</p>
                <a href="{{ url_for('member.apply_club') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>提交申请
                </a>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 分页 -->
    {% if pagination and pagination.pages > 1 %}
    <nav aria-label="申请列表分页" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('member.applications', page=pagination.prev_num, **request.args) }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            {% endif %}
            
            {% for page_num in pagination.iter_pages() %}
                {% if page_num %}
                    {% if page_num != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('member.applications', page=page_num, **request.args) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('member.applications', page=pagination.next_num, **request.args) }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- 申请详情模态框 -->
<div class="modal fade" id="applicationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">申请详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="applicationModalBody">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="modalEditBtn" style="display: none;">编辑申请</button>
                <button type="button" class="btn btn-danger" id="modalCancelBtn" style="display: none;">撤销申请</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 查看申请详情
function viewApplication(requestId) {
    fetch(`/member/applications/${requestId}/detail`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $('#applicationModalBody').html(data.html);
                
                // 如果是待审批状态，显示编辑和撤销按钮
                if (data.application.Status === '待批') {
                    $('#modalEditBtn').show().attr('onclick', `editApplication('${requestId}')`);
                    $('#modalCancelBtn').show().attr('onclick', `cancelApplication('${requestId}')`);
                } else {
                    $('#modalEditBtn, #modalCancelBtn').hide();
                }
                
                $('#applicationModal').modal('show');
            } else {
                alert('获取详情失败：' + data.message);
            }
        })
        .catch(error => {
            alert('获取详情失败：' + error.message);
        });
}

// 编辑申请
function editApplication(requestId) {
    window.location.href = `/member/applications/${requestId}/edit`;
}

// 撤销申请
function cancelApplication(requestId) {
    if (confirm('确定要撤销这个申请吗？撤销后无法恢复。')) {
        fetch(`/member/applications/${requestId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('申请已成功撤销');
                location.reload();
            } else {
                alert('撤销失败：' + data.message);
            }
        })
        .catch(error => {
            alert('撤销失败：' + error.message);
        });
    }
}

// 自动刷新申请状态
setInterval(function() {
    const pendingCards = document.querySelectorAll('.card-header.bg-warning');
    if (pendingCards.length > 0) {
        // 检查是否有申请状态更新
        fetch('/member/api/application-status-check')
            .then(response => response.json())
            .then(data => {
                if (data.updated) {
                    // 如果有状态更新，刷新页面
                    location.reload();
                }
            })
            .catch(error => {
                console.error('检查申请状态失败:', error);
            });
    }
}, 30000); // 每30秒检查一次
</script>
{% endblock %}
