#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Members表数据模型
包含用户基本信息和角色权限管理
"""

from app import db
from datetime import datetime
import uuid
import bcrypt

class Member(db.Model):
    """
    成员模型类
    包含管理员、会长、会员三种角色
    """
    
    __tablename__ = 'Members'
    
    # 主键：MemberID CHAR(36) PRIMARY KEY
    MemberID = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Name VARCHAR(50) NOT NULL
    Name = db.Column(db.String(50), nullable=False, comment='用户姓名')
    
    # Age INT CHECK (Age > 0 AND Age < 150)
    Age = db.Column(db.Integer, nullable=True, comment='用户年龄')
    
    # Gender ENUM('男', '女', '其他') NOT NULL
    Gender = db.Column(db.Enum('男', '女', '其他'), nullable=False, comment='用户性别')
    
    # College VARCHAR(100)
    College = db.Column(db.String(100), nullable=True, comment='所属学院')
    
    # Dormitory VARCHAR(50)
    Dormitory = db.Column(db.String(50), nullable=True, comment='宿舍信息')
    
    # Phone VARCHAR(20) CHECK (Phone REGEXP '^[0-9]{7,15}$')
    Phone = db.Column(db.String(20), nullable=True, comment='联系电话')
    
    # Specialty VARCHAR(200)
    Specialty = db.Column(db.String(200), nullable=True, comment='专长技能')
    
    # Username VARCHAR(20) NOT NULL UNIQUE
    Username = db.Column(db.String(20), nullable=False, unique=True, comment='用户名')
    
    # Password VARCHAR(30)
    Password = db.Column(db.String(60), nullable=True, comment='密码哈希')  # bcrypt需要60字符
    
    # Role ENUM('管理员', '会长', '会员') NOT NULL
    Role = db.Column(db.Enum('管理员', '会长', '会员'), nullable=False, comment='用户角色')
    
    # 关系定义
    # 作为社长的社团（一对多关系）
    clubs_as_president = db.relationship('Club', backref='president', lazy='dynamic')
    
    # 作为组织者的活动（一对多关系）
    activities_as_organizer = db.relationship('Activity', foreign_keys='Activity.OrganizerID', lazy='dynamic')
    
    # 社团成员关系（一对多关系）
    club_memberships = db.relationship('MemberClub', backref='member', lazy='dynamic')
    
    # 审批申请（一对多关系）
    approval_requests = db.relationship('ApprovalRequest', backref='applicant', lazy='dynamic')
    
    def __init__(self, **kwargs):
        """初始化成员对象"""
        super(Member, self).__init__(**kwargs)
        if not self.MemberID:
            self.MemberID = str(uuid.uuid4())
    
    def set_password(self, password, encrypt=False):
        """设置密码（可选择是否加密存储）"""
        if password:
            if encrypt:
                # 使用bcrypt加密存储
                self.Password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            else:
                # 明文存储（用于测试数据）
                self.Password = password
    
    def check_password(self, password):
        """验证密码"""
        if not self.Password or not password:
            return False

        # 检查是否为bcrypt哈希值（以$2b$开头）
        if self.Password.startswith('$2b$'):
            # 使用bcrypt验证
            return bcrypt.checkpw(password.encode('utf-8'), self.Password.encode('utf-8'))
        else:
            # 明文密码直接比较
            return self.Password == password
    
    def is_admin(self):
        """判断是否为管理员"""
        return self.Role == '管理员'
    
    def is_president(self):
        """判断是否为会长"""
        return self.Role == '会长'
    
    def is_member(self):
        """判断是否为普通会员"""
        return self.Role == '会员'
    
    def get_clubs(self):
        """获取用户加入的所有社团"""
        return [mc.club for mc in self.club_memberships.filter_by(Status='已批准').all()]
    
    def validate_age(self):
        """验证年龄约束：Age > 0 AND Age < 150"""
        if self.Age is not None:
            return 0 < self.Age < 150
        return True
    
    def validate_phone(self):
        """验证电话格式：Phone REGEXP '^[0-9]{7,15}$'"""
        if self.Phone:
            import re
            return bool(re.match(r'^[0-9]{7,15}$', self.Phone))
        return True
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'MemberID': self.MemberID,
            'Name': self.Name,
            'Age': self.Age,
            'Gender': self.Gender,
            'College': self.College,
            'Dormitory': self.Dormitory,
            'Phone': self.Phone,
            'Specialty': self.Specialty,
            'Username': self.Username,
            'Role': self.Role
        }
    
    def __repr__(self):
        return f'<Member {self.Username}({self.Role})>'
