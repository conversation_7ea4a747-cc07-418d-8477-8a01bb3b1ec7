{% extends "base.html" %}

{% block title %}注册 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-success text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="bi bi-person-plus me-2"></i>用户注册
                    </h3>
                </div>
                <div class="card-body p-5">
                    <form method="POST" id="registerForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-person-badge me-2"></i>基本信息
                                </h5>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person me-1"></i>用户名 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" 
                                       name="username" required maxlength="20"
                                       placeholder="请输入用户名">
                                <div class="form-text">用户名将用于登录，不可修改</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    <i class="bi bi-card-text me-1"></i>真实姓名 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" 
                                       name="name" required maxlength="50"
                                       placeholder="请输入真实姓名">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock me-1"></i>密码 <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="password" 
                                       name="password" required minlength="6" maxlength="20"
                                       placeholder="请输入密码">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="bi bi-lock-fill me-1"></i>确认密码 <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="confirm_password" 
                                       name="confirm_password" required
                                       placeholder="请再次输入密码">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="gender" class="form-label">
                                    <i class="bi bi-gender-ambiguous me-1"></i>性别 <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="">请选择性别</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="age" class="form-label">
                                    <i class="bi bi-calendar me-1"></i>年龄
                                </label>
                                <input type="number" class="form-control" id="age" 
                                       name="age" min="1" max="149"
                                       placeholder="请输入年龄">
                            </div>
                            
                            <!-- 详细信息 -->
                            <div class="col-12 mt-3">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-info-circle me-2"></i>详细信息
                                </h5>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="college" class="form-label">
                                    <i class="bi bi-building me-1"></i>所属学院
                                </label>
                                <input type="text" class="form-control" id="college" 
                                       name="college" maxlength="100"
                                       placeholder="请输入所属学院">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="dormitory" class="form-label">
                                    <i class="bi bi-house me-1"></i>宿舍信息
                                </label>
                                <input type="text" class="form-control" id="dormitory" 
                                       name="dormitory" maxlength="50"
                                       placeholder="请输入宿舍信息">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">
                                    <i class="bi bi-telephone me-1"></i>联系电话
                                </label>
                                <input type="tel" class="form-control" id="phone" 
                                       name="phone" pattern="[0-9]{7,15}"
                                       placeholder="请输入联系电话">
                                <div class="form-text">请输入7-15位数字</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">
                                    <i class="fas fa-user-tag me-1"></i>用户角色
                                </label>
                                <input type="text" class="form-control" value="普通会员" readonly>
                                <input type="hidden" name="role" value="会员">
                                <div class="form-text">新注册用户默认为普通会员角色</div>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="specialty" class="form-label">
                                    <i class="bi bi-star me-1"></i>专长技能
                                </label>
                                <textarea class="form-control" id="specialty" name="specialty" 
                                          rows="3" maxlength="200"
                                          placeholder="请描述您的专长技能，这将帮助我们为您推荐合适的社团"></textarea>
                            </div>
                        </div>
                        
                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="bi bi-person-plus me-2"></i>立即注册
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center">
                        <p class="text-muted mb-3">已有账户？</p>
                        <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary">
                            <i class="bi bi-box-arrow-in-right me-2"></i>立即登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 表单验证
    $('#registerForm').on('submit', function(e) {
        const password = $('#password').val();
        const confirmPassword = $('#confirm_password').val();
        
        if (password !== confirmPassword) {
            e.preventDefault();
            alert('两次输入的密码不一致');
            $('#confirm_password').focus();
            return false;
        }
        
        // 显示加载状态
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>注册中...');
    });
    
    // 密码确认验证
    $('#confirm_password').on('input', function() {
        const password = $('#password').val();
        const confirmPassword = $(this).val();
        
        if (confirmPassword && password !== confirmPassword) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // 用户名可用性检查
    let usernameTimeout;
    $('#username').on('input', function() {
        const username = $(this).val().trim();
        const $this = $(this);
        
        clearTimeout(usernameTimeout);
        
        if (username.length >= 3) {
            usernameTimeout = setTimeout(function() {
                $.get('{{ url_for("auth.check_username") }}', {username: username})
                .done(function(data) {
                    if (data.available) {
                        $this.removeClass('is-invalid').addClass('is-valid');
                    } else {
                        $this.removeClass('is-valid').addClass('is-invalid');
                    }
                });
            }, 500);
        } else {
            $this.removeClass('is-valid is-invalid');
        }
    });
    
    // 电话号码格式验证
    $('#phone').on('input', function() {
        const phone = $(this).val();
        const pattern = /^[0-9]{7,15}$/;
        
        if (phone && !pattern.test(phone)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
});
</script>
{% endblock %}
