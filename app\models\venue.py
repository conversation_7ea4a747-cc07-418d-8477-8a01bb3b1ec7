#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Venues表数据模型
严格按照database.sql中的Venues表结构定义
活动场馆信息和资源管理
"""

from app import db
import uuid

class Venue(db.Model):
    """
    场馆模型类 - 对应database.sql中的Venues表
    管理活动场馆的基础信息和可用性
    """
    
    __tablename__ = 'Venues'
    
    # 主键：VenueID CHAR(36) PRIMARY KEY
    VenueID = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # VenueName VARCHAR(100) NOT NULL
    VenueName = db.Column(db.String(100), nullable=False, comment='场馆名称')
    
    # Location VARCHAR(100) NOT NULL
    Location = db.Column(db.String(100), nullable=False, comment='场馆位置')
    
    # Address VARCHAR(200) NOT NULL
    Address = db.Column(db.String(200), nullable=False, comment='场馆地址')
    
    # ContactPhone VARCHAR(20) CHECK (ContactPhone REGEXP '^[0-9]{7,15}$')
    ContactPhone = db.Column(db.String(20), nullable=True, comment='联系电话')
    
    # Capacity INT CHECK (Capacity > 0)
    Capacity = db.Column(db.Integer, nullable=True, comment='场馆容量')
    
    # VenueType ENUM('室内', '室外', '多功能厅', '体育馆', '其他') NOT NULL
    VenueType = db.Column(db.Enum('室内', '室外', '多功能厅', '体育馆', '其他'),
                         nullable=False, comment='场馆类型')
    
    # AvailabTime VARCHAR(20) NOT NULL
    AvailabTime = db.Column(db.String(20), nullable=False, comment='可用时间')
    
    # 关系定义
    # 场馆举办的活动（一对多关系）
    activities = db.relationship('Activity', backref='venue', lazy='dynamic')
    
    def __init__(self, **kwargs):
        """初始化场馆对象"""
        super(Venue, self).__init__(**kwargs)
        if not self.VenueID:
            self.VenueID = str(uuid.uuid4())
    
    def validate_contact_phone(self):
        """验证联系电话格式：ContactPhone REGEXP '^[0-9]{7,15}$'"""
        if self.ContactPhone:
            import re
            return bool(re.match(r'^[0-9]{7,15}$', self.ContactPhone))
        return True
    
    def validate_capacity(self):
        """验证容量约束：Capacity > 0"""
        if self.Capacity is not None:
            return self.Capacity > 0
        return True
    
    def check_availability(self, start_time, end_time, exclude_activity_id=None):
        """
        检查场馆在指定时间段的可用性
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            exclude_activity_id: 排除的活动ID（用于编辑活动时）
        
        Returns:
            bool: True表示可用，False表示有冲突
        """
        from app.models.activity import Activity
        
        # 查询在指定时间段内的活动
        query = Activity.query.filter(
            Activity.VenueID == self.VenueID,
            Activity.Status.in_(['计划中', '进行中']),  # 只考虑未完成的活动
            # 时间重叠条件：新活动开始时间 < 现有活动结束时间 AND 新活动结束时间 > 现有活动开始时间
            Activity.StartTime < end_time,
            Activity.EndTime > start_time
        )
        
        # 如果是编辑活动，排除当前活动
        if exclude_activity_id:
            query = query.filter(Activity.ActivityID != exclude_activity_id)
        
        conflicting_activities = query.all()
        return len(conflicting_activities) == 0
    
    def get_conflicting_activities(self, start_time, end_time, exclude_activity_id=None):
        """
        获取在指定时间段内冲突的活动列表
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            exclude_activity_id: 排除的活动ID
        
        Returns:
            list: 冲突的活动列表
        """
        from app.models.activity import Activity
        
        query = Activity.query.filter(
            Activity.VenueID == self.VenueID,
            Activity.Status.in_(['计划中', '进行中']),
            Activity.StartTime < end_time,
            Activity.EndTime > start_time
        )
        
        if exclude_activity_id:
            query = query.filter(Activity.ActivityID != exclude_activity_id)
        
        return query.all()
    
    def get_upcoming_activities(self, limit=10):
        """获取即将举行的活动"""
        from datetime import datetime
        return self.activities.filter(
            Activity.StartTime > datetime.now(),
            Activity.Status.in_(['计划中', '进行中'])
        ).order_by(Activity.StartTime.asc()).limit(limit).all()
    
    def get_usage_rate(self, start_date=None, end_date=None):
        """
        计算场馆使用率
        
        Args:
            start_date: 统计开始日期
            end_date: 统计结束日期
        
        Returns:
            float: 使用率百分比
        """
        from datetime import datetime, timedelta
        from app.models.activity import Activity
        
        if not start_date:
            start_date = datetime.now() - timedelta(days=30)
        if not end_date:
            end_date = datetime.now()
        
        # 计算总的可用时间（简化计算，假设每天可用8小时）
        total_days = (end_date - start_date).days
        total_available_hours = total_days * 8
        
        # 计算实际使用时间
        activities = self.activities.filter(
            Activity.StartTime >= start_date,
            Activity.EndTime <= end_date,
            Activity.Status.in_(['已完成', '进行中'])
        ).all()
        
        used_hours = 0
        for activity in activities:
            duration = activity.EndTime - activity.StartTime
            used_hours += duration.total_seconds() / 3600
        
        if total_available_hours > 0:
            return (used_hours / total_available_hours) * 100
        return 0
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'VenueID': self.VenueID,
            'VenueName': self.VenueName,
            'Location': self.Location,
            'Address': self.Address,
            'ContactPhone': self.ContactPhone,
            'Capacity': self.Capacity,
            'VenueType': self.VenueType,
            'AvailabTime': self.AvailabTime
        }
    
    def __repr__(self):
        return f'<Venue {self.VenueName}({self.VenueType})>'
