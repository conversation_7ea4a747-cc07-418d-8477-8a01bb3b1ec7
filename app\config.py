#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学校社团管理系统配置文件
"""

import os
from datetime import timedelta

class Config:
    """
    Flask应用配置类
    包含数据库连接、安全密钥等核心配置
    """
    
    # Flask核心配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'club-management-system-secret-key-2024'
    
    # 数据库配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or 3306)
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or '123456'
    MYSQL_DATABASE = 'ClubManagementSystem'  
    
    # SQLAlchemy配置
    SQLALCHEMY_DATABASE_URI = (
        f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@'
        f'{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}'
        f'?charset=utf8mb4'
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'echo': False  # 生产环境设为False
    }
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    SESSION_COOKIE_SECURE = False  # 开发环境设为False，生产环境设为True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # CSRF保护配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # 1小时
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 时区配置
    TIMEZONE = 'Asia/Shanghai'  # 中国标准时间UTC+8
    
    # 分页配置
    ITEMS_PER_PAGE = 20
    
    # 业务规则配置（基于需求文档）
    MAX_CLUB_MEMBERS_DEFAULT = 100  # 默认社团最大成员数
    ACTIVITY_ADVANCE_DAYS = 30  # 活动最多提前30天申请
    APPROVAL_TIMEOUT_DAYS = 7  # 审批超时天数
