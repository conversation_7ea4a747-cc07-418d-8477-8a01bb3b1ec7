{% extends "base.html" %}

{% block title %}搜索结果 - 学校社团管理系统{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 搜索框 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-8">
                            <label for="search" class="form-label">搜索内容</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="search" name="q" 
                                       placeholder="输入关键词搜索社团、活动、场馆" value="{{ query }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">搜索类型</label>
                            <select class="form-select" id="type" name="type">
                                <option value="all" {% if search_type == 'all' %}selected{% endif %}>全部</option>
                                <option value="clubs" {% if search_type == 'clubs' %}selected{% endif %}>社团</option>
                                <option value="activities" {% if search_type == 'activities' %}selected{% endif %}>活动</option>
                                <option value="venues" {% if search_type == 'venues' %}selected{% endif %}>场馆</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if query %}
    <!-- 搜索结果 -->
    <div class="row">
        <div class="col-12">
            <h3 class="mb-4">搜索结果：{{ query }}</h3>
        </div>
    </div>

    <!-- 社团搜索结果 -->
    {% if results.clubs %}
    <div class="row mb-5">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-layer-group me-2 text-primary"></i>社团 ({{ results.clubs|length }})
            </h4>
            <div class="row">
                {% for club in results.clubs %}
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h6 class="card-title">{{ club.ClubName }}</h6>
                            <p class="card-text text-muted">{{ club.Description }}</p>
                            <p class="card-text small">
                                <i class="fas fa-users me-1"></i>{{ club.CurrentMembers }}/{{ club.MaxMembers or '∞' }}
                            </p>
                            <a href="{{ url_for('common.club_detail', club_id=club.ClubID) }}" 
                               class="btn btn-sm btn-outline-primary">查看详情</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 活动搜索结果 -->
    {% if results.activities %}
    <div class="row mb-5">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-calendar-alt me-2 text-warning"></i>活动 ({{ results.activities|length }})
            </h4>
            <div class="row">
                {% for activity in results.activities %}
                <div class="col-lg-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h6 class="card-title">{{ activity.ActivityName }}</h6>
                            <p class="card-text text-muted">
                                <i class="fas fa-layer-group me-1"></i>{{ activity.club.ClubName if activity.club else '未知社团' }}
                            </p>
                            <p class="card-text small">
                                <i class="fas fa-calendar me-1"></i>{{ activity.StartTime.strftime('%Y-%m-%d %H:%M') if activity.StartTime else '时间待定' }}
                            </p>
                            <p class="card-text small">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ activity.venue.VenueName if activity.venue else '地点待定' }}
                            </p>
                            <span class="badge bg-{{ 'warning' if activity.Status == '计划中' else 'primary' if activity.Status == '进行中' else 'success' }}">
                                {{ activity.Status }}
                            </span>
                            <a href="{{ url_for('common.activity_detail', activity_id=activity.ActivityID) }}" 
                               class="btn btn-sm btn-outline-primary ms-2">查看详情</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 场馆搜索结果 -->
    {% if results.venues %}
    <div class="row mb-5">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-building me-2 text-info"></i>场馆 ({{ results.venues|length }})
            </h4>
            <div class="row">
                {% for venue in results.venues %}
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h6 class="card-title">{{ venue.VenueName }}</h6>
                            <p class="card-text text-muted">{{ venue.VenueType }}</p>
                            <p class="card-text small">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ venue.Location }}
                            </p>
                            {% if venue.Capacity %}
                            <p class="card-text small">
                                <i class="fas fa-users me-1"></i>容量：{{ venue.Capacity }}人
                            </p>
                            {% endif %}
                            <a href="{{ url_for('common.venue_detail', venue_id=venue.VenueID) }}" 
                               class="btn btn-sm btn-outline-primary">查看详情</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 无搜索结果 -->
    {% if not results.clubs and not results.activities and not results.venues %}
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-search text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">没有找到相关结果</h4>
                <p class="text-muted">尝试使用其他关键词或调整搜索类型</p>
            </div>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- 搜索提示 -->
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-search text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">开始搜索</h4>
                <p class="text-muted">输入关键词搜索社团、活动或场馆</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
