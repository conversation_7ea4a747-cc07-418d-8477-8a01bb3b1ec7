#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证控制器
处理用户登录、注册、登出等认证相关业务逻辑
严基于Members表的数据结构进行用户管理
"""

from app import db
from app.models.member import Member
from app.utils.helpers import generate_uuid, validate_phone, validate_age
from flask import session, flash
import re

class AuthController:
    """认证控制器类"""
    
    @staticmethod
    def login(username, password):
        """
        用户登录
        
        Args:
            username: 用户名（对应Members.Username）
            password: 密码
        
        Returns:
            tuple: (success: bool, message: str, user: Member)
        """
        if not username or not password:
            return False, '用户名和密码不能为空', None
        
        # 查询用户（基于Members表的Username字段）
        user = Member.query.filter_by(Username=username).first()
        
        if not user:
            return False, '用户名不存在', None
        
        # 验证密码
        if not user.check_password(password):
            return False, '密码错误', None
        
        # 登录成功，设置会话
        session['user_id'] = user.MemberID
        session['username'] = user.Username
        session['role'] = user.Role
        session.permanent = True
        
        return True, f'欢迎回来，{user.Name}！', user
    
    @staticmethod
    def register(form_data):
        """
        用户注册
        
        Args:
            form_data: 注册表单数据字典
        
        Returns:
            tuple: (success: bool, message: str, user: Member)
        """
        # 验证必填字段
        required_fields = ['username', 'password', 'name', 'gender']
        for field in required_fields:
            if not form_data.get(field):
                return False, f'{field}不能为空', None

        # 强制设置角色为"会员"（安全限制）
        form_data['role'] = '会员'
        
        # 验证用户名唯一性（Members.Username UNIQUE约束）
        existing_user = Member.query.filter_by(Username=form_data['username']).first()
        if existing_user:
            return False, '用户名已存在', None
        
        # 验证用户名长度（VARCHAR(20)约束）
        if len(form_data['username']) > 20:
            return False, '用户名长度不能超过20个字符', None
        
        # 验证姓名长度（VARCHAR(50)约束）
        if len(form_data['name']) > 50:
            return False, '姓名长度不能超过50个字符', None
        
        # 验证性别枚举值
        valid_genders = ['男', '女', '其他']
        if form_data['gender'] not in valid_genders:
            return False, '性别必须为：男、女、其他', None
        
        # 验证角色（只允许会员注册）
        if form_data['role'] != '会员':
            return False, '注册用户只能选择会员角色', None
        
        # 验证年龄（如果提供）
        age = form_data.get('age')
        if age and not validate_age(age):
            return False, '年龄必须在1-149之间', None
        
        # 验证电话号码（如果提供）
        phone = form_data.get('phone')
        if phone and not validate_phone(phone):
            return False, '电话号码格式不正确（7-15位数字）', None
        
        # 验证学院长度（如果提供）
        college = form_data.get('college', '')
        if college and len(college) > 100:
            return False, '学院名称长度不能超过100个字符', None
        
        # 验证宿舍长度（如果提供）
        dormitory = form_data.get('dormitory', '')
        if dormitory and len(dormitory) > 50:
            return False, '宿舍信息长度不能超过50个字符', None
        
        # 验证专长长度（如果提供）
        specialty = form_data.get('specialty', '')
        if specialty and len(specialty) > 200:
            return False, '专长描述长度不能超过200个字符', None
        
        try:
            # 创建新用户（严格按照Members表结构）
            new_user = Member(
                MemberID=generate_uuid(),  # CHAR(36) PRIMARY KEY
                Name=form_data['name'],    # VARCHAR(50) NOT NULL
                Age=int(age) if age else None,  # INT CHECK (Age > 0 AND Age < 150)
                Gender=form_data['gender'], # ENUM('男', '女', '其他') NOT NULL
                College=college or None,    # VARCHAR(100)
                Dormitory=dormitory or None, # VARCHAR(50)
                Phone=phone or None,        # VARCHAR(20) CHECK (Phone REGEXP '^[0-9]{7,15}$')
                Specialty=specialty or None, # VARCHAR(200)
                Username=form_data['username'], # VARCHAR(20) NOT NULL UNIQUE
                Role=form_data['role']      # ENUM('管理员', '会长', '会员') NOT NULL
            )
            
            # 设置密码（加密存储）
            new_user.set_password(form_data['password'])
            
            # 保存到数据库
            db.session.add(new_user)
            db.session.commit()
            
            return True, '注册成功！请登录', new_user
            
        except Exception as e:
            db.session.rollback()
            return False, f'注册失败：{str(e)}', None
    
    @staticmethod
    def logout():
        """
        用户登出
        
        Returns:
            tuple: (success: bool, message: str)
        """
        # 清除会话信息
        session.pop('user_id', None)
        session.pop('username', None)
        session.pop('role', None)
        
        return True, '已安全退出'
    
    @staticmethod
    def change_password(user_id, old_password, new_password):
        """
        修改密码
        
        Args:
            user_id: 用户ID
            old_password: 原密码
            new_password: 新密码
        
        Returns:
            tuple: (success: bool, message: str)
        """
        if not old_password or not new_password:
            return False, '原密码和新密码不能为空'
        
        user = Member.query.get(user_id)
        if not user:
            return False, '用户不存在'
        
        # 验证原密码
        if not user.check_password(old_password):
            return False, '原密码错误'
        
        # 验证新密码长度（考虑bcrypt加密后的长度限制）
        if len(new_password) < 6:
            return False, '新密码长度不能少于6位'
        
        if len(new_password) > 20:  # 原始密码长度限制
            return False, '新密码长度不能超过20位'
        
        try:
            # 设置新密码
            user.set_password(new_password)
            db.session.commit()
            
            return True, '密码修改成功'
            
        except Exception as e:
            db.session.rollback()
            return False, f'密码修改失败：{str(e)}'
    
    @staticmethod
    def update_profile(user_id, form_data):
        """
        更新用户资料
        
        Args:
            user_id: 用户ID
            form_data: 表单数据
        
        Returns:
            tuple: (success: bool, message: str)
        """
        user = Member.query.get(user_id)
        if not user:
            return False, '用户不存在'
        
        try:
            # 更新允许修改的字段（不包括MemberID, Username, Role）
            if 'name' in form_data and form_data['name']:
                if len(form_data['name']) > 50:
                    return False, '姓名长度不能超过50个字符'
                user.Name = form_data['name']
            
            if 'age' in form_data and form_data['age']:
                if not validate_age(form_data['age']):
                    return False, '年龄必须在1-149之间'
                user.Age = int(form_data['age'])
            
            if 'gender' in form_data and form_data['gender']:
                valid_genders = ['男', '女', '其他']
                if form_data['gender'] not in valid_genders:
                    return False, '性别必须为：男、女、其他'
                user.Gender = form_data['gender']
            
            if 'college' in form_data:
                college = form_data['college'] or None
                if college and len(college) > 100:
                    return False, '学院名称长度不能超过100个字符'
                user.College = college
            
            if 'dormitory' in form_data:
                dormitory = form_data['dormitory'] or None
                if dormitory and len(dormitory) > 50:
                    return False, '宿舍信息长度不能超过50个字符'
                user.Dormitory = dormitory
            
            if 'phone' in form_data:
                phone = form_data['phone'] or None
                if phone and not validate_phone(phone):
                    return False, '电话号码格式不正确（7-15位数字）'
                user.Phone = phone
            
            if 'specialty' in form_data:
                specialty = form_data['specialty'] or None
                if specialty and len(specialty) > 200:
                    return False, '专长描述长度不能超过200个字符'
                user.Specialty = specialty
            
            db.session.commit()
            return True, '资料更新成功'
            
        except Exception as e:
            db.session.rollback()
            return False, f'资料更新失败：{str(e)}'
    
    @staticmethod
    def get_user_by_id(user_id):
        """
        根据ID获取用户信息
        
        Args:
            user_id: 用户ID
        
        Returns:
            Member: 用户对象
        """
        return Member.query.get(user_id)
    
    @staticmethod
    def is_username_available(username, exclude_user_id=None):
        """
        检查用户名是否可用
        
        Args:
            username: 用户名
            exclude_user_id: 排除的用户ID（用于编辑时检查）
        
        Returns:
            bool: True表示可用
        """
        query = Member.query.filter_by(Username=username)
        if exclude_user_id:
            query = query.filter(Member.MemberID != exclude_user_id)
        
        return query.first() is None
