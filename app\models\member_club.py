#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MemberClub表数据模型
严格按照database.sql中的MemberClub表结构定义
会员社团关联关系和入会退会流程管理
"""

from app import db
from datetime import datetime
import uuid

class MemberClub(db.Model):
    """
    会员社团关联模型类 - 对应database.sql中的ClubMembers表
    管理会员与社团的关联关系，包括申请、审批、退会等流程
    """

    __tablename__ = 'ClubMembers'
    
    # 主键：RecordID CHAR(36) PRIMARY KEY
    RecordID = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # MemberID CHAR(36) NOT NULL, FOREIGN KEY (MemberID) REFERENCES Members(MemberID)
    MemberID = db.Column(db.String(36), db.ForeignKey('Members.MemberID'), 
                        nullable=False, comment='会员ID')
    
    # ClubID CHAR(36) NOT NULL, FOREIGN KEY (ClubID) REFERENCES Clubs(ClubID)
    ClubID = db.Column(db.String(36), db.ForeignKey('Clubs.ClubID'), 
                      nullable=False, comment='社团ID')
    
    # ApplyTime DATETIME NOT NULL
    ApplyTime = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='申请时间')
    
    # Status ENUM('待审批', '已批准', '已拒绝', '已退出') NOT NULL
    Status = db.Column(db.Enum('待审批', '已批准', '已拒绝', '已退出'),
                      nullable=False, default='待审批', comment='申请状态')

    # ApplicationReason VARCHAR(200)
    ApplicationReason = db.Column(db.String(200), nullable=True, comment='申请理由')

    # ApprovalId VARCHAR(20) NOT NULL
    ApprovalId = db.Column(db.String(20), nullable=False, comment='审批人ID')

    # ApprovalTime DATETIME
    ApprovalTime = db.Column(db.DateTime, nullable=True, comment='审批时间')

    # Rejoinable ENUM('是', '否') NOT NULL
    Rejoinable = db.Column(db.Enum('是', '否'), nullable=False, comment='是否可重新加入')
    
    # 唯一约束：UNIQUE (MemberID, ClubID)
    __table_args__ = (
        db.UniqueConstraint('MemberID', 'ClubID', name='unique_member_club'),
    )

    # 注意：关系定义已在Member和Club模型中通过backref定义，这里不需要重复定义

    def __init__(self, **kwargs):
        """初始化会员社团关联对象"""
        super(MemberClub, self).__init__(**kwargs)
        if not self.RecordID:
            self.RecordID = str(uuid.uuid4())
        if not self.ApplyTime:
            self.ApplyTime = datetime.now()
    
    def approve(self, approver_id, comments=None):
        """
        批准申请
        
        Args:
            approver_id: 审批人ID
            comments: 审批意见
        """
        self.Status = '已批准'
        self.ApprovalId = approver_id
        self.ApprovalTime = datetime.now()
        self.Rejoinable = '是'  # 默认可重新加入
        
        # 更新社团当前成员数
        if self.club:
            self.club.update_current_members()
            db.session.commit()
    
    def reject(self, approver_id, comments=None):
        """
        拒绝申请
        
        Args:
            approver_id: 审批人ID
            comments: 拒绝理由
        """
        self.Status = '已拒绝'
        self.ApprovalId = approver_id
        self.ApprovalTime = datetime.now()
        self.Rejoinable = '是'  # 被拒绝后可重新申请
    
    def withdraw(self, reason=None):
        """
        退出社团
        
        Args:
            reason: 退出理由
        """
        if self.Status == '已批准':
            self.Status = '已退出'
            self.ApprovalTime = datetime.now()
            if reason:
                self.ApplicationReason = f"退出理由：{reason}"
            
            # 更新社团当前成员数
            if self.club:
                self.club.update_current_members()
                db.session.commit()
    
    def can_be_approved(self):
        """判断是否可以批准"""
        return self.Status == '待审批'

    def can_be_rejected(self):
        """判断是否可以拒绝"""
        return self.Status == '待审批'

    def can_withdraw(self):
        """判断是否可以退出"""
        return self.Status == '已批准'

    def is_pending(self):
        """判断是否为待审批状态"""
        return self.Status == '待审批'
    
    def is_approved(self):
        """判断是否已批准"""
        return self.Status == '已批准'
    
    def is_rejected(self):
        """判断是否已拒绝"""
        return self.Status == '已拒绝'
    
    def is_withdrawn(self):
        """判断是否已退出"""
        return self.Status == '已退出'
    
    def can_rejoin(self):
        """判断是否可以重新加入"""
        return self.Rejoinable == '是' and self.Status in ['已拒绝', '已退出']
    
    def get_status_display(self):
        """获取状态的中文显示"""
        status_map = {
            '待审批': '待审批',
            '已批准': '已加入',
            '已拒绝': '已拒绝',
            '已退出': '已退出'
        }
        return status_map.get(self.Status, self.Status)
    
    def get_approval_duration(self):
        """获取审批耗时（天数）"""
        if self.ApprovalTime and self.ApplyTime:
            duration = self.ApprovalTime - self.ApplyTime
            return duration.days
        return None
    
    def is_overdue(self, timeout_days=7):
        """
        判断申请是否超时
        
        Args:
            timeout_days: 超时天数，默认7天
        
        Returns:
            bool: True表示超时
        """
        if self.Status == '待审批':
            from datetime import timedelta
            timeout_date = self.ApplyTime + timedelta(days=timeout_days)
            return datetime.now() > timeout_date
        return False
    
    def get_membership_duration(self):
        """获取会员身份持续时间（天数）"""
        if self.Status == '已批准' and self.ApprovalTime:
            return (datetime.now() - self.ApprovalTime).days
        elif self.Status == '已退出' and self.ApprovalTime:
            # 计算从批准到退出的时间
            end_time = self.ApprovalTime  # 这里应该是退出时间，但数据库设计中没有单独的退出时间字段
            return (end_time - self.ApprovalTime).days
        return 0
    
    @staticmethod
    def get_member_clubs(member_id, status=None):
        """
        获取会员加入的社团列表
        
        Args:
            member_id: 会员ID
            status: 状态过滤，如'已批准'
        
        Returns:
            list: 社团关联记录列表
        """
        query = MemberClub.query.filter_by(MemberID=member_id)
        if status:
            query = query.filter_by(Status=status)
        return query.all()
    
    @staticmethod
    def get_club_members(club_id, status=None):
        """
        获取社团的成员列表
        
        Args:
            club_id: 社团ID
            status: 状态过滤，如'已批准'
        
        Returns:
            list: 会员关联记录列表
        """
        query = MemberClub.query.filter_by(ClubID=club_id)
        if status:
            query = query.filter_by(Status=status)
        return query.all()
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'RecordID': self.RecordID,
            'MemberID': self.MemberID,
            'ClubID': self.ClubID,
            'ApplyTime': self.ApplyTime.isoformat() if self.ApplyTime else None,
            'Status': self.Status,
            'StatusDisplay': self.get_status_display(),
            'ApplicationReason': self.ApplicationReason,
            'ApprovalId': self.ApprovalId,
            'ApprovalTime': self.ApprovalTime.isoformat() if self.ApprovalTime else None,
            'Rejoinable': self.Rejoinable,
            'MemberName': self.member.Name if self.member else None,
            'ClubName': self.club.ClubName if self.club else None,
            'ApprovalDuration': self.get_approval_duration(),
            'IsOverdue': self.is_overdue()
        }
    
    def __repr__(self):
        member_name = self.member.Name if self.member else 'Unknown'
        club_name = self.club.ClubName if self.club else 'Unknown'
        return f'<MemberClub {member_name}-{club_name}({self.Status})>'
