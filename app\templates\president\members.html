{% extends "admin_base.html" %}

{% block title %}成员管理 - 会长后台{% endblock %}

{% block breadcrumb_title %}成员管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1 fw-bold">成员管理</h2>
                    <p class="text-muted mb-0">管理所有社团的成员信息和申请审批</p>
                </div>
                {% if clubs %}
                <div>
                    <a href="{{ url_for('president.member_applications') }}" class="btn btn-warning">
                        <i class="fas fa-clock me-2"></i>待审批申请
                        {% if stats.pending > 0 %}
                        <span class="badge bg-light text-dark ms-1">{{ stats.pending }}</span>
                        {% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    {% if clubs %}
    <!-- 统计概览 -->
    <div class="row mb-4">
        <div class="col-md-3 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body py-3">
                    <i class="fas fa-layer-group text-primary fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ clubs | length }}</h4>
                    <small class="text-muted">管理社团</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body py-3">
                    <i class="fas fa-users text-success fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ stats.approved }}</h4>
                    <small class="text-muted">已加入</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body py-3">
                    <i class="fas fa-clock text-warning fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ stats.pending }}</h4>
                    <small class="text-muted">待审批</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body py-3">
                    <i class="fas fa-user-times text-secondary fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ stats.withdrawn }}</h4>
                    <small class="text-muted">已退出</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">状态筛选</label>
                            <select name="status" class="form-select" onchange="this.form.submit()">
                                <option value="all" {% if current_status == 'all' %}selected{% endif %}>全部状态</option>
                                <option value="已批准" {% if current_status == '已批准' %}selected{% endif %}>已加入</option>
                                <option value="待审批" {% if current_status == '待审批' %}selected{% endif %}>待审批</option>
                                <option value="已拒绝" {% if current_status == '已拒绝' %}selected{% endif %}>已拒绝</option>
                                <option value="已退出" {% if current_status == '已退出' %}selected{% endif %}>已退出</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">搜索成员</label>
                            <div class="input-group">
                                <input type="text" name="search" class="form-control"
                                       placeholder="搜索姓名、用户名或学号..."
                                       value="{{ search_query or '' }}">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            {% if search_query or current_status != 'all' %}
                            <a href="{{ url_for('president.members') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>清除筛选
                            </a>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 成员列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>成员列表
                            {% if current_status != 'all' %}
                            <span class="badge bg-secondary ms-2">{{ current_status }}</span>
                            {% endif %}
                        </h5>
                        <small class="text-muted">
                            共 {{ pagination.total if pagination else 0 }} 条记录
                        </small>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if members %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>成员信息</th>
                                    <th>所属社团</th>
                                    <th>申请时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for member_club in members %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-3">
                                                {{ (member_club.member.Name or member_club.member.Username)[0] }}
                                            </div>
                                            <div>
                                                <div class="fw-semibold">{{ member_club.member.Name or member_club.member.Username }}</div>
                                                <small class="text-muted">{{ member_club.member.Username }}</small>
                                                {% if member_club.member.StudentID %}
                                                <div><small class="text-muted">学号: {{ member_club.member.StudentID }}</small></div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ clubs_dict[member_club.ClubID].ClubName }}</span>
                                    </td>
                                    <td>
                                        <div>{{ member_club.ApplyTime.strftime('%Y-%m-%d') if member_club.ApplyTime else '未知' }}</div>
                                        <small class="text-muted">{{ member_club.ApplyTime.strftime('%H:%M') if member_club.ApplyTime else '' }}</small>
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if member_club.Status == '已批准' %}bg-success
                                            {% elif member_club.Status == '待审批' %}bg-warning
                                            {% elif member_club.Status == '已拒绝' %}bg-danger
                                            {% else %}bg-secondary{% endif %}">
                                            {{ member_club.Status }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if member_club.Status == '待审批' %}
                                            <button type="button" class="btn btn-outline-success"
                                                    onclick="approveApplication('{{ member_club.RecordID }}', '{{ member_club.member.Name or member_club.member.Username }}')"
                                                    title="批准申请">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="rejectApplication('{{ member_club.RecordID }}', '{{ member_club.member.Name or member_club.member.Username }}')"
                                                    title="拒绝申请">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% elif member_club.Status == '已批准' %}
                                            <button type="button" class="btn btn-outline-warning"
                                                    onclick="removeMember('{{ member_club.RecordID }}', '{{ member_club.member.Name or member_club.member.Username }}')"
                                                    title="移除成员">
                                                <i class="fas fa-user-minus"></i>
                                            </button>
                                            {% endif %}
                                            <button type="button" class="btn btn-outline-info"
                                                    onclick="viewMemberDetail('{{ member_club.RecordID }}')"
                                                    title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination and pagination.pages > 1 %}
                    <div class="card-footer bg-white">
                        <nav aria-label="成员列表分页">
                            <ul class="pagination justify-content-center mb-0">
                                {% if pagination.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('president.members', page=pagination.prev_num, status=current_status, search=search_query) }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in pagination.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != pagination.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('president.members', page=page_num, status=current_status, search=search_query) }}">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if pagination.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('president.members', page=pagination.next_num, status=current_status, search=search_query) }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-search text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
                        <h5 class="mt-3 text-muted">没有找到符合条件的成员</h5>
                        <p class="text-muted">请尝试调整筛选条件或搜索关键词</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- 空状态 -->
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-users text-muted" style="font-size: 5rem; opacity: 0.3;"></i>
                </div>
                <h3 class="text-muted mb-3">暂无管理的社团</h3>
                <p class="text-muted mb-4">您当前没有管理任何社团，无法进行成员管理。</p>
                <a href="{{ url_for('president.dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>返回仪表板
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 成员详情模态框 -->
<div class="modal fade" id="memberDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>成员详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="memberDetailBody">
                <!-- 加载状态 -->
                <div class="text-center py-4" id="memberDetailLoading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载成员详情...</div>
                </div>

                <!-- 详情内容 -->
                <div id="memberDetailContent" style="display: none;">
                    <!-- 基本信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>基本信息</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">姓名</small>
                                            <div class="fw-semibold" id="memberName">-</div>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">用户名</small>
                                            <div id="memberUsername">-</div>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">年龄</small>
                                            <div id="memberAge">-</div>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">性别</small>
                                            <div id="memberGender">-</div>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">学院</small>
                                            <div id="memberCollege">-</div>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">宿舍</small>
                                            <div id="memberDormitory">-</div>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">特长</small>
                                            <div id="memberSpecialty">-</div>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted">角色</small>
                                            <div id="memberRole">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-envelope me-2"></i>联系信息</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <small class="text-muted">联系电话</small>
                                        <div id="memberPhone">-</div>
                                    </div>
                                    <div class="mb-3">
                                        <small class="text-muted">备注</small>
                                        <div class="text-muted">其他联系方式请通过学校系统查询</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 社团信息 -->
                    <div class="card border-0 bg-light mb-4">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-layer-group me-2"></i>社团信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <small class="text-muted">所属社团</small>
                                    <div class="fw-semibold" id="clubName">-</div>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <small class="text-muted">申请时间</small>
                                    <div id="applyTime">-</div>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <small class="text-muted">当前状态</small>
                                    <div>
                                        <span class="badge" id="memberStatus">-</span>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <small class="text-muted">加入天数</small>
                                    <div id="memberSinceDays">-</div>
                                </div>
                                <div class="col-12 mb-2">
                                    <small class="text-muted">申请理由</small>
                                    <div class="border rounded p-2 bg-white" id="applicationReason">-</div>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <small class="text-muted">审批时间</small>
                                    <div id="approvalTime">-</div>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <small class="text-muted">审批人ID</small>
                                    <div id="approvalId">-</div>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <small class="text-muted">可重新加入</small>
                                    <div id="rejoinable">-</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 活动参与统计 -->
                    <div class="card border-0 bg-light">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>活动参与统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="fw-bold h5 text-primary" id="totalActivities">0</div>
                                    <small class="text-muted">总参与活动</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold h5 text-success" id="completedActivities">0</div>
                                    <small class="text-muted">已完成活动</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold h5 text-info" id="participationRate">0%</div>
                                    <small class="text-muted">参与率</small>
                                </div>
                            </div>
                            <div id="recentActivities">
                                <small class="text-muted">最近参与的活动</small>
                                <div class="mt-2" id="recentActivitiesList">
                                    <div class="text-center text-muted py-2">暂无活动记录</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 错误状态 -->
                <div id="memberDetailError" style="display: none;">
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">加载失败</h5>
                        <p class="text-muted" id="errorMessage">获取成员详情时出现错误</p>
                        <button type="button" class="btn btn-primary" onclick="retryLoadMemberDetail()">
                            <i class="fas fa-redo me-1"></i>重试
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div id="memberDetailActions">
                    <!-- 动态生成的操作按钮 -->
                </div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 审批模态框 -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalTitle">审批申请</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="approvalModalBody"></div>
                <div class="mb-3">
                    <label for="approvalComments" class="form-label">审批意见（可选）</label>
                    <textarea class="form-control" id="approvalComments" rows="3"
                              placeholder="请输入审批意见..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmApprove" style="display:none;">
                    <i class="fas fa-check me-1"></i>确认批准
                </button>
                <button type="button" class="btn btn-danger" id="confirmReject" style="display:none;">
                    <i class="fas fa-times me-1"></i>确认拒绝
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* 卡片样式优化 */
.card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

/* 用户头像样式 */
.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

/* 表格样式优化 */
.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* 按钮样式优化 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.btn:hover {
    transform: translateY(-1px);
}

/* 状态徽章样式 */
.badge {
    font-size: 0.75em;
    padding: 0.35em 0.65em;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

/* 搜索框样式 */
.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .card:hover {
        transform: none;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group-sm .btn {
        padding: 0.2rem 0.4rem;
    }
}

/* 加载动画 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 空状态样式 */
.text-center .fas {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 模态框样式 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
}

/* 成员详情模态框特殊样式 */
#memberDetailModal .modal-dialog {
    max-width: 900px;
}

#memberDetailModal .card {
    border-radius: 8px;
    transition: transform 0.2s ease;
}

#memberDetailModal .card:hover {
    transform: translateY(-2px);
}

#memberDetailModal .card-header {
    border-radius: 8px 8px 0 0;
    font-weight: 600;
}

#memberDetailModal .card-body {
    border-radius: 0 0 8px 8px;
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 统计数字样式 */
#memberDetailModal .h5 {
    font-size: 1.5rem;
    margin-bottom: 0;
}

/* 活动记录样式 */
#recentActivitiesList .border {
    transition: all 0.2s ease;
}

#recentActivitiesList .border:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

/* 响应式优化 */
@media (max-width: 768px) {
    #memberDetailModal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }

    #memberDetailModal .row .col-md-6,
    #memberDetailModal .row .col-md-3 {
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentAction = '';
let currentRecordId = '';

$(document).ready(function() {
    // 初始化工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();

    // 统计数字动画效果
    $('.h4').each(function() {
        const $this = $(this);
        const countTo = parseInt($this.text());

        if (!isNaN(countTo) && countTo > 0) {
            $this.text('0');
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 1000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(countTo);
                }
            });
        }
    });

    // 表格行加载动画
    $('.table tbody tr').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateX(-20px)'
        }).delay(index * 50).animate({
            'opacity': '1'
        }, 300).css('transform', 'translateX(0)');
    });
});

// 批准申请
function approveApplication(recordId, memberName) {
    currentAction = 'approve';
    currentRecordId = recordId;

    $('#approvalModalTitle').text('批准申请');
    $('#approvalModalBody').html(`
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            确定要批准 <strong>${memberName}</strong> 的入会申请吗？
        </div>
    `);

    $('#confirmApprove').show();
    $('#confirmReject').hide();
    $('#approvalComments').val('');
    $('#approvalModal').modal('show');
}

// 拒绝申请
function rejectApplication(recordId, memberName) {
    currentAction = 'reject';
    currentRecordId = recordId;

    $('#approvalModalTitle').text('拒绝申请');
    $('#approvalModalBody').html(`
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            确定要拒绝 <strong>${memberName}</strong> 的入会申请吗？
        </div>
    `);

    $('#confirmApprove').hide();
    $('#confirmReject').show();
    $('#approvalComments').val('');
    $('#approvalModal').modal('show');
}

// 确认批准
$('#confirmApprove').click(function() {
    processApplication('approve', currentRecordId);
});

// 确认拒绝
$('#confirmReject').click(function() {
    processApplication('reject', currentRecordId);
});

// 处理申请
function processApplication(action, recordId) {
    const comments = $('#approvalComments').val().trim();
    const url = action === 'approve' ?
        `/president/approve_application/${recordId}` :
        `/president/reject_application/${recordId}`;

    // 显示加载状态
    const $modal = $('#approvalModal');
    $modal.find('.modal-content').addClass('loading');

    // 创建表单数据
    const formData = new FormData();
    formData.append('csrf_token', '{{ csrf_token() }}');
    formData.append('comments', comments);

    // 发送请求
    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (response.ok) {
            $modal.modal('hide');
            // 显示成功消息
            showToast(action === 'approve' ? '申请已批准' : '申请已拒绝', 'success');
            // 刷新页面
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            throw new Error('操作失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('操作失败，请重试', 'error');
    })
    .finally(() => {
        $modal.find('.modal-content').removeClass('loading');
    });
}

// 显示提示消息
function showToast(message, type) {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    // 创建toast容器（如果不存在）
    if (!$('#toast-container').length) {
        $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }

    const $toast = $(toastHtml);
    $('#toast-container').append($toast);

    const toast = new bootstrap.Toast($toast[0]);
    toast.show();

    // 自动移除
    $toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

// 删除成员
function removeMember(recordId, memberName) {
    if (confirm(`确定要移除成员 ${memberName} 吗？此操作不可撤销。`)) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token() }}');
        formData.append('reason', '会长移除');

        fetch(`/president/members/remove/${recordId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (response.ok) {
                showToast('成员已移除', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                throw new Error('移除失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('移除失败，请重试', 'error');
        });
    }
}

// 查看成员详情
let currentMemberRecordId = null;

function viewMemberDetail(recordId) {
    currentMemberRecordId = recordId;

    // 显示模态框
    $('#memberDetailModal').modal('show');

    // 重置状态
    resetMemberDetailModal();

    // 加载成员详情
    loadMemberDetail(recordId);
}

function resetMemberDetailModal() {
    $('#memberDetailLoading').show();
    $('#memberDetailContent').hide();
    $('#memberDetailError').hide();
    $('#memberDetailActions').empty();
}

function loadMemberDetail(recordId) {
    fetch(`/president/members/detail/${recordId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            displayMemberDetail(data.data);
        } else {
            throw new Error(data.message || '获取成员详情失败');
        }
    })
    .catch(error => {
        console.error('Error loading member detail:', error);
        showMemberDetailError(error.message);
    });
}

function displayMemberDetail(memberData) {
    // 隐藏加载状态
    $('#memberDetailLoading').hide();

    // 填充基本信息
    $('#memberName').text(memberData.basic_info.name);
    $('#memberUsername').text(memberData.basic_info.username);
    $('#memberAge').text(memberData.basic_info.age);
    $('#memberGender').text(memberData.basic_info.gender);
    $('#memberCollege').text(memberData.basic_info.college);
    $('#memberDormitory').text(memberData.basic_info.dormitory);
    $('#memberSpecialty').text(memberData.basic_info.specialty);
    $('#memberRole').text(memberData.basic_info.role);

    // 填充联系信息
    $('#memberPhone').text(memberData.contact_info.phone);

    // 填充社团信息
    $('#clubName').text(memberData.club_info.club_name);
    $('#applyTime').text(memberData.club_info.apply_time);
    $('#applicationReason').text(memberData.club_info.application_reason);
    $('#approvalTime').text(memberData.club_info.approval_time);
    $('#approvalId').text(memberData.club_info.approval_id);
    $('#rejoinable').text(memberData.club_info.rejoinable);
    $('#memberSinceDays').text(memberData.member_since_days + ' 天');

    // 设置状态徽章
    const statusBadge = $('#memberStatus');
    const status = memberData.club_info.status;
    statusBadge.text(status);
    statusBadge.removeClass('bg-success bg-warning bg-danger bg-secondary');

    if (status === '已批准') {
        statusBadge.addClass('bg-success');
    } else if (status === '待审批') {
        statusBadge.addClass('bg-warning');
    } else if (status === '已拒绝') {
        statusBadge.addClass('bg-danger');
    } else {
        statusBadge.addClass('bg-secondary');
    }

    // 填充活动统计
    $('#totalActivities').text(memberData.activity_stats.total_activities);
    $('#completedActivities').text(memberData.activity_stats.completed_activities);

    const participationRate = memberData.activity_stats.total_activities > 0 ?
        Math.round((memberData.activity_stats.completed_activities / memberData.activity_stats.total_activities) * 100) : 0;
    $('#participationRate').text(participationRate + '%');

    // 显示最近活动
    displayRecentActivities(memberData.activity_stats.recent_activities);

    // 生成操作按钮
    generateMemberDetailActions(memberData);

    // 显示内容
    $('#memberDetailContent').show();
}

function displayRecentActivities(activities) {
    const container = $('#recentActivitiesList');

    if (!activities || activities.length === 0) {
        container.html('<div class="text-center text-muted py-2">暂无活动记录</div>');
        return;
    }

    let html = '';
    activities.forEach(activity => {
        html += `
            <div class="border rounded p-2 mb-2 bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="fw-semibold">${activity.name}</div>
                        <small class="text-muted">${activity.date}</small>
                    </div>
                    <span class="badge bg-${activity.status === '已完成' ? 'success' : 'warning'}">${activity.status}</span>
                </div>
            </div>
        `;
    });

    container.html(html);
}

function generateMemberDetailActions(memberData) {
    const container = $('#memberDetailActions');
    let html = '';

    const status = memberData.club_info.status;
    const recordId = memberData.record_id;

    if (status === '待审批') {
        html += `
            <button type="button" class="btn btn-success me-2"
                    onclick="quickApproveFromDetail(${recordId}, '${memberData.basic_info.name}')">
                <i class="fas fa-check me-1"></i>快速批准
            </button>
            <button type="button" class="btn btn-danger me-2"
                    onclick="quickRejectFromDetail(${recordId}, '${memberData.basic_info.name}')">
                <i class="fas fa-times me-1"></i>快速拒绝
            </button>
        `;
    } else if (status === '已批准') {
        html += `
            <button type="button" class="btn btn-warning me-2"
                    onclick="removeMemberFromDetail(${recordId}, '${memberData.basic_info.name}')">
                <i class="fas fa-user-minus me-1"></i>移除成员
            </button>
        `;
    }

    container.html(html);
}

function showMemberDetailError(message) {
    $('#memberDetailLoading').hide();
    $('#memberDetailContent').hide();
    $('#errorMessage').text(message);
    $('#memberDetailError').show();
}

function retryLoadMemberDetail() {
    if (currentMemberRecordId) {
        resetMemberDetailModal();
        loadMemberDetail(currentMemberRecordId);
    }
}

// 从详情页面快速审批
function quickApproveFromDetail(recordId, memberName) {
    $('#memberDetailModal').modal('hide');
    setTimeout(() => {
        approveApplication(recordId, memberName);
    }, 300);
}

function quickRejectFromDetail(recordId, memberName) {
    $('#memberDetailModal').modal('hide');
    setTimeout(() => {
        rejectApplication(recordId, memberName);
    }, 300);
}

function removeMemberFromDetail(recordId, memberName) {
    $('#memberDetailModal').modal('hide');
    setTimeout(() => {
        removeMember(recordId, memberName);
    }, 300);
}

// 搜索功能增强
$('input[name="search"]').on('input', function() {
    const searchTerm = $(this).val().toLowerCase();
    if (searchTerm.length === 0) {
        $('.table tbody tr').show();
        return;
    }

    $('.table tbody tr').each(function() {
        const text = $(this).text().toLowerCase();
        if (text.includes(searchTerm)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
});

// 批量操作功能
function selectAll() {
    const checkboxes = $('.member-checkbox');
    const selectAllCheckbox = $('#selectAll');
    checkboxes.prop('checked', selectAllCheckbox.prop('checked'));
    updateBatchButtons();
}

function updateBatchButtons() {
    const selectedCount = $('.member-checkbox:checked').length;
    if (selectedCount > 0) {
        $('#batchActions').show();
        $('#selectedCount').text(selectedCount);
    } else {
        $('#batchActions').hide();
    }
}

// 批量批准
function batchApprove() {
    const selected = $('.member-checkbox:checked');
    if (selected.length === 0) {
        showToast('请选择要批准的申请', 'warning');
        return;
    }

    if (confirm(`确定要批准选中的 ${selected.length} 个申请吗？`)) {
        showToast('批量批准功能开发中', 'info');
    }
}

// 批量拒绝
function batchReject() {
    const selected = $('.member-checkbox:checked');
    if (selected.length === 0) {
        showToast('请选择要拒绝的申请', 'warning');
        return;
    }

    if (confirm(`确定要拒绝选中的 ${selected.length} 个申请吗？`)) {
        showToast('批量拒绝功能开发中', 'info');
    }
}
</script>
{% endblock %}
